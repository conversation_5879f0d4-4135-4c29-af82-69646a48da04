### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools
  commentId: T:DrawnUi.Draw.SkiaLabel.ObjectPools
  id: SkiaLabel.ObjectPools
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList
  - DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes
  - DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder
  - DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList
  - DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List{DrawnUi.Draw.LineGlyph})
  - DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder(System.Text.StringBuilder)
  - DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List{DrawnUi.Draw.TextLine})
  langs:
  - csharp
  - vb
  name: SkiaLabel.ObjectPools
  nameWithType: SkiaLabel.ObjectPools
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObjectPools
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Thread-safe object pools for reducing GC allocations in text measurement
  example: []
  syntax:
    content: public static class SkiaLabel.ObjectPools
    content.vb: Public Module SkiaLabel.ObjectPools
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList
  id: GetLineGlyphList
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: GetLineGlyphList()
  nameWithType: SkiaLabel.ObjectPools.GetLineGlyphList()
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetLineGlyphList
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static List<LineGlyph> GetLineGlyphList()
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.LineGlyph}
    content.vb: Public Shared Function GetLineGlyphList() As List(Of LineGlyph)
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList*
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List{DrawnUi.Draw.LineGlyph})
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List{DrawnUi.Draw.LineGlyph})
  id: ReturnLineGlyphList(System.Collections.Generic.List{DrawnUi.Draw.LineGlyph})
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: ReturnLineGlyphList(List<LineGlyph>)
  nameWithType: SkiaLabel.ObjectPools.ReturnLineGlyphList(List<LineGlyph>)
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List<DrawnUi.Draw.LineGlyph>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReturnLineGlyphList
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static void ReturnLineGlyphList(List<LineGlyph> list)
    parameters:
    - id: list
      type: System.Collections.Generic.List{DrawnUi.Draw.LineGlyph}
    content.vb: Public Shared Sub ReturnLineGlyphList(list As List(Of LineGlyph))
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList*
  nameWithType.vb: SkiaLabel.ObjectPools.ReturnLineGlyphList(List(Of LineGlyph))
  fullName.vb: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List(Of DrawnUi.Draw.LineGlyph))
  name.vb: ReturnLineGlyphList(List(Of LineGlyph))
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList
  id: GetTextLineList
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: GetTextLineList()
  nameWithType: SkiaLabel.ObjectPools.GetTextLineList()
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetTextLineList
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 60
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static List<TextLine> GetTextLineList()
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.TextLine}
    content.vb: Public Shared Function GetTextLineList() As List(Of TextLine)
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList*
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List{DrawnUi.Draw.TextLine})
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List{DrawnUi.Draw.TextLine})
  id: ReturnTextLineList(System.Collections.Generic.List{DrawnUi.Draw.TextLine})
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: ReturnTextLineList(List<TextLine>)
  nameWithType: SkiaLabel.ObjectPools.ReturnTextLineList(List<TextLine>)
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List<DrawnUi.Draw.TextLine>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReturnTextLineList
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 71
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static void ReturnTextLineList(List<TextLine> list)
    parameters:
    - id: list
      type: System.Collections.Generic.List{DrawnUi.Draw.TextLine}
    content.vb: Public Shared Sub ReturnTextLineList(list As List(Of TextLine))
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList*
  nameWithType.vb: SkiaLabel.ObjectPools.ReturnTextLineList(List(Of TextLine))
  fullName.vb: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List(Of DrawnUi.Draw.TextLine))
  name.vb: ReturnTextLineList(List(Of TextLine))
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder
  id: GetStringBuilder
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: GetStringBuilder()
  nameWithType: SkiaLabel.ObjectPools.GetStringBuilder()
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetStringBuilder
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 92
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static StringBuilder GetStringBuilder()
    return:
      type: System.Text.StringBuilder
    content.vb: Public Shared Function GetStringBuilder() As StringBuilder
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder*
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder(System.Text.StringBuilder)
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder(System.Text.StringBuilder)
  id: ReturnStringBuilder(System.Text.StringBuilder)
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: ReturnStringBuilder(StringBuilder)
  nameWithType: SkiaLabel.ObjectPools.ReturnStringBuilder(StringBuilder)
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder(System.Text.StringBuilder)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReturnStringBuilder
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 103
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static void ReturnStringBuilder(StringBuilder sb)
    parameters:
    - id: sb
      type: System.Text.StringBuilder
    content.vb: Public Shared Sub ReturnStringBuilder(sb As StringBuilder)
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder*
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes
  commentId: M:DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes
  id: GetPoolSizes
  parent: DrawnUi.Draw.SkiaLabel.ObjectPools
  langs:
  - csharp
  - vb
  name: GetPoolSizes()
  nameWithType: SkiaLabel.ObjectPools.GetPoolSizes()
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetPoolSizes
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs
    startLine: 124
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static (int LineGlyphLists, int TextLineLists, int StringBuilders) GetPoolSizes()
    return:
      type: System.ValueTuple{System.Int32,System.Int32,System.Int32}
    content.vb: Public Shared Function GetPoolSizes() As (LineGlyphLists As Integer, TextLineLists As Integer, StringBuilders As Integer)
  overload: DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_GetLineGlyphList
  name: GetLineGlyphList
  nameWithType: SkiaLabel.ObjectPools.GetLineGlyphList
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList
- uid: System.Collections.Generic.List{DrawnUi.Draw.LineGlyph}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.LineGlyph}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<LineGlyph>
  nameWithType: List<LineGlyph>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.LineGlyph>
  nameWithType.vb: List(Of LineGlyph)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.LineGlyph)
  name.vb: List(Of LineGlyph)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.LineGlyph
    name: LineGlyph
    href: DrawnUi.Draw.LineGlyph.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.LineGlyph
    name: LineGlyph
    href: DrawnUi.Draw.LineGlyph.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnLineGlyphList_System_Collections_Generic_List_DrawnUi_Draw_LineGlyph__
  name: ReturnLineGlyphList
  nameWithType: SkiaLabel.ObjectPools.ReturnLineGlyphList
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_GetTextLineList
  name: GetTextLineList
  nameWithType: SkiaLabel.ObjectPools.GetTextLineList
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList
- uid: System.Collections.Generic.List{DrawnUi.Draw.TextLine}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.TextLine}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<TextLine>
  nameWithType: List<TextLine>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.TextLine>
  nameWithType.vb: List(Of TextLine)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.TextLine)
  name.vb: List(Of TextLine)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.TextLine
    name: TextLine
    href: DrawnUi.Draw.TextLine.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.TextLine
    name: TextLine
    href: DrawnUi.Draw.TextLine.html
  - name: )
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnTextLineList_System_Collections_Generic_List_DrawnUi_Draw_TextLine__
  name: ReturnTextLineList
  nameWithType: SkiaLabel.ObjectPools.ReturnTextLineList
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_GetStringBuilder
  name: GetStringBuilder
  nameWithType: SkiaLabel.ObjectPools.GetStringBuilder
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder
- uid: System.Text.StringBuilder
  commentId: T:System.Text.StringBuilder
  parent: System.Text
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.text.stringbuilder
  name: StringBuilder
  nameWithType: StringBuilder
  fullName: System.Text.StringBuilder
- uid: System.Text
  commentId: N:System.Text
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Text
  nameWithType: System.Text
  fullName: System.Text
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Text
    name: Text
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.text
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Text
    name: Text
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.text
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnStringBuilder_System_Text_StringBuilder_
  name: ReturnStringBuilder
  nameWithType: SkiaLabel.ObjectPools.ReturnStringBuilder
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder
- uid: DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes
  href: DrawnUi.Draw.SkiaLabel.ObjectPools.html#DrawnUi_Draw_SkiaLabel_ObjectPools_GetPoolSizes
  name: GetPoolSizes
  nameWithType: SkiaLabel.ObjectPools.GetPoolSizes
  fullName: DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes
- uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}
  commentId: T:System.ValueTuple{System.Int32,System.Int32,System.Int32}
  parent: System
  definition: System.ValueTuple`3
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: (int LineGlyphLists, int TextLineLists, int StringBuilders)
  nameWithType: (int LineGlyphLists, int TextLineLists, int StringBuilders)
  fullName: (int LineGlyphLists, int TextLineLists, int StringBuilders)
  nameWithType.vb: (LineGlyphLists As Integer, TextLineLists As Integer, StringBuilders As Integer)
  fullName.vb: (LineGlyphLists As Integer, TextLineLists As Integer, StringBuilders As Integer)
  name.vb: (LineGlyphLists As Integer, TextLineLists As Integer, StringBuilders As Integer)
  spec.csharp:
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: " "
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.LineGlyphLists
    name: LineGlyphLists
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.lineglyphlists
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: " "
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.TextLineLists
    name: TextLineLists
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.textlinelists
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: " "
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.StringBuilders
    name: StringBuilders
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.stringbuilders
  - name: )
  spec.vb:
  - name: (
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.LineGlyphLists
    name: LineGlyphLists
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.lineglyphlists
  - name: " "
  - name: As
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.TextLineLists
    name: TextLineLists
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.textlinelists
  - name: " "
  - name: As
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.ValueTuple{System.Int32,System.Int32,System.Int32}.StringBuilders
    name: StringBuilders
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.stringbuilders
  - name: " "
  - name: As
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.ValueTuple`3
  commentId: T:System.ValueTuple`3
  name: (T1, T2, T3)
  nameWithType: (T1, T2, T3)
  fullName: (T1, T2, T3)
  spec.csharp:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: )
  spec.vb:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: )
