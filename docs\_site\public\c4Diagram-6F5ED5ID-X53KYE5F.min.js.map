{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F5ED5ID.mjs"], "sourcesContent": ["import {\n  drawRect,\n  getNoteRect\n} from \"./chunk-ASOPGD6M.mjs\";\nimport {\n  calculateTextHeight,\n  calculateTextWidth,\n  wrapLabel\n} from \"./chunk-7DKRZKHE.mjs\";\nimport {\n  __name,\n  assignWithDepth_default,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/c4/parser/c4Diagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 29:\n          $$[$0].splice(2, 0, \"SYSTEM\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n            break;\n          case 1:\n            return 7;\n            break;\n          case 2:\n            return 8;\n            break;\n          case 3:\n            return 9;\n            break;\n          case 4:\n            return 22;\n            break;\n          case 5:\n            return 23;\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n            break;\n          case 16:\n            break;\n          case 17:\n            return 11;\n            break;\n          case 18:\n            return 15;\n            break;\n          case 19:\n            return 16;\n            break;\n          case 20:\n            return 17;\n            break;\n          case 21:\n            return 18;\n            break;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n            break;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n            break;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n            break;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n            break;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n            break;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n            break;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n            break;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n            break;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n            break;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n            break;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n            break;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n            break;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n            break;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n            break;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n            break;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n            break;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n            break;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n            break;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n            break;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n            break;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n            break;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n            break;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n            break;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n            break;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n            break;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n            break;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n            break;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n            break;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n            break;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n            break;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n            break;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n            break;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n            break;\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            break;\n          case 71:\n            return 80;\n            break;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n            break;\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n            break;\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n            break;\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n            break;\n          case 81:\n            return \"LBRACE\";\n            break;\n          case 82:\n            return \"RBRACE\";\n            break;\n          case 83:\n            return \"SPACE\";\n            break;\n          case 84:\n            return \"EOL\";\n            break;\n          case 85:\n            return 14;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar c4Diagram_default = parser;\n\n// src/diagrams/c4/c4Db.js\nvar c4ShapeArray = [];\nvar boundaryParseStack = [\"\"];\nvar currentBoundaryParse = \"global\";\nvar parentBoundaryParse = \"\";\nvar boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nvar rels = [];\nvar title = \"\";\nvar wrapEnabled = false;\nvar c4ShapeInRow = 4;\nvar c4BoundaryInRow = 2;\nvar c4Type;\nvar getC4Type = /* @__PURE__ */ __name(function() {\n  return c4Type;\n}, \"getC4Type\");\nvar setC4Type = /* @__PURE__ */ __name(function(c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n}, \"setC4Type\");\nvar addRel = /* @__PURE__ */ __name(function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n}, \"addRel\");\nvar addPersonOrSystem = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n}, \"addPersonOrSystem\");\nvar addContainer = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n}, \"addContainer\");\nvar addComponent = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n}, \"addComponent\");\nvar addPersonOrSystemBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addPersonOrSystemBoundary\");\nvar addContainerBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addContainerBoundary\");\nvar addDeploymentNode = /* @__PURE__ */ __name(function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addDeploymentNode\");\nvar popBoundaryParseStack = /* @__PURE__ */ __name(function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"popBoundaryParseStack\");\nvar updateElStyle = /* @__PURE__ */ __name(function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n}, \"updateElStyle\");\nvar updateRelStyle = /* @__PURE__ */ __name(function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n}, \"updateRelStyle\");\nvar updateLayoutConfig = /* @__PURE__ */ __name(function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n}, \"updateLayoutConfig\");\nvar getC4ShapeInRow = /* @__PURE__ */ __name(function() {\n  return c4ShapeInRow;\n}, \"getC4ShapeInRow\");\nvar getC4BoundaryInRow = /* @__PURE__ */ __name(function() {\n  return c4BoundaryInRow;\n}, \"getC4BoundaryInRow\");\nvar getCurrentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return currentBoundaryParse;\n}, \"getCurrentBoundaryParse\");\nvar getParentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return parentBoundaryParse;\n}, \"getParentBoundaryParse\");\nvar getC4ShapeArray = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n}, \"getC4ShapeArray\");\nvar getC4Shape = /* @__PURE__ */ __name(function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n}, \"getC4Shape\");\nvar getC4ShapeKeys = /* @__PURE__ */ __name(function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n}, \"getC4ShapeKeys\");\nvar getBoundaries = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n}, \"getBoundaries\");\nvar getBoundarys = getBoundaries;\nvar getRels = /* @__PURE__ */ __name(function() {\n  return rels;\n}, \"getRels\");\nvar getTitle = /* @__PURE__ */ __name(function() {\n  return title;\n}, \"getTitle\");\nvar setWrap = /* @__PURE__ */ __name(function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n}, \"setWrap\");\nvar autoWrap = /* @__PURE__ */ __name(function() {\n  return wrapEnabled;\n}, \"autoWrap\");\nvar clear = /* @__PURE__ */ __name(function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n}, \"clear\");\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar setTitle = /* @__PURE__ */ __name(function(txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n}, \"setTitle\");\nvar c4Db_default = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().c4, \"getConfig\"),\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\n\n// src/diagrams/c4/c4Renderer.js\nimport { select } from \"d3\";\n\n// src/diagrams/c4/svgDraw.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawImage = /* @__PURE__ */ __name(function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : sanitizeUrl(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawRels = /* @__PURE__ */ __name((elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n}, \"drawRels\");\nvar drawBoundary = /* @__PURE__ */ __name(function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect2(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n}, \"drawBoundary\");\nvar drawC4Shape = /* @__PURE__ */ __name(function(elem, c4Shape, conf2) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = getNoteRect();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect2(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && c4Shape.techn?.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n}, \"drawC4Shape\");\nvar insertDatabaseIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowEnd = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n}, \"insertArrowEnd\");\nvar insertArrowFilledHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertDynamicNumber = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertDynamicNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ __name(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n}, \"insertArrowCrossHead\");\nvar getC4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"getC4ShapeFont\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\n\n// src/diagrams/c4/c4Renderer.js\nvar globalBoundaryMaxX = 0;\nvar globalBoundaryMaxY = 0;\nvar c4ShapeInRow2 = 4;\nvar c4BoundaryInRow2 = 2;\nparser.yy = c4Db_default;\nvar conf = {};\nvar Bounds = class {\n  static {\n    __name(this, \"Bounds\");\n  }\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow2) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n};\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  assignWithDepth_default(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar c4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"c4ShapeFont\");\nvar boundaryFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n}, \"boundaryFont\");\nvar messageFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common_default.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common_default.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\n__name(calcC4ShapeTextWH, \"calcC4ShapeTextWH\");\nvar drawBoundary2 = /* @__PURE__ */ __name(function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw_default.drawBoundary(diagram2, boundary, conf);\n}, \"drawBoundary\");\nvar drawC4ShapeArray = /* @__PURE__ */ __name(function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      \"\\xAB\" + c4Shape.typeC4Shape.text + \"\\xBB\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw_default.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n}, \"drawC4ShapeArray\");\nvar Point = class {\n  static {\n    __name(this, \"Point\");\n  }\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n};\nvar getIntersectPoint = /* @__PURE__ */ __name(function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n}, \"getIntersectPoint\");\nvar getIntersectPoints = /* @__PURE__ */ __name(function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n}, \"getIntersectPoints\");\nvar drawRels2 = /* @__PURE__ */ __name(function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw_default.drawRels(diagram2, rels2, conf);\n}, \"drawRels\");\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow2, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n    if (i == 0 || i % c4BoundaryInRow2 === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary2(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n__name(drawInsideBoundary, \"drawInsideBoundary\");\nvar draw = /* @__PURE__ */ __name(function(_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  let db = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow2 = db.getC4ShapeInRow();\n  c4BoundaryInRow2 = db.getC4BoundaryInRow();\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowEnd(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  drawRels2(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, box);\n}, \"draw\");\nvar c4Renderer_default = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary: drawBoundary2,\n  setConf,\n  draw\n};\n\n// src/diagrams/c4/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/c4/c4Diagram.ts\nvar diagram = {\n  parser: c4Diagram_default,\n  db: c4Db_default,\n  renderer: c4Renderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name(({ c4, wrap }) => {\n    c4Renderer_default.setConf(c4);\n    c4Db_default.setWrap(wrap);\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": "iXAgrDA,IAAAA,GAA4B,WAxpDxBC,GAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAASC,GAAGC,EAAGC,EAAIC,EAAG,CACnD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,GAAE,OAAQG,IAAKD,EAAGF,GAAEG,CAAC,CAAC,EAAIF,EAAG,CACrD,OAAOC,CACT,EAAG,GAAG,EAAGE,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,EAAE,EAAGC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAC3yCC,GAAU,CACZ,MAAuB5D,EAAO,UAAiB,CAC/C,EAAG,OAAO,EACV,GAAI,CAAC,EACL,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,WAAc,EAAG,UAAa,EAAG,aAAgB,EAAG,aAAgB,EAAG,aAAgB,EAAG,aAAgB,EAAG,YAAe,GAAI,WAAc,GAAI,QAAW,GAAI,WAAc,GAAI,IAAO,GAAI,aAAgB,GAAI,aAAgB,GAAI,WAAc,GAAI,cAAiB,GAAI,gBAAmB,GAAI,kBAAqB,GAAI,eAAkB,GAAI,MAAS,GAAI,eAAkB,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,kBAAqB,GAAI,uBAA0B,GAAI,sBAAyB,GAAI,cAAiB,GAAI,OAAU,GAAI,oBAAuB,GAAI,WAAc,GAAI,gBAAmB,GAAI,SAAY,GAAI,mBAAsB,GAAI,KAAQ,GAAI,OAAU,GAAI,OAAU,GAAI,OAAU,GAAI,iBAAoB,GAAI,OAAU,GAAI,WAAc,GAAI,OAAU,GAAI,UAAa,GAAI,aAAgB,GAAI,WAAc,GAAI,cAAiB,GAAI,iBAAoB,GAAI,UAAa,GAAI,aAAgB,GAAI,gBAAmB,GAAI,cAAiB,GAAI,iBAAoB,GAAI,oBAAuB,GAAI,UAAa,GAAI,aAAgB,GAAI,gBAAmB,GAAI,cAAiB,GAAI,iBAAoB,GAAI,oBAAuB,GAAI,IAAO,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,UAAa,GAAI,gBAAmB,GAAI,iBAAoB,GAAI,qBAAwB,GAAI,UAAa,GAAI,IAAO,GAAI,QAAW,GAAI,UAAa,GAAI,UAAa,GAAI,gBAAmB,GAAI,QAAW,EAAG,KAAQ,CAAE,EACzgD,WAAY,CAAE,EAAG,QAAS,EAAG,eAAgB,EAAG,eAAgB,EAAG,eAAgB,EAAG,eAAgB,GAAI,aAAc,GAAI,UAAW,GAAI,MAAO,GAAI,eAAgB,GAAI,eAAgB,GAAI,aAAc,GAAI,gBAAiB,GAAI,QAAS,GAAI,iBAAkB,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,SAAU,GAAI,sBAAuB,GAAI,kBAAmB,GAAI,WAAY,GAAI,qBAAsB,GAAI,OAAQ,GAAI,SAAU,GAAI,SAAU,GAAI,SAAU,GAAI,SAAU,GAAI,aAAc,GAAI,SAAU,GAAI,YAAa,GAAI,eAAgB,GAAI,aAAc,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,YAAa,GAAI,eAAgB,GAAI,kBAAmB,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,sBAAuB,GAAI,YAAa,GAAI,eAAgB,GAAI,kBAAmB,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,sBAAuB,GAAI,MAAO,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,YAAa,GAAI,kBAAmB,GAAI,mBAAoB,GAAI,uBAAwB,GAAI,MAAO,GAAI,UAAW,GAAI,YAAa,GAAI,YAAa,GAAI,iBAAkB,EACtrC,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,CAAC,EACrrB,cAA+BA,EAAO,SAAmB6D,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,GAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACf,IAAK,GACHD,EAAG,aAAa,IAAI,EACpB,MACF,IAAK,GACHA,EAAG,aAAa,IAAI,EACpB,MACF,IAAK,GACHA,EAAG,aAAa,IAAI,EACpB,MACF,IAAK,GACHA,EAAG,aAAa,IAAI,EACpB,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACHA,EAAG,UAAUE,EAAGE,EAAK,CAAC,CAAC,EACvB,MACF,IAAK,IACHJ,EAAG,SAASE,EAAGE,CAAE,EAAE,UAAU,CAAC,CAAC,EAC/B,KAAK,EAAIF,EAAGE,CAAE,EAAE,UAAU,CAAC,EAC3B,MACF,IAAK,IACHJ,EAAG,kBAAkBE,EAAGE,CAAE,EAAE,UAAU,EAAE,CAAC,EACzC,KAAK,EAAIF,EAAGE,CAAE,EAAE,UAAU,EAAE,EAC5B,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAK,EACrBJ,EAAG,SAAS,KAAK,CAAC,EAClB,MACF,IAAK,IACL,IAAK,IACH,KAAK,EAAIE,EAAGE,CAAE,EAAE,KAAK,EACrBJ,EAAG,kBAAkB,KAAK,CAAC,EAC3B,MACF,IAAK,IACHE,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,YAAY,EAChCJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHF,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,QAAQ,EAC5BJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHF,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,WAAW,EAC/BJ,EAAG,qBAAqB,GAAGE,EAAGE,CAAE,CAAC,EACjC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,OAAQ,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,QAAS,GAAGE,EAAGE,CAAE,CAAC,EACvC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,QAAS,GAAGE,EAAGE,CAAE,CAAC,EACvC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,sBAAsB,EACzB,MACF,IAAK,IACHA,EAAG,kBAAkB,SAAU,GAAGE,EAAGE,CAAE,CAAC,EACxC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EACjD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,SAAU,GAAGE,EAAGE,CAAE,CAAC,EACxC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,YAAa,GAAGE,EAAGE,CAAE,CAAC,EAC3C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EAC9C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EACjD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EACpD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,kBAAkB,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EACvD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,YAAa,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EACzC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAC5C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EAC/C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EAClD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,2BAA4B,GAAGE,EAAGE,CAAE,CAAC,EACrD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,YAAa,GAAGE,EAAGE,CAAE,CAAC,EACtC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EACzC,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAC5C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EAC/C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EAClD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,aAAa,2BAA4B,GAAGE,EAAGE,CAAE,CAAC,EACrD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,MAAO,GAAGE,EAAGE,CAAE,CAAC,EAC1B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAC5B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHF,EAAGE,CAAE,EAAE,OAAO,EAAG,CAAC,EAClBJ,EAAG,OAAO,MAAO,GAAGE,EAAGE,CAAE,CAAC,EAC1B,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,cAAc,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAC7C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,eAAe,mBAAoB,GAAGE,EAAGE,CAAE,CAAC,EAC/C,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACHJ,EAAG,mBAAmB,uBAAwB,GAAGE,EAAGE,CAAE,CAAC,EACvD,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EAChB,MACF,IAAK,IACHF,EAAGE,CAAE,EAAE,QAAQF,EAAGE,EAAK,CAAC,CAAC,EACzB,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACL,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAK,EACrB,MACF,IAAK,IACH,IAAIC,GAAK,CAAC,EACVA,GAAGH,EAAGE,EAAK,CAAC,EAAE,KAAK,CAAC,EAAIF,EAAGE,CAAE,EAAE,KAAK,EACpC,KAAK,EAAIC,GACT,MACF,IAAK,IACH,KAAK,EAAI,GACT,KACJ,CACF,EAAG,WAAW,EACd,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,GAAI,EAAG,GAAI,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,EAAG,CAAC,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIhE,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGhD,EAAEiD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAItC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,CAAC,EAAGhD,EAAEiD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGjD,EAAEkD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,CAAC,EAAGlD,EAAEiD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,CAAC,EAAGjD,EAAEmD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGnD,EAAEmD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGnD,EAAEmD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAGvD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIJ,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI5C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,EAAG,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAII,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAGvD,EAAEiD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGjD,EAAEkD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,IAAK,GAAI5C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,CAAC,EAAGV,EAAEiD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,GAAI3C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,EAAI,CAAC,EAAGhD,EAAEmD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGnD,EAAEmD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGnD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEyD,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,IAAK,GAAIL,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,CAAC,EAAGvD,EAAE0D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG1D,EAAE0D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG1D,EAAE0D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG1D,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,IAAK,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,EAAGxD,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG3D,EAAE2D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,EAAE,CAAE,EAAG3D,EAAEkD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGlD,EAAEiD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGjD,EAAEyD,GAAK,CAAC,EAAG,EAAE,CAAC,EAAGzD,EAAE0D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG1D,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAEwD,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGxD,EAAE4D,GAAK,CAAC,EAAG,EAAE,CAAC,EAAG5D,EAAE4D,GAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,CAAC,EAAG,GAAG,CAAE,CAAC,EAAG5D,EAAE4D,GAAK,CAAC,EAAG,EAAE,CAAC,CAAC,EACz5O,eAAgB,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,CAAC,EAAG,IAAK,CAAC,EAAG,CAAC,EAAG,IAAK,CAAC,EAAG,EAAE,EAAG,IAAK,CAAC,EAAG,EAAE,EAAG,IAAK,CAAC,EAAG,EAAE,CAAE,EACjK,WAA4B3D,EAAO,SAAoBsE,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACR,CACF,EAAG,YAAY,EACf,MAAuBxE,EAAO,SAAeyE,EAAO,CAClD,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,GAAQ,KAAK,MAAOlB,EAAS,GAAIE,GAAW,EAAGD,GAAS,EAAGkB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,GAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASpF,MAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IACjDoF,GAAY,GAAGpF,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGjCmF,EAAO,SAASX,EAAOY,GAAY,EAAE,EACrCA,GAAY,GAAG,MAAQD,EACvBC,GAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,CAAC,GAEnB,IAAIE,GAAQF,EAAO,OACnBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,GAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,GAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASG,GAASC,EAAG,CACnBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CAClC,CACAzF,EAAOwF,GAAU,UAAU,EAC3B,SAASE,IAAM,CACb,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAO,IAAI,GAAKF,GACpC,OAAOS,GAAU,WACfA,aAAiB,QACnBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAErBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE3BA,CACT,CACA3F,EAAO0F,GAAK,KAAK,EAEjB,QADIE,EAAQC,GAAgBC,GAAOC,EAAQC,GAAGC,GAAGC,GAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUX,GATAR,GAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,EAAK,EAC3BC,EAAS,KAAK,eAAeD,EAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,GAAI,GAEfK,EAAShB,GAAMe,EAAK,GAAKf,GAAMe,EAAK,EAAEF,CAAM,GAE1C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,GAAMe,EAAK,EACf,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC5BqB,GAAS,KAAK,IAAM,KAAK,WAAWH,EAAC,EAAI,GAAG,EAG5Cf,EAAO,aACTmB,GAAS,wBAA0BxC,GAAW,GAAK;AAAA,EAAQqB,EAAO,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BxC,GAAW,GAAK,iBAAmB6B,GAAUV,GAAM,eAAiB,KAAO,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWW,GAAQ,CACtB,KAAMnB,EAAO,MACb,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAO,SACb,IAAKE,GACL,SAAAgB,EACF,CAAC,CACH,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,GAAQ,YAAcF,CAAM,EAEpG,OAAQG,EAAO,CAAC,EAAG,CACjB,IAAK,GACHpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAO,MAAM,EACzBN,EAAO,KAAKM,EAAO,MAAM,EACzBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASHD,EAASC,GACTA,GAAiB,OATjB/B,GAASsB,EAAO,OAChBvB,EAASuB,EAAO,OAChBrB,GAAWqB,EAAO,SAClBE,GAAQF,EAAO,OACXJ,GAAa,GACfA,MAMJ,MACF,IAAK,GAwBH,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,GAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,GAAM,GAAK,CACT,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACzC,EACIS,KACFW,GAAM,GAAG,MAAQ,CACfpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACnC,GAEFmB,GAAI,KAAK,cAAc,MAAMC,GAAO,CAClCrC,EACAC,GACAC,GACAsB,GAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACF,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACf,OAAOA,GAELG,IACFzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAEnCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,GAAM,CAAC,EACnBpB,EAAO,KAAKoB,GAAM,EAAE,EACpBG,GAAWtB,GAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACX,CACF,CACA,MAAO,EACT,EAAG,OAAO,CACZ,EACIG,GAAwB,UAAW,CACrC,IAAIpB,GAAS,CACX,IAAK,EACL,WAA4BpF,EAAO,SAAoBsE,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEvB,EAAG,YAAY,EAEf,SAA0BtE,EAAO,SAASyE,EAAOT,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASS,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACf,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACT,EAAG,UAAU,EAEb,MAAuBzE,EAAO,UAAW,CACvC,IAAIyG,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACT,EAAG,OAAO,EAEV,MAAuBzG,EAAO,SAASyG,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIT,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaS,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAC1L,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACT,EAAG,OAAO,EAEV,KAAsBpG,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACT,EAAG,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,EAEH,OAAO,IACT,EAAG,QAAQ,EAEX,KAAsBA,EAAO,SAASyF,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAChC,EAAG,MAAM,EAET,UAA2BzF,EAAO,UAAW,CAC3C,IAAI4G,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAAG,WAAW,EAEd,cAA+B5G,EAAO,UAAW,CAC/C,IAAI6G,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CACjF,EAAG,eAAe,EAElB,aAA8B7G,EAAO,UAAW,CAC9C,IAAI8G,EAAM,KAAK,UAAU,EACrBC,EAAK,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC3C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAK,GAClD,EAAG,cAAc,EAEjB,WAA4B/G,EAAO,SAASgH,EAAOC,EAAc,CAC/D,IAAItB,EAAOe,EAAOQ,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC3B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACb,EACI,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MAC/I,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBrB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMsB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVtB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAAS1F,KAAKiH,EACZ,KAAKjH,CAAC,EAAIiH,EAAOjH,CAAC,EAEpB,MAAO,EACT,CACA,MAAO,EACT,EAAG,YAAY,EAEf,KAAsBD,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAI2F,EAAOqB,EAAOG,EAAWC,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAEhC,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADA3B,EAAQ,KAAK,WAAWwB,EAAWE,EAAMC,CAAC,CAAC,EACvC3B,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1BqB,EAAQ,GACR,QACF,KACE,OAAO,EAEX,SAAW,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFrB,EAAQ,KAAK,WAAWqB,EAAOK,EAAMD,CAAK,CAAC,EACvCzB,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,CAEL,EAAG,MAAM,EAET,IAAqB3F,EAAO,UAAe,CACzC,IAAIiG,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGK,KAAK,IAAI,CAEpB,EAAG,KAAK,EAER,MAAuBjG,EAAO,SAAeuH,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACpC,EAAG,OAAO,EAEV,SAA0BvH,EAAO,UAAoB,CACnD,IAAIyF,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEhC,EAAG,UAAU,EAEb,cAA+BzF,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAEtC,EAAG,eAAe,EAElB,SAA0BA,EAAO,SAAkByF,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEX,EAAG,UAAU,EAEb,UAA2BzF,EAAO,SAAmBuH,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACtB,EAAG,WAAW,EAEd,eAAgCvH,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC7B,EAAG,gBAAgB,EACnB,QAAS,CAAC,EACV,cAA+BA,EAAO,SAAmBgE,EAAIwD,EAAKC,EAA2BC,EAAU,CACrG,IAAIC,EAAUD,EACd,OAAQD,EAA2B,CACjC,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,GACH,YAAK,SAAS,EACP,kBACP,MACF,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,GACH,YAAK,SAAS,EACP,kBACP,MACF,IAAK,IACH,KAAK,MAAM,qBAAqB,EAChC,MACF,IAAK,IACH,KAAK,SAAS,EACd,MACF,IAAK,IACH,MAAO,4BAET,IAAK,IACH,MACF,IAAK,IACH,EACA,MACF,IAAK,IACH,MAAO,IAET,IAAK,IACH,MACF,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,YAAK,MAAM,YAAY,EAChB,GACP,MACF,IAAK,IACH,YAAK,MAAM,QAAQ,EACZ,GACP,MACF,IAAK,IACH,YAAK,MAAM,kBAAkB,EACtB,GACP,MACF,IAAK,IACH,YAAK,MAAM,eAAe,EACnB,GACP,MACF,IAAK,IACH,YAAK,MAAM,YAAY,EAChB,GACP,MACF,IAAK,IACH,YAAK,MAAM,cAAc,EAClB,GACP,MACF,IAAK,IACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,IACH,YAAK,MAAM,QAAQ,EACZ,GACP,MACF,IAAK,IACH,YAAK,MAAM,UAAU,EACd,GACP,MACF,IAAK,IACH,YAAK,MAAM,qBAAqB,EACzB,GACP,MACF,IAAK,IACH,YAAK,MAAM,iBAAiB,EACrB,GACP,MACF,IAAK,IACH,YAAK,MAAM,qBAAqB,EACzB,GACP,MACF,IAAK,IACH,YAAK,MAAM,kBAAkB,EACtB,GACP,MACF,IAAK,IACH,YAAK,MAAM,eAAe,EACnB,GACP,MACF,IAAK,IACH,YAAK,MAAM,iBAAiB,EACrB,GACP,MACF,IAAK,IACH,YAAK,MAAM,cAAc,EAClB,GACP,MACF,IAAK,IACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,IACH,YAAK,MAAM,oBAAoB,EACxB,GACP,MACF,IAAK,IACH,YAAK,MAAM,qBAAqB,EACzB,GACP,MACF,IAAK,IACH,YAAK,MAAM,kBAAkB,EACtB,GACP,MACF,IAAK,IACH,YAAK,MAAM,eAAe,EACnB,GACP,MACF,IAAK,IACH,YAAK,MAAM,iBAAiB,EACrB,GACP,MACF,IAAK,IACH,YAAK,MAAM,cAAc,EAClB,GACP,MACF,IAAK,IACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,IACH,YAAK,MAAM,MAAM,EACV,GACP,MACF,IAAK,IACH,YAAK,MAAM,MAAM,EACV,GACP,MACF,IAAK,IACH,YAAK,MAAM,QAAQ,EACZ,GACP,MACF,IAAK,IACH,YAAK,MAAM,QAAQ,EACZ,GACP,MACF,IAAK,IACH,YAAK,MAAM,KAAK,EACT,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,OAAO,EACX,GACP,MACF,IAAK,IACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,IACH,YAAK,MAAM,iBAAiB,EACrB,GACP,MACF,IAAK,IACH,YAAK,MAAM,kBAAkB,EACtB,GACP,MACF,IAAK,IACH,YAAK,MAAM,sBAAsB,EAC1B,GACP,MACF,IAAK,IACH,MAAO,gBAET,IAAK,IACH,YAAK,MAAM,WAAW,EACf,kBACP,MACF,IAAK,IACH,KAAK,MAAM,WAAW,EACtB,MACF,IAAK,IACH,KAAK,SAAS,EACd,KAAK,SAAS,EACd,MACF,IAAK,IACH,MAAO,IAET,IAAK,IACH,MACF,IAAK,IACH,MAAO,IAET,IAAK,IACH,KAAK,MAAM,QAAQ,EACnB,MACF,IAAK,IACH,KAAK,SAAS,EACd,MACF,IAAK,IACH,MAAO,MAET,IAAK,IACH,KAAK,MAAM,WAAW,EACtB,MACF,IAAK,IACH,YAAK,MAAM,eAAe,EACnB,UACP,MACF,IAAK,IACH,KAAK,SAAS,EACd,KAAK,MAAM,iBAAiB,EAC5B,MACF,IAAK,IACH,MAAO,YAET,IAAK,IACH,KAAK,SAAS,EACd,KAAK,SAAS,EACd,MACF,IAAK,IACH,MAAO,MAET,IAAK,IACH,MAAO,SAET,IAAK,IACH,MAAO,SAET,IAAK,IACH,MAAO,QAET,IAAK,IACH,MAAO,MAET,IAAK,IACH,MAAO,GAEX,CACF,EAAG,WAAW,EACd,MAAO,CAAC,8BAA+B,8BAA+B,8BAA+B,8BAA+B,uBAAwB,gCAAiC,uBAAwB,uBAAwB,uBAAwB,uBAAwB,wBAAyB,YAAa,cAAe,gCAAiC,wBAAyB,mBAAoB,WAAY,mBAAoB,qBAAsB,qBAAsB,mBAAoB,sBAAuB,oBAAqB,gBAAiB,yBAA0B,sBAAuB,oBAAqB,qBAAsB,kBAAmB,gBAAiB,kBAAmB,6BAA8B,yBAA0B,4BAA6B,yBAA0B,uBAAwB,wBAAyB,qBAAsB,mBAAoB,4BAA6B,4BAA6B,yBAA0B,uBAAwB,wBAAyB,qBAAsB,mBAAoB,yBAA0B,cAAe,gBAAiB,gBAAiB,aAAc,eAAgB,gBAAiB,eAAgB,kBAAmB,eAAgB,kBAAmB,eAAgB,mBAAoB,eAAgB,kBAAmB,kBAAmB,4BAA6B,wBAAyB,4BAA6B,SAAU,kBAAmB,WAAY,WAAY,UAAW,SAAU,kBAAmB,eAAgB,WAAY,aAAc,gBAAiB,aAAc,kBAAmB,aAAc,WAAY,aAAc,UAAW,UAAW,aAAc,eAAgB,QAAQ,EACntD,WAAY,CAAE,oBAAuB,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAM,EAAG,gBAAmB,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,cAAiB,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,qBAAwB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,EAAG,UAAa,EAAM,EAAG,IAAO,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,KAAQ,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,oBAAuB,CAAE,MAAS,CAAC,EAAG,UAAa,EAAM,EAAG,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,mBAAsB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,oBAAuB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,oBAAuB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,SAAY,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,WAAc,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,WAAc,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAK,CAAE,CAC3yG,EACA,OAAOrC,EACT,EAAE,EACFxB,GAAQ,MAAQ4C,GAChB,SAASoB,IAAS,CAChB,KAAK,GAAK,CAAC,CACb,CACA,OAAA5H,EAAO4H,GAAQ,QAAQ,EACvBA,GAAO,UAAYhE,GACnBA,GAAQ,OAASgE,GACV,IAAIA,EACb,EAAE,EACF9H,GAAO,OAASA,GAChB,IAAI+H,GAAoB/H,GAGpBgI,EAAe,CAAC,EAChBC,GAAqB,CAAC,EAAE,EACxBC,EAAuB,SACvBC,EAAsB,GACtBC,EAAa,CACf,CACE,MAAO,SACP,MAAO,CAAE,KAAM,QAAS,EACxB,KAAM,CAAE,KAAM,QAAS,EACvB,KAAM,KACN,KAAM,KACN,eAAgB,EAClB,CACF,EACIC,GAAO,CAAC,EACRC,GAAQ,GACRC,GAAc,GACdC,GAAe,EACfC,GAAkB,EAClBC,GACAC,GAA4BzI,EAAO,UAAW,CAChD,OAAOwI,EACT,EAAG,WAAW,EACVE,GAA4B1I,EAAO,SAAS2I,EAAa,CAE3DH,GADoBI,GAAaD,EAAaE,GAAU,CAAC,CAE3D,EAAG,WAAW,EACVC,GAAyB9I,EAAO,SAAS+I,EAAMC,EAAMC,EAAIC,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CACpG,GAAuBR,GAAS,MAAQC,IAAS,QAAUA,IAAS,MAAQC,IAAO,QAAUA,IAAO,MAAQC,IAAU,QAAUA,IAAU,KACxI,OAEF,IAAIM,EAAM,CAAC,EACLC,EAAMtB,GAAK,KAAMuB,GAASA,EAAK,OAASV,GAAQU,EAAK,KAAOT,CAAE,EAUpE,GATIQ,EACFD,EAAMC,EAENtB,GAAK,KAAKqB,CAAG,EAEfA,EAAI,KAAOT,EACXS,EAAI,KAAOR,EACXQ,EAAI,GAAKP,EACTO,EAAI,MAAQ,CAAE,KAAMN,CAAM,EACFC,GAAU,KAChCK,EAAI,MAAQ,CAAE,KAAM,EAAG,UAEnB,OAAOL,GAAU,SAAU,CAC7B,GAAI,CAACQ,EAAKC,CAAK,EAAI,OAAO,QAAQT,CAAK,EAAE,CAAC,EAC1CK,EAAIG,CAAG,EAAI,CAAE,KAAMC,CAAM,CAC3B,MACEJ,EAAI,MAAQ,CAAE,KAAML,CAAM,EAG9B,GAAwBC,GAAU,KAChCI,EAAI,MAAQ,CAAE,KAAM,EAAG,UAEnB,OAAOJ,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CI,EAAIG,CAAG,EAAI,CAAE,KAAMC,CAAM,CAC3B,MACEJ,EAAI,MAAQ,CAAE,KAAMJ,CAAM,EAG9B,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAM,EAAE,CAAC,EAC3CG,EAAIG,CAAG,EAAIC,CACb,MACEJ,EAAI,OAASH,EAEf,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCE,EAAIG,CAAG,EAAIC,CACb,MACEJ,EAAI,KAAOF,EAEb,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCC,EAAIG,CAAG,EAAIC,CACb,MACEJ,EAAI,KAAOD,EAEbC,EAAI,KAAOK,GAAS,CACtB,EAAG,QAAQ,EACPC,GAAoC9J,EAAO,SAAS+J,EAAaC,EAAOd,EAAOE,EAAOC,EAAQC,EAAMC,EAAM,CAC5G,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIe,EAAiB,CAAC,EAChBR,EAAM3B,EAAa,KAAMoC,GAAoBA,EAAgB,QAAUF,CAAK,EAYlF,GAXIP,GAAOO,IAAUP,EAAI,MACvBQ,EAAiBR,GAEjBQ,EAAe,MAAQD,EACvBlC,EAAa,KAAKmC,CAAc,GAEVf,GAAU,KAChCe,EAAe,MAAQ,CAAE,KAAM,EAAG,EAElCA,EAAe,MAAQ,CAAE,KAAMf,CAAM,EAEfE,GAAU,KAChCa,EAAe,MAAQ,CAAE,KAAM,EAAG,UAE9B,OAAOb,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1Ca,EAAeN,CAAG,EAAI,CAAE,KAAMC,CAAM,CACtC,MACEK,EAAe,MAAQ,CAAE,KAAMb,CAAM,EAGzC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAM,EAAE,CAAC,EAC3CY,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,OAASZ,EAE1B,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCW,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,KAAOX,EAExB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCU,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,KAAOV,EAExBU,EAAe,YAAc,CAAE,KAAMF,CAAY,EACjDE,EAAe,eAAiBjC,EAChCiC,EAAe,KAAOJ,GAAS,CACjC,EAAG,mBAAmB,EAClBM,GAA+BnK,EAAO,SAAS+J,EAAaC,EAAOd,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CAC9G,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIkB,EAAY,CAAC,EACXX,EAAM3B,EAAa,KAAMuC,GAAeA,EAAW,QAAUL,CAAK,EAYxE,GAXIP,GAAOO,IAAUP,EAAI,MACvBW,EAAYX,GAEZW,EAAU,MAAQJ,EAClBlC,EAAa,KAAKsC,CAAS,GAELlB,GAAU,KAChCkB,EAAU,MAAQ,CAAE,KAAM,EAAG,EAE7BA,EAAU,MAAQ,CAAE,KAAMlB,CAAM,EAEVC,GAAU,KAChCiB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOjB,GAAU,SAAU,CAC7B,GAAI,CAACQ,EAAKC,CAAK,EAAI,OAAO,QAAQT,CAAK,EAAE,CAAC,EAC1CiB,EAAUT,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEQ,EAAU,MAAQ,CAAE,KAAMjB,CAAM,EAGpC,GAAwBC,GAAU,KAChCgB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOhB,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CgB,EAAUT,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEQ,EAAU,MAAQ,CAAE,KAAMhB,CAAM,EAGpC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAM,EAAE,CAAC,EAC3Ce,EAAUT,CAAG,EAAIC,CACnB,MACEQ,EAAU,OAASf,EAErB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCc,EAAUT,CAAG,EAAIC,CACnB,MACEQ,EAAU,KAAOd,EAEnB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCa,EAAUT,CAAG,EAAIC,CACnB,MACEQ,EAAU,KAAOb,EAEnBa,EAAU,KAAOP,GAAS,EAC1BO,EAAU,YAAc,CAAE,KAAML,CAAY,EAC5CK,EAAU,eAAiBpC,CAC7B,EAAG,cAAc,EACbsC,GAA+BtK,EAAO,SAAS+J,EAAaC,EAAOd,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CAC9G,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIqB,EAAY,CAAC,EACXd,EAAM3B,EAAa,KAAM0C,GAAeA,EAAW,QAAUR,CAAK,EAYxE,GAXIP,GAAOO,IAAUP,EAAI,MACvBc,EAAYd,GAEZc,EAAU,MAAQP,EAClBlC,EAAa,KAAKyC,CAAS,GAELrB,GAAU,KAChCqB,EAAU,MAAQ,CAAE,KAAM,EAAG,EAE7BA,EAAU,MAAQ,CAAE,KAAMrB,CAAM,EAEVC,GAAU,KAChCoB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOpB,GAAU,SAAU,CAC7B,GAAI,CAACQ,EAAKC,CAAK,EAAI,OAAO,QAAQT,CAAK,EAAE,CAAC,EAC1CoB,EAAUZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEW,EAAU,MAAQ,CAAE,KAAMpB,CAAM,EAGpC,GAAwBC,GAAU,KAChCmB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOnB,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CmB,EAAUZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEW,EAAU,MAAQ,CAAE,KAAMnB,CAAM,EAGpC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAM,EAAE,CAAC,EAC3CkB,EAAUZ,CAAG,EAAIC,CACnB,MACEW,EAAU,OAASlB,EAErB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCiB,EAAUZ,CAAG,EAAIC,CACnB,MACEW,EAAU,KAAOjB,EAEnB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCgB,EAAUZ,CAAG,EAAIC,CACnB,MACEW,EAAU,KAAOhB,EAEnBgB,EAAU,KAAOV,GAAS,EAC1BU,EAAU,YAAc,CAAE,KAAMR,CAAY,EAC5CQ,EAAU,eAAiBvC,CAC7B,EAAG,cAAc,EACbyC,GAA4CzK,EAAO,SAASgK,EAAOd,EAAOH,EAAMO,EAAMC,EAAM,CAC9F,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIwB,EAAW,CAAC,EACVjB,EAAMvB,EAAW,KAAMyC,GAAcA,EAAU,QAAUX,CAAK,EAYpE,GAXIP,GAAOO,IAAUP,EAAI,MACvBiB,EAAWjB,GAEXiB,EAAS,MAAQV,EACjB9B,EAAW,KAAKwC,CAAQ,GAEFxB,GAAU,KAChCwB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMxB,CAAM,EAEVH,GAAS,KAC9B2B,EAAS,KAAO,CAAE,KAAM,QAAS,UAE7B,OAAO3B,GAAS,SAAU,CAC5B,GAAI,CAACY,EAAKC,CAAK,EAAI,OAAO,QAAQb,CAAI,EAAE,CAAC,EACzC2B,EAASf,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEc,EAAS,KAAO,CAAE,KAAM3B,CAAK,EAGjC,GAAI,OAAOO,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCoB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOpB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCmB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOnB,EAElBmB,EAAS,eAAiB1C,EAC1B0C,EAAS,KAAOb,GAAS,EACzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EAAG,2BAA2B,EAC1B2C,GAAuC5K,EAAO,SAASgK,EAAOd,EAAOH,EAAMO,EAAMC,EAAM,CACzF,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIwB,EAAW,CAAC,EACVjB,EAAMvB,EAAW,KAAMyC,GAAcA,EAAU,QAAUX,CAAK,EAYpE,GAXIP,GAAOO,IAAUP,EAAI,MACvBiB,EAAWjB,GAEXiB,EAAS,MAAQV,EACjB9B,EAAW,KAAKwC,CAAQ,GAEFxB,GAAU,KAChCwB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMxB,CAAM,EAEVH,GAAS,KAC9B2B,EAAS,KAAO,CAAE,KAAM,WAAY,UAEhC,OAAO3B,GAAS,SAAU,CAC5B,GAAI,CAACY,EAAKC,CAAK,EAAI,OAAO,QAAQb,CAAI,EAAE,CAAC,EACzC2B,EAASf,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEc,EAAS,KAAO,CAAE,KAAM3B,CAAK,EAGjC,GAAI,OAAOO,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCoB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOpB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCmB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOnB,EAElBmB,EAAS,eAAiB1C,EAC1B0C,EAAS,KAAOb,GAAS,EACzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EAAG,sBAAsB,EACrB4C,GAAoC7K,EAAO,SAAS8K,EAAUd,EAAOd,EAAOH,EAAMK,EAAOC,EAAQC,EAAMC,EAAM,CAC/G,GAAIS,IAAU,MAAQd,IAAU,KAC9B,OAEF,IAAIwB,EAAW,CAAC,EACVjB,EAAMvB,EAAW,KAAMyC,GAAcA,EAAU,QAAUX,CAAK,EAYpE,GAXIP,GAAOO,IAAUP,EAAI,MACvBiB,EAAWjB,GAEXiB,EAAS,MAAQV,EACjB9B,EAAW,KAAKwC,CAAQ,GAEFxB,GAAU,KAChCwB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMxB,CAAM,EAEVH,GAAS,KAC9B2B,EAAS,KAAO,CAAE,KAAM,MAAO,UAE3B,OAAO3B,GAAS,SAAU,CAC5B,GAAI,CAACY,EAAKC,CAAK,EAAI,OAAO,QAAQb,CAAI,EAAE,CAAC,EACzC2B,EAASf,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEc,EAAS,KAAO,CAAE,KAAM3B,CAAK,EAGjC,GAAwBK,GAAU,KAChCsB,EAAS,MAAQ,CAAE,KAAM,EAAG,UAExB,OAAOtB,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CsB,EAASf,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEc,EAAS,MAAQ,CAAE,KAAMtB,CAAM,EAGnC,GAAI,OAAOE,GAAS,SAAU,CAC5B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAI,EAAE,CAAC,EACzCoB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOpB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCmB,EAASf,CAAG,EAAIC,CAClB,MACEc,EAAS,KAAOnB,EAElBmB,EAAS,SAAWI,EACpBJ,EAAS,eAAiB1C,EAC1B0C,EAAS,KAAOb,GAAS,EACzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EAAG,mBAAmB,EAClB8C,GAAwC/K,EAAO,UAAW,CAC5DgI,EAAuBC,EACvBF,GAAmB,IAAI,EACvBE,EAAsBF,GAAmB,IAAI,EAC7CA,GAAmB,KAAKE,CAAmB,CAC7C,EAAG,uBAAuB,EACtB+C,GAAgChL,EAAO,SAAS+J,EAAakB,EAAaC,EAASC,EAAWC,EAAaC,EAAWC,EAAOjC,EAAQF,EAAOoC,EAAYC,EAAc,CACxK,IAAI/B,EAAM3B,EAAa,KAAM2D,GAAYA,EAAQ,QAAUR,CAAW,EACtE,GAAI,EAAAxB,IAAQ,SACVA,EAAMvB,EAAW,KAAMuD,GAAYA,EAAQ,QAAUR,CAAW,EAC5DxB,IAAQ,SAId,IAA0ByB,GAAY,KACpC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAACvB,EAAKC,CAAK,EAAI,OAAO,QAAQsB,CAAO,EAAE,CAAC,EAC5CzB,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,QAAUyB,EAGlB,GAA4BC,GAAc,KACxC,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAACxB,EAAKC,CAAK,EAAI,OAAO,QAAQuB,CAAS,EAAE,CAAC,EAC9C1B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,UAAY0B,EAGpB,GAA8BC,GAAgB,KAC5C,GAAI,OAAOA,GAAgB,SAAU,CACnC,GAAI,CAACzB,EAAKC,CAAK,EAAI,OAAO,QAAQwB,CAAW,EAAE,CAAC,EAChD3B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,YAAc2B,EAGtB,GAA4BC,GAAc,KACxC,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAAC1B,EAAKC,CAAK,EAAI,OAAO,QAAQyB,CAAS,EAAE,CAAC,EAC9C5B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,UAAY4B,EAGpB,GAAwBC,GAAU,KAChC,GAAI,OAAOA,GAAU,SAAU,CAC7B,GAAI,CAAC3B,EAAKC,CAAK,EAAI,OAAO,QAAQ0B,CAAK,EAAE,CAAC,EAC1C7B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,MAAQ6B,EAGhB,GAAyBjC,GAAW,KAClC,GAAI,OAAOA,GAAW,SAAU,CAC9B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAM,EAAE,CAAC,EAC3CI,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,OAASJ,EAGjB,GAAwBF,GAAU,KAChC,GAAI,OAAOA,GAAU,SAAU,CAC7B,GAAI,CAACQ,EAAKC,CAAK,EAAI,OAAO,QAAQT,CAAK,EAAE,CAAC,EAC1CM,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,MAAQN,EAGhB,GAA6BoC,GAAe,KAC1C,GAAI,OAAOA,GAAe,SAAU,CAClC,GAAI,CAAC5B,EAAKC,CAAK,EAAI,OAAO,QAAQ2B,CAAU,EAAE,CAAC,EAC/C9B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,WAAa8B,EAGrB,GAA+BC,GAAiB,KAC9C,GAAI,OAAOA,GAAiB,SAAU,CACpC,GAAI,CAAC7B,EAAKC,CAAK,EAAI,OAAO,QAAQ4B,CAAY,EAAE,CAAC,EACjD/B,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,aAAe+B,EAGzB,EAAG,eAAe,EACdE,GAAiC1L,EAAO,SAAS+J,EAAaf,EAAMC,EAAI0C,EAAWC,EAAWC,EAASC,EAAS,CAClH,IAAMrC,EAAMtB,GAAK,KAAMqB,GAAQA,EAAI,OAASR,GAAQQ,EAAI,KAAOP,CAAE,EACjE,GAAIQ,IAAQ,OAGZ,IAA4BkC,GAAc,KACxC,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAAChC,EAAKC,CAAK,EAAI,OAAO,QAAQ+B,CAAS,EAAE,CAAC,EAC9ClC,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,UAAYkC,EAGpB,GAA4BC,GAAc,KACxC,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAACjC,EAAKC,CAAK,EAAI,OAAO,QAAQgC,CAAS,EAAE,CAAC,EAC9CnC,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,UAAYmC,EAGpB,GAA0BC,GAAY,KACpC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAAClC,EAAKC,CAAK,EAAI,OAAO,QAAQiC,CAAO,EAAE,CAAC,EAC5CpC,EAAIE,CAAG,EAAI,SAASC,CAAK,CAC3B,MACEH,EAAI,QAAU,SAASoC,CAAO,EAGlC,GAA0BC,GAAY,KACpC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAACnC,EAAKC,CAAK,EAAI,OAAO,QAAQkC,CAAO,EAAE,CAAC,EAC5CrC,EAAIE,CAAG,EAAI,SAASC,CAAK,CAC3B,MACEH,EAAI,QAAU,SAASqC,CAAO,EAGpC,EAAG,gBAAgB,EACfC,GAAqC/L,EAAO,SAAS+J,EAAaiC,EAAmBC,EAAsB,CAC7G,IAAIC,EAAoB5D,GACpB6D,EAAuB5D,GAC3B,GAAI,OAAOyD,GAAsB,SAAU,CACzC,IAAMpC,EAAQ,OAAO,OAAOoC,CAAiB,EAAE,CAAC,EAChDE,EAAoB,SAAStC,CAAK,CACpC,MACEsC,EAAoB,SAASF,CAAiB,EAEhD,GAAI,OAAOC,GAAyB,SAAU,CAC5C,IAAMrC,EAAQ,OAAO,OAAOqC,CAAoB,EAAE,CAAC,EACnDE,EAAuB,SAASvC,CAAK,CACvC,MACEuC,EAAuB,SAASF,CAAoB,EAElDC,GAAqB,IACvB5D,GAAe4D,GAEbC,GAAwB,IAC1B5D,GAAkB4D,EAEtB,EAAG,oBAAoB,EACnBC,GAAkCpM,EAAO,UAAW,CACtD,OAAOsI,EACT,EAAG,iBAAiB,EAChB+D,GAAqCrM,EAAO,UAAW,CACzD,OAAOuI,EACT,EAAG,oBAAoB,EACnB+D,GAA0CtM,EAAO,UAAW,CAC9D,OAAOgI,CACT,EAAG,yBAAyB,EACxBuE,GAAyCvM,EAAO,UAAW,CAC7D,OAAOiI,CACT,EAAG,wBAAwB,EACvBuE,GAAkCxM,EAAO,SAASyM,EAAgB,CACpE,OAAiCA,GAAmB,KAC3C3E,EAEAA,EAAa,OAAQmC,GACnBA,EAAe,iBAAmBwC,CAC1C,CAEL,EAAG,iBAAiB,EAChBC,GAA6B1M,EAAO,SAASgK,EAAO,CACtD,OAAOlC,EAAa,KAAMmC,GAAmBA,EAAe,QAAUD,CAAK,CAC7E,EAAG,YAAY,EACX2C,GAAiC3M,EAAO,SAASyM,EAAgB,CACnE,OAAO,OAAO,KAAKD,GAAgBC,CAAc,CAAC,CACpD,EAAG,gBAAgB,EACfG,GAAgC5M,EAAO,SAASyM,EAAgB,CAClE,OAAiCA,GAAmB,KAC3CvE,EAEAA,EAAW,OAAQwC,GAAaA,EAAS,iBAAmB+B,CAAc,CAErF,EAAG,eAAe,EACdI,GAAeD,GACfE,GAA0B9M,EAAO,UAAW,CAC9C,OAAOmI,EACT,EAAG,SAAS,EACR4E,GAA2B/M,EAAO,UAAW,CAC/C,OAAOoI,EACT,EAAG,UAAU,EACT4E,GAA0BhN,EAAO,SAASiN,EAAa,CACzD5E,GAAc4E,CAChB,EAAG,SAAS,EACRpD,GAA2B7J,EAAO,UAAW,CAC/C,OAAOqI,EACT,EAAG,UAAU,EACT6E,GAAwBlN,EAAO,UAAW,CAC5C8H,EAAe,CAAC,EAChBI,EAAa,CACX,CACE,MAAO,SACP,MAAO,CAAE,KAAM,QAAS,EACxB,KAAM,CAAE,KAAM,QAAS,EACvB,KAAM,KACN,KAAM,KACN,eAAgB,EAClB,CACF,EACAD,EAAsB,GACtBD,EAAuB,SACvBD,GAAqB,CAAC,EAAE,EACxBI,GAAO,CAAC,EACRJ,GAAqB,CAAC,EAAE,EACxBK,GAAQ,GACRC,GAAc,GACdC,GAAe,EACfC,GAAkB,CACpB,EAAG,OAAO,EACN4E,GAAW,CACb,MAAO,EACP,OAAQ,EACR,KAAM,EACN,YAAa,EACb,aAAc,EACd,WAAY,EACZ,YAAa,EACb,WAAY,GACZ,SAAU,GACV,UAAW,GACX,SAAU,GACV,QAAS,GACT,UAAW,GACX,QAAS,GACT,aAAc,GACd,WAAY,GACZ,UAAW,GACX,QAAS,GACT,QAAS,GACT,WAAY,GACZ,SAAU,GACV,YAAa,GACb,aAAc,EAChB,EACIC,GAAY,CACd,OAAQ,EACR,KAAM,CACR,EACIC,GAAY,CACd,OAAQ,EACR,QAAS,EACT,KAAM,CACR,EACIC,GAA2BtN,EAAO,SAASuN,EAAK,CAElDnF,GADoBQ,GAAa2E,EAAK1E,GAAU,CAAC,CAEnD,EAAG,UAAU,EACT2E,GAAe,CACjB,kBAAA1D,GACA,0BAAAW,GACA,aAAAN,GACA,qBAAAS,GACA,aAAAN,GACA,kBAAAO,GACA,sBAAAE,GACA,OAAAjC,GACA,cAAAkC,GACA,eAAAU,GACA,mBAAAK,GACA,SAAAlC,GACA,QAAAmD,GACA,gBAAAR,GACA,WAAAE,GACA,eAAAC,GACA,cAAAC,GACA,aAAAC,GACA,wBAAAP,GACA,uBAAAC,GACA,QAAAO,GACA,SAAAC,GACA,UAAAtE,GACA,gBAAA2D,GACA,mBAAAC,GACA,YAAAoB,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,UAA2B5N,EAAO,IAAM6I,GAAU,EAAE,GAAI,WAAW,EACnE,MAAAqE,GACA,SAAAC,GACA,UAAAC,GACA,UAAAC,GACA,SAAAC,GACA,UAAA5E,EAEF,EAOImF,GAA4B7N,EAAO,SAAS8N,EAAMC,EAAU,CAC9D,OAAOC,GAASF,EAAMC,CAAQ,CAChC,EAAG,UAAU,EACTE,GAA4BjO,EAAO,SAAS8N,EAAMI,EAAOC,EAAQC,EAAGC,EAAG9E,EAAM,CAC/E,IAAM+E,EAAYR,EAAK,OAAO,OAAO,EACrCQ,EAAU,KAAK,QAASJ,CAAK,EAC7BI,EAAU,KAAK,SAAUH,CAAM,EAC/BG,EAAU,KAAK,IAAKF,CAAC,EACrBE,EAAU,KAAK,IAAKD,CAAC,EACrB,IAAIE,EAAgBhF,EAAK,WAAW,uBAAuB,EAAIA,KAAO,gBAAYA,CAAI,EACtF+E,EAAU,KAAK,aAAcC,CAAa,CAC5C,EAAG,WAAW,EACVC,GAA2BxO,EAAO,CAAC8N,EAAMW,EAAOC,IAAU,CAC5D,IAAMC,EAAWb,EAAK,OAAO,GAAG,EAC5BxG,EAAI,EACR,QAASkC,KAAOiF,EAAO,CACrB,IAAI9C,EAAYnC,EAAI,UAAYA,EAAI,UAAY,UAC5CoF,EAAcpF,EAAI,UAAYA,EAAI,UAAY,UAC9CqC,EAAUrC,EAAI,QAAU,SAASA,EAAI,OAAO,EAAI,EAChDsC,EAAUtC,EAAI,QAAU,SAASA,EAAI,OAAO,EAAI,EAChDqF,EAAM,GACV,GAAIvH,IAAM,EAAG,CACX,IAAIwH,EAAOH,EAAS,OAAO,MAAM,EACjCG,EAAK,KAAK,KAAMtF,EAAI,WAAW,CAAC,EAChCsF,EAAK,KAAK,KAAMtF,EAAI,WAAW,CAAC,EAChCsF,EAAK,KAAK,KAAMtF,EAAI,SAAS,CAAC,EAC9BsF,EAAK,KAAK,KAAMtF,EAAI,SAAS,CAAC,EAC9BsF,EAAK,KAAK,eAAgB,GAAG,EAC7BA,EAAK,KAAK,SAAUF,CAAW,EAC/BE,EAAK,MAAM,OAAQ,MAAM,EACrBtF,EAAI,OAAS,SACfsF,EAAK,KAAK,aAAc,OAASD,EAAM,aAAa,GAElDrF,EAAI,OAAS,SAAWA,EAAI,OAAS,UACvCsF,EAAK,KAAK,eAAgB,OAASD,EAAM,YAAY,EAEvDvH,EAAI,EACN,KAAO,CACL,IAAIwH,EAAOH,EAAS,OAAO,MAAM,EACjCG,EAAK,KAAK,OAAQ,MAAM,EAAE,KAAK,eAAgB,GAAG,EAAE,KAAK,SAAUF,CAAW,EAAE,KAC9E,IACA,iDAAiD,WAAW,SAAUpF,EAAI,WAAW,CAAC,EAAE,WAAW,SAAUA,EAAI,WAAW,CAAC,EAAE,WAC7H,WACAA,EAAI,WAAW,GAAKA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,GAAKA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,CACrG,EAAE,WAAW,WAAYA,EAAI,WAAW,GAAKA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,CAAC,EAAE,WAAW,QAASA,EAAI,SAAS,CAAC,EAAE,WAAW,QAASA,EAAI,SAAS,CAAC,CAC7J,EACIA,EAAI,OAAS,SACfsF,EAAK,KAAK,aAAc,OAASD,EAAM,aAAa,GAElDrF,EAAI,OAAS,SAAWA,EAAI,OAAS,UACvCsF,EAAK,KAAK,eAAgB,OAASD,EAAM,YAAY,CAEzD,CACA,IAAIE,EAAcL,EAAM,YAAY,EACpCM,EAAuBN,CAAK,EAC1BlF,EAAI,MAAM,KACVmF,EACA,KAAK,IAAInF,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EAAI,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAAIqC,EAC/F,KAAK,IAAIrC,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EAAI,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAAIsC,EAC/FtC,EAAI,MAAM,MACVA,EAAI,MAAM,OACV,CAAE,KAAMmC,CAAU,EAClBoD,CACF,EACIvF,EAAI,OAASA,EAAI,MAAM,OAAS,KAClCuF,EAAcL,EAAM,YAAY,EAChCM,EAAuBN,CAAK,EAC1B,IAAMlF,EAAI,MAAM,KAAO,IACvBmF,EACA,KAAK,IAAInF,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EAAI,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAAIqC,EAC/F,KAAK,IAAIrC,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EAAI,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAAIkF,EAAM,gBAAkB,EAAI5C,EAC3H,KAAK,IAAItC,EAAI,MAAM,MAAOA,EAAI,MAAM,KAAK,EACzCA,EAAI,MAAM,OACV,CAAE,KAAMmC,EAAW,aAAc,QAAS,EAC1CoD,CACF,EAEJ,CACF,EAAG,UAAU,EACTE,GAA+BjP,EAAO,SAAS8N,EAAMpD,EAAUgE,EAAO,CACxE,IAAMQ,EAAepB,EAAK,OAAO,GAAG,EAChCqB,EAAYzE,EAAS,QAAUA,EAAS,QAAU,OAClDkE,EAAclE,EAAS,YAAcA,EAAS,YAAc,UAC5DS,EAAYT,EAAS,UAAYA,EAAS,UAAY,QACtD0E,EAAa,CAAE,eAAgB,EAAG,mBAAoB,SAAU,EAChE1E,EAAS,WACX0E,EAAa,CAAE,eAAgB,CAAE,GAEnC,IAAIrB,EAAW,CACb,EAAGrD,EAAS,EACZ,EAAGA,EAAS,EACZ,KAAMyE,EACN,OAAQP,EACR,MAAOlE,EAAS,MAChB,OAAQA,EAAS,OACjB,GAAI,IACJ,GAAI,IACJ,MAAO0E,CACT,EACAvB,GAAUqB,EAAcnB,CAAQ,EAChC,IAAIsB,EAAeX,EAAM,aAAa,EACtCW,EAAa,WAAa,OAC1BA,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAYlE,EACzB6D,EAAuBN,CAAK,EAC1BhE,EAAS,MAAM,KACfwE,EACAxE,EAAS,EACTA,EAAS,EAAIA,EAAS,MAAM,EAC5BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClB2E,CACF,EACI3E,EAAS,MAAQA,EAAS,KAAK,OAAS,KAC1C2E,EAAeX,EAAM,aAAa,EAClCW,EAAa,UAAYlE,EACzB6D,EAAuBN,CAAK,EAC1BhE,EAAS,KAAK,KACdwE,EACAxE,EAAS,EACTA,EAAS,EAAIA,EAAS,KAAK,EAC3BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClB2E,CACF,GAEE3E,EAAS,OAASA,EAAS,MAAM,OAAS,KAC5C2E,EAAeX,EAAM,aAAa,EAClCW,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAYlE,EACzB6D,EAAuBN,CAAK,EAC1BhE,EAAS,MAAM,KACfwE,EACAxE,EAAS,EACTA,EAAS,EAAIA,EAAS,MAAM,EAC5BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClB2E,CACF,EAEJ,EAAG,cAAc,EACbC,GAA8BtP,EAAO,SAAS8N,EAAMyB,EAASb,EAAO,CACtE,IAAIS,EAAYI,EAAQ,QAAUA,EAAQ,QAAUb,EAAMa,EAAQ,YAAY,KAAO,WAAW,EAC5FX,EAAcW,EAAQ,YAAcA,EAAQ,YAAcb,EAAMa,EAAQ,YAAY,KAAO,eAAe,EAC1GpE,EAAYoE,EAAQ,UAAYA,EAAQ,UAAY,UACpDC,EAAY,qyBAChB,OAAQD,EAAQ,YAAY,KAAM,CAChC,IAAK,SACHC,EAAY,qyBACZ,MACF,IAAK,kBACHA,EAAY,ivBACZ,KACJ,CACA,IAAMC,EAAc3B,EAAK,OAAO,GAAG,EACnC2B,EAAY,KAAK,QAAS,YAAY,EACtC,IAAMC,EAAOC,GAAY,EACzB,OAAQJ,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACL,IAAK,SACL,IAAK,kBACL,IAAK,YACL,IAAK,qBACL,IAAK,YACL,IAAK,qBACHG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,KAAOP,EACZO,EAAK,MAAQH,EAAQ,MACrBG,EAAK,OAASH,EAAQ,OACtBG,EAAK,OAASd,EACdc,EAAK,GAAK,IACVA,EAAK,GAAK,IACVA,EAAK,MAAQ,CAAE,eAAgB,EAAI,EACnC7B,GAAU4B,EAAaC,CAAI,EAC3B,MACF,IAAK,YACL,IAAK,qBACL,IAAK,eACL,IAAK,wBACL,IAAK,eACL,IAAK,wBACHD,EAAY,OAAO,MAAM,EAAE,KAAK,OAAQN,CAAS,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,SAAUP,CAAW,EAAE,KACzG,IACA,4HAA4H,WAAW,SAAUW,EAAQ,CAAC,EAAE,WAAW,SAAUA,EAAQ,CAAC,EAAE,WAAW,OAAQA,EAAQ,MAAQ,CAAC,EAAE,WAAW,SAAUA,EAAQ,MAAM,CACvQ,EACAE,EAAY,OAAO,MAAM,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,SAAUb,CAAW,EAAE,KACtG,IACA,0DAA0D,WAAW,SAAUW,EAAQ,CAAC,EAAE,WAAW,SAAUA,EAAQ,CAAC,EAAE,WAAW,OAAQA,EAAQ,MAAQ,CAAC,CAChK,EACA,MACF,IAAK,eACL,IAAK,wBACL,IAAK,kBACL,IAAK,2BACL,IAAK,kBACL,IAAK,2BACHE,EAAY,OAAO,MAAM,EAAE,KAAK,OAAQN,CAAS,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,SAAUP,CAAW,EAAE,KACzG,IACA,kHAAkH,WAAW,SAAUW,EAAQ,CAAC,EAAE,WAAW,SAAUA,EAAQ,CAAC,EAAE,WAAW,QAASA,EAAQ,KAAK,EAAE,WAAW,OAAQA,EAAQ,OAAS,CAAC,CAC5P,EACAE,EAAY,OAAO,MAAM,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,SAAUb,CAAW,EAAE,KACtG,IACA,2DAA2D,WAAW,SAAUW,EAAQ,EAAIA,EAAQ,KAAK,EAAE,WAAW,SAAUA,EAAQ,CAAC,EAAE,WAAW,OAAQA,EAAQ,OAAS,CAAC,CAClL,EACA,KACJ,CACA,IAAIK,EAAkBC,GAAenB,EAAOa,EAAQ,YAAY,IAAI,EAEpE,OADAE,EAAY,OAAO,MAAM,EAAE,KAAK,OAAQtE,CAAS,EAAE,KAAK,cAAeyE,EAAgB,UAAU,EAAE,KAAK,YAAaA,EAAgB,SAAW,CAAC,EAAE,KAAK,aAAc,QAAQ,EAAE,KAAK,eAAgB,SAAS,EAAE,KAAK,aAAcL,EAAQ,YAAY,KAAK,EAAE,KAAK,IAAKA,EAAQ,EAAIA,EAAQ,MAAQ,EAAIA,EAAQ,YAAY,MAAQ,CAAC,EAAE,KAAK,IAAKA,EAAQ,EAAIA,EAAQ,YAAY,CAAC,EAAE,KAAK,KAAOA,EAAQ,YAAY,KAAO,IAAI,EACvZA,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACHtB,GACEwB,EACA,GACA,GACAF,EAAQ,EAAIA,EAAQ,MAAQ,EAAI,GAChCA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BC,CACF,EACA,KACJ,CACA,IAAIM,EAAepB,EAAMa,EAAQ,YAAY,KAAO,MAAM,EAAE,EAC5D,OAAAO,EAAa,WAAa,OAC1BA,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAY3E,EACzB6D,EAAuBN,CAAK,EAC1Ba,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMpE,CAAU,EAClB2E,CACF,EACAA,EAAepB,EAAMa,EAAQ,YAAY,KAAO,MAAM,EAAE,EACxDO,EAAa,UAAY3E,EACrBoE,EAAQ,OAASA,EAAQ,OAAO,OAAS,GAC3CP,EAAuBN,CAAK,EAC1Ba,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMpE,EAAW,aAAc,QAAS,EAC1C2E,CACF,EACSP,EAAQ,MAAQA,EAAQ,KAAK,OAAS,IAC/CP,EAAuBN,CAAK,EAC1Ba,EAAQ,KAAK,KACbE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,KAAK,EACzBA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMpE,EAAW,aAAc,QAAS,EAC1C2E,CACF,EAEEP,EAAQ,OAASA,EAAQ,MAAM,OAAS,KAC1CO,EAAepB,EAAM,WAAW,EAChCoB,EAAa,UAAY3E,EACzB6D,EAAuBN,CAAK,EAC1Ba,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMpE,CAAU,EAClB2E,CACF,GAEKP,EAAQ,MACjB,EAAG,aAAa,EACZQ,GAAqC/P,EAAO,SAAS8N,EAAM,CAC7DA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,UAAU,EAAE,KAAK,YAAa,SAAS,EAAE,KAAK,YAAa,SAAS,EAAE,OAAO,MAAM,EAAE,KAAK,YAAa,WAAW,EAAE,KAClK,IACA,i1ZACF,CACF,EAAG,oBAAoB,EACnBkC,GAAqChQ,EAAO,SAAS8N,EAAM,CAC7DA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,UAAU,EAAE,KAAK,QAAS,IAAI,EAAE,KAAK,SAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,YAAa,WAAW,EAAE,KACjJ,IACA,0JACF,CACF,EAAG,oBAAoB,EACnBmC,GAAkCjQ,EAAO,SAAS8N,EAAM,CAC1DA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,OAAO,EAAE,KAAK,QAAS,IAAI,EAAE,KAAK,SAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,YAAa,WAAW,EAAE,KAC9I,IACA,2UACF,CACF,EAAG,iBAAiB,EAChBoC,GAAkClQ,EAAO,SAAS8N,EAAM,CAC1DA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,WAAW,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,gBAAgB,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,uBAAuB,CAC7P,EAAG,iBAAiB,EAChBqC,GAAiCnQ,EAAO,SAAS8N,EAAM,CACzDA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,UAAU,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,gBAAgB,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,wBAAwB,CAC7P,EAAG,gBAAgB,EACfsC,GAAwCpQ,EAAO,SAAS8N,EAAM,CAChEA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,aAAa,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,2BAA2B,CAC9N,EAAG,uBAAuB,EACtBuC,GAAsCrQ,EAAO,SAAS8N,EAAM,CAC9DA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,gBAAgB,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,IAAK,CAAC,CACxO,EAAG,qBAAqB,EACpBwC,GAAuCtQ,EAAO,SAAS8N,EAAM,CAE/D,IAAMyC,EADOzC,EAAK,OAAO,MAAM,EACX,OAAO,QAAQ,EAAE,KAAK,KAAM,WAAW,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,SAAU,MAAM,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,OAAQ,CAAC,EACnKyC,EAAO,OAAO,MAAM,EAAE,KAAK,OAAQ,OAAO,EAAE,KAAK,SAAU,SAAS,EAAE,MAAM,mBAAoB,MAAM,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,IAAK,mBAAmB,EACjKA,EAAO,OAAO,MAAM,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,SAAU,SAAS,EAAE,MAAM,mBAAoB,MAAM,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,IAAK,yBAAyB,CACxK,EAAG,sBAAsB,EACrBV,GAAiC7P,EAAO,CAACwQ,EAAKzG,KACzC,CACL,WAAYyG,EAAIzG,EAAc,YAAY,EAC1C,SAAUyG,EAAIzG,EAAc,UAAU,EACtC,WAAYyG,EAAIzG,EAAc,YAAY,CAC5C,GACC,gBAAgB,EACfiF,EAAyC,UAAW,CACtD,SAASyB,EAAOC,EAASC,EAAGvC,EAAGC,EAAGH,EAAOC,EAAQyC,EAAW,CAC1D,IAAMC,EAAOF,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKvC,EAAIF,EAAQ,CAAC,EAAE,KAAK,IAAKG,EAAIF,EAAS,EAAI,CAAC,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAKuC,CAAO,EAChII,EAAcD,EAAMD,CAAS,CAC/B,CACA5Q,EAAOyQ,EAAQ,QAAQ,EACvB,SAASM,EAAQL,EAASC,EAAGvC,EAAGC,EAAGH,EAAOC,EAAQyC,EAAWlC,EAAO,CAClE,GAAM,CAAE,SAAAsC,EAAU,WAAAC,EAAY,WAAAC,CAAW,EAAIxC,EACvChI,EAAQgK,EAAQ,MAAMS,GAAe,cAAc,EACzD,QAAS7J,EAAI,EAAGA,EAAIZ,EAAM,OAAQY,IAAK,CACrC,IAAM8J,EAAK9J,EAAI0J,EAAWA,GAAYtK,EAAM,OAAS,GAAK,EACpDmK,EAAOF,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKvC,EAAIF,EAAQ,CAAC,EAAE,KAAK,IAAKG,CAAC,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAK,oBAAqB,QAAQ,EAAE,MAAM,YAAa2C,CAAQ,EAAE,MAAM,cAAeE,CAAU,EAAE,MAAM,cAAeD,CAAU,EACpOJ,EAAK,OAAO,OAAO,EAAE,KAAK,KAAMO,CAAE,EAAE,KAAK1K,EAAMY,CAAC,CAAC,EAAE,KAAK,qBAAsB,cAAc,EAC5FwJ,EAAcD,EAAMD,CAAS,CAC/B,CACF,CACA5Q,EAAO+Q,EAAS,SAAS,EACzB,SAASM,EAAKX,EAASC,EAAGvC,EAAGC,EAAGH,EAAOC,EAAQyC,EAAWlC,EAAO,CAC/D,IAAM4C,EAAIX,EAAE,OAAO,QAAQ,EAErBE,EADIS,EAAE,OAAO,eAAe,EAAE,KAAK,IAAKlD,CAAC,EAAE,KAAK,IAAKC,CAAC,EAAE,KAAK,QAASH,CAAK,EAAE,KAAK,SAAUC,CAAM,EACzF,OAAO,WAAW,EAAE,MAAM,UAAW,OAAO,EAAE,MAAM,SAAU,MAAM,EAAE,MAAM,QAAS,MAAM,EAC1G0C,EAAK,OAAO,KAAK,EAAE,MAAM,UAAW,YAAY,EAAE,MAAM,aAAc,QAAQ,EAAE,MAAM,iBAAkB,QAAQ,EAAE,KAAKH,CAAO,EAC9HK,EAAQL,EAASY,EAAGlD,EAAGC,EAAGH,EAAOC,EAAQyC,EAAWlC,CAAK,EACzDoC,EAAcD,EAAMD,CAAS,CAC/B,CACA5Q,EAAOqR,EAAM,MAAM,EACnB,SAASP,EAAcS,EAAQC,EAAmB,CAChD,QAAW7H,KAAO6H,EACZA,EAAkB,eAAe7H,CAAG,GACtC4H,EAAO,KAAK5H,EAAK6H,EAAkB7H,CAAG,CAAC,CAG7C,CACA,OAAA3J,EAAO8Q,EAAe,eAAe,EAC9B,SAASpC,EAAO,CACrB,OAAOA,EAAM,gBAAkB,KAAO2C,EAAO3C,EAAM,gBAAkB,MAAQ+B,EAASM,CACxF,CACF,EAAE,EACEU,EAAkB,CACpB,SAAU5D,GACV,aAAAoB,GACA,YAAAK,GACA,SAAAd,GACA,UAAAP,GACA,gBAAAiC,GACA,eAAAC,GACA,sBAAAC,GACA,oBAAAC,GACA,qBAAAC,GACA,mBAAAP,GACA,mBAAAC,GACA,gBAAAC,EACF,EAGIyB,GAAqB,EACrBC,GAAqB,EACrBC,GAAgB,EAChBC,GAAmB,EACvB/R,GAAO,GAAK0N,GACZ,IAAIsE,EAAO,CAAC,EACRC,GAAS,KAAM,CACjB,MAAO,CACL/R,EAAO,KAAM,QAAQ,CACvB,CACA,YAAYgS,EAAS,CACnB,KAAK,KAAO,GACZ,KAAK,KAAO,CAAC,EACb,KAAK,KAAK,OAAS,OACnB,KAAK,KAAK,MAAQ,OAClB,KAAK,KAAK,OAAS,OACnB,KAAK,KAAK,MAAQ,OAClB,KAAK,KAAK,WAAa,OACvB,KAAK,SAAW,CAAC,EACjB,KAAK,SAAS,OAAS,OACvB,KAAK,SAAS,MAAQ,OACtB,KAAK,SAAS,OAAS,OACvB,KAAK,SAAS,MAAQ,OACtB,KAAK,SAAS,IAAM,EACpBC,GAAQD,EAAQ,GAAG,UAAU,CAAC,CAChC,CACA,QAAQE,EAAQC,EAAOC,EAAQC,EAAO,CACpC,KAAK,SAAS,OAAS,KAAK,KAAK,OAASH,EAC1C,KAAK,SAAS,MAAQ,KAAK,KAAK,MAAQC,EACxC,KAAK,SAAS,OAAS,KAAK,KAAK,OAASC,EAC1C,KAAK,SAAS,MAAQ,KAAK,KAAK,MAAQC,CAC1C,CACA,UAAUC,EAAK3I,EAAK4I,EAAKC,EAAK,CACxBF,EAAI3I,CAAG,IAAM,OACf2I,EAAI3I,CAAG,EAAI4I,EAEXD,EAAI3I,CAAG,EAAI6I,EAAID,EAAKD,EAAI3I,CAAG,CAAC,CAEhC,CACA,OAAO4F,EAAS,CACd,KAAK,SAAS,IAAM,KAAK,SAAS,IAAM,EACxC,IAAIkD,EAAU,KAAK,SAAS,SAAW,KAAK,SAAS,MAAQ,KAAK,SAAS,MAAQlD,EAAQ,OAAS,KAAK,SAAS,MAAQA,EAAQ,OAAS,EACvImD,EAASD,EAAUlD,EAAQ,MAC3BoD,EAAU,KAAK,SAAS,OAASpD,EAAQ,OAAS,EAClDqD,EAASD,EAAUpD,EAAQ,QAC3BkD,GAAW,KAAK,KAAK,YAAcC,GAAU,KAAK,KAAK,YAAc,KAAK,SAAS,IAAMd,MAC3Fa,EAAU,KAAK,SAAS,OAASlD,EAAQ,OAASuC,EAAK,iBACvDa,EAAU,KAAK,SAAS,MAAQpD,EAAQ,OAAS,EACjD,KAAK,SAAS,MAAQmD,EAASD,EAAUlD,EAAQ,MACjD,KAAK,SAAS,OAAS,KAAK,SAAS,MACrC,KAAK,SAAS,MAAQqD,EAASD,EAAUpD,EAAQ,OACjD,KAAK,SAAS,IAAM,GAEtBA,EAAQ,EAAIkD,EACZlD,EAAQ,EAAIoD,EACZ,KAAK,UAAU,KAAK,KAAM,SAAUF,EAAS,KAAK,GAAG,EACrD,KAAK,UAAU,KAAK,KAAM,SAAUE,EAAS,KAAK,GAAG,EACrD,KAAK,UAAU,KAAK,KAAM,QAASD,EAAQ,KAAK,GAAG,EACnD,KAAK,UAAU,KAAK,KAAM,QAASE,EAAQ,KAAK,GAAG,EACnD,KAAK,UAAU,KAAK,SAAU,SAAUH,EAAS,KAAK,GAAG,EACzD,KAAK,UAAU,KAAK,SAAU,SAAUE,EAAS,KAAK,GAAG,EACzD,KAAK,UAAU,KAAK,SAAU,QAASD,EAAQ,KAAK,GAAG,EACvD,KAAK,UAAU,KAAK,SAAU,QAASE,EAAQ,KAAK,GAAG,CACzD,CACA,KAAKZ,EAAS,CACZ,KAAK,KAAO,GACZ,KAAK,KAAO,CACV,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,OACP,WAAY,MACd,EACA,KAAK,SAAW,CACd,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,OACP,IAAK,CACP,EACAC,GAAQD,EAAQ,GAAG,UAAU,CAAC,CAChC,CACA,eAAea,EAAQ,CACrB,KAAK,KAAK,OAASA,EACnB,KAAK,KAAK,OAASA,CACrB,CACF,EACIZ,GAA0BjS,EAAO,SAASwQ,EAAK,CACjDsC,GAAwBhB,EAAMtB,CAAG,EAC7BA,EAAI,aACNsB,EAAK,iBAAmBA,EAAK,iBAAmBA,EAAK,kBAAoBtB,EAAI,YAE3EA,EAAI,WACNsB,EAAK,eAAiBA,EAAK,eAAiBA,EAAK,gBAAkBtB,EAAI,UAErEA,EAAI,aACNsB,EAAK,iBAAmBA,EAAK,iBAAmBA,EAAK,kBAAoBtB,EAAI,WAEjF,EAAG,SAAS,EACRuC,GAA8B/S,EAAO,CAACwQ,EAAKzG,KACtC,CACL,WAAYyG,EAAIzG,EAAc,YAAY,EAC1C,SAAUyG,EAAIzG,EAAc,UAAU,EACtC,WAAYyG,EAAIzG,EAAc,YAAY,CAC5C,GACC,aAAa,EACZiJ,GAA+BhT,EAAQwQ,IAClC,CACL,WAAYA,EAAI,mBAChB,SAAUA,EAAI,iBACd,WAAYA,EAAI,kBAClB,GACC,cAAc,EACbyC,GAA8BjT,EAAQwQ,IACjC,CACL,WAAYA,EAAI,kBAChB,SAAUA,EAAI,gBACd,WAAYA,EAAI,iBAClB,GACC,aAAa,EAChB,SAAS0C,EAAkBC,EAAU5D,EAAS6D,EAAiBC,EAAUC,EAAgB,CACvF,GAAI,CAAC/D,EAAQ4D,CAAQ,EAAE,MACrB,GAAIC,EACF7D,EAAQ4D,CAAQ,EAAE,KAAOI,GAAUhE,EAAQ4D,CAAQ,EAAE,KAAMG,EAAgBD,CAAQ,EACnF9D,EAAQ4D,CAAQ,EAAE,UAAY5D,EAAQ4D,CAAQ,EAAE,KAAK,MAAMhC,GAAe,cAAc,EAAE,OAC1F5B,EAAQ4D,CAAQ,EAAE,MAAQG,EAC1B/D,EAAQ4D,CAAQ,EAAE,OAASK,GAAoBjE,EAAQ4D,CAAQ,EAAE,KAAME,CAAQ,MAC1E,CACL,IAAI3M,EAAQ6I,EAAQ4D,CAAQ,EAAE,KAAK,MAAMhC,GAAe,cAAc,EACtE5B,EAAQ4D,CAAQ,EAAE,UAAYzM,EAAM,OACpC,IAAI+M,EAAa,EACjBlE,EAAQ4D,CAAQ,EAAE,OAAS,EAC3B5D,EAAQ4D,CAAQ,EAAE,MAAQ,EAC1B,QAAWrE,KAAQpI,EACjB6I,EAAQ4D,CAAQ,EAAE,MAAQ,KAAK,IAC7BO,GAAmB5E,EAAMuE,CAAQ,EACjC9D,EAAQ4D,CAAQ,EAAE,KACpB,EACAM,EAAaD,GAAoB1E,EAAMuE,CAAQ,EAC/C9D,EAAQ4D,CAAQ,EAAE,OAAS5D,EAAQ4D,CAAQ,EAAE,OAASM,CAE1D,CAEJ,CACAzT,EAAOkT,EAAmB,mBAAmB,EAC7C,IAAIS,GAAgC3T,EAAO,SAAS4T,EAAUlJ,EAAUmJ,EAAQ,CAC9EnJ,EAAS,EAAImJ,EAAO,KAAK,OACzBnJ,EAAS,EAAImJ,EAAO,KAAK,OACzBnJ,EAAS,MAAQmJ,EAAO,KAAK,MAAQA,EAAO,KAAK,OACjDnJ,EAAS,OAASmJ,EAAO,KAAK,MAAQA,EAAO,KAAK,OAClDnJ,EAAS,MAAM,EAAIoH,EAAK,cAAgB,GACxC,IAAIgC,EAAmBpJ,EAAS,MAAQoH,EAAK,KACzCiC,EAAoBf,GAAalB,CAAI,EACzCiC,EAAkB,SAAWA,EAAkB,SAAW,EAC1DA,EAAkB,WAAa,OAC/B,IAAIT,EAAiBI,GAAmBhJ,EAAS,MAAM,KAAMqJ,CAAiB,EAC9Eb,EAAkB,QAASxI,EAAUoJ,EAAkBC,EAAmBT,CAAc,EACxF7B,EAAgB,aAAamC,EAAUlJ,EAAUoH,CAAI,CACvD,EAAG,cAAc,EACbkC,GAAmChU,EAAO,SAASiU,EAAeL,EAAUM,EAAeC,EAAa,CAC1G,IAAIC,EAAI,EACR,QAAWC,KAAcF,EAAa,CACpCC,EAAI,EACJ,IAAM7E,EAAU2E,EAAcG,CAAU,EACpCC,EAAkBvB,GAAYjB,EAAMvC,EAAQ,YAAY,IAAI,EAUhE,OATA+E,EAAgB,SAAWA,EAAgB,SAAW,EACtD/E,EAAQ,YAAY,MAAQmE,GAC1B,OAASnE,EAAQ,YAAY,KAAO,OACpC+E,CACF,EACA/E,EAAQ,YAAY,OAAS+E,EAAgB,SAAW,EACxD/E,EAAQ,YAAY,EAAIuC,EAAK,eAC7BsC,EAAI7E,EAAQ,YAAY,EAAIA,EAAQ,YAAY,OAAS,EACzDA,EAAQ,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,CAAE,EACpCA,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACHA,EAAQ,MAAM,MAAQ,GACtBA,EAAQ,MAAM,OAAS,GACvBA,EAAQ,MAAM,EAAI6E,EAClBA,EAAI7E,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OACpC,KACJ,CACIA,EAAQ,SACVA,EAAQ,MAAM,MAAQ,GACtBA,EAAQ,MAAM,OAAS,GACvBA,EAAQ,MAAM,EAAI6E,EAClBA,EAAI7E,EAAQ,MAAM,EAAIA,EAAQ,MAAM,QAEtC,IAAI6D,EAAkB7D,EAAQ,MAAQuC,EAAK,KACvCwB,EAAiBxB,EAAK,MAAQA,EAAK,eAAiB,EACpDyC,EAAmBxB,GAAYjB,EAAMvC,EAAQ,YAAY,IAAI,EAMjE,GALAgF,EAAiB,SAAWA,EAAiB,SAAW,EACxDA,EAAiB,WAAa,OAC9BrB,EAAkB,QAAS3D,EAAS6D,EAAiBmB,EAAkBjB,CAAc,EACrF/D,EAAQ,MAAM,EAAI6E,EAAI,EACtBA,EAAI7E,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OAChCA,EAAQ,MAAQA,EAAQ,KAAK,OAAS,GAAI,CAC5CA,EAAQ,KAAK,KAAO,IAAMA,EAAQ,KAAK,KAAO,IAC9C,IAAIiF,EAAmBzB,GAAYjB,EAAMvC,EAAQ,YAAY,IAAI,EACjE2D,EAAkB,OAAQ3D,EAAS6D,EAAiBoB,EAAkBlB,CAAc,EACpF/D,EAAQ,KAAK,EAAI6E,EAAI,EACrBA,EAAI7E,EAAQ,KAAK,EAAIA,EAAQ,KAAK,MACpC,SAAWA,EAAQ,OAASA,EAAQ,MAAM,OAAS,GAAI,CACrDA,EAAQ,MAAM,KAAO,IAAMA,EAAQ,MAAM,KAAO,IAChD,IAAIkF,EAAmB1B,GAAYjB,EAAMvC,EAAQ,MAAM,IAAI,EAC3D2D,EAAkB,QAAS3D,EAAS6D,EAAiBqB,EAAkBnB,CAAc,EACrF/D,EAAQ,MAAM,EAAI6E,EAAI,EACtBA,EAAI7E,EAAQ,MAAM,EAAIA,EAAQ,MAAM,MACtC,CACA,IAAImF,EAAaN,EACbO,EAAYpF,EAAQ,MAAM,MAC9B,GAAIA,EAAQ,OAASA,EAAQ,MAAM,OAAS,GAAI,CAC9C,IAAIqF,EAAmB7B,GAAYjB,EAAMvC,EAAQ,YAAY,IAAI,EACjE2D,EAAkB,QAAS3D,EAAS6D,EAAiBwB,EAAkBtB,CAAc,EACrF/D,EAAQ,MAAM,EAAI6E,EAAI,GACtBA,EAAI7E,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OACpCoF,EAAY,KAAK,IAAIpF,EAAQ,MAAM,MAAOA,EAAQ,MAAM,KAAK,EAC7DmF,EAAaN,EAAI7E,EAAQ,MAAM,UAAY,CAC7C,CACAoF,EAAYA,EAAY7C,EAAK,eAC7BvC,EAAQ,MAAQ,KAAK,IAAIA,EAAQ,OAASuC,EAAK,MAAO6C,EAAW7C,EAAK,KAAK,EAC3EvC,EAAQ,OAAS,KAAK,IAAIA,EAAQ,QAAUuC,EAAK,OAAQ4C,EAAY5C,EAAK,MAAM,EAChFvC,EAAQ,OAASA,EAAQ,QAAUuC,EAAK,cACxCmC,EAAc,OAAO1E,CAAO,EAC5BkC,EAAgB,YAAYmC,EAAUrE,EAASuC,CAAI,CACrD,CACAmC,EAAc,eAAenC,EAAK,aAAa,CACjD,EAAG,kBAAkB,EACjB+C,EAAQ,KAAM,CAChB,MAAO,CACL7U,EAAO,KAAM,OAAO,CACtB,CACA,YAAYoO,EAAGC,EAAG,CAChB,KAAK,EAAID,EACT,KAAK,EAAIC,CACX,CACF,EACIyG,GAAoC9U,EAAO,SAAS+U,EAAUC,EAAU,CAC1E,IAAIC,EAAKF,EAAS,EACdG,EAAKH,EAAS,EACdI,EAAKH,EAAS,EACdI,EAAKJ,EAAS,EACdK,EAAcJ,EAAKF,EAAS,MAAQ,EACpCO,EAAcJ,EAAKH,EAAS,OAAS,EACrCQ,EAAK,KAAK,IAAIN,EAAKE,CAAE,EACrB/D,EAAK,KAAK,IAAI8D,EAAKE,CAAE,EACrBI,EAASpE,EAAKmE,EACdE,EAAUV,EAAS,OAASA,EAAS,MACrCW,EAAc,KAClB,OAAIR,GAAME,GAAMH,EAAKE,EACnBO,EAAc,IAAIb,EAAMI,EAAKF,EAAS,MAAOO,CAAW,EAC/CJ,GAAME,GAAMH,EAAKE,EAC1BO,EAAc,IAAIb,EAAMI,EAAIK,CAAW,EAC9BL,GAAME,GAAMD,EAAKE,EAC1BM,EAAc,IAAIb,EAAMQ,EAAaH,EAAKH,EAAS,MAAM,EAChDE,GAAME,GAAMD,EAAKE,IAC1BM,EAAc,IAAIb,EAAMQ,EAAaH,CAAE,GAErCD,EAAKE,GAAMD,EAAKE,EACdK,GAAWD,EACbE,EAAc,IAAIb,EAAMI,EAAIK,EAAcE,EAAST,EAAS,MAAQ,CAAC,EAErEW,EAAc,IAAIb,EAChBQ,EAAcE,EAAKnE,EAAK2D,EAAS,OAAS,EAC1CG,EAAKH,EAAS,MAChB,EAEOE,EAAKE,GAAMD,EAAKE,EACrBK,GAAWD,EACbE,EAAc,IAAIb,EAAMI,EAAKF,EAAS,MAAOO,EAAcE,EAAST,EAAS,MAAQ,CAAC,EAEtFW,EAAc,IAAIb,EAChBQ,EAAcE,EAAKnE,EAAK2D,EAAS,OAAS,EAC1CG,EAAKH,EAAS,MAChB,EAEOE,EAAKE,GAAMD,EAAKE,EACrBK,GAAWD,EACbE,EAAc,IAAIb,EAAMI,EAAKF,EAAS,MAAOO,EAAcE,EAAST,EAAS,MAAQ,CAAC,EAEtFW,EAAc,IAAIb,EAAMQ,EAAcN,EAAS,OAAS,EAAIQ,EAAKnE,EAAI8D,CAAE,EAEhED,EAAKE,GAAMD,EAAKE,IACrBK,GAAWD,EACbE,EAAc,IAAIb,EAAMI,EAAIK,EAAcP,EAAS,MAAQ,EAAIS,CAAM,EAErEE,EAAc,IAAIb,EAAMQ,EAAcN,EAAS,OAAS,EAAIQ,EAAKnE,EAAI8D,CAAE,GAGpEQ,CACT,EAAG,mBAAmB,EAClBC,GAAqC3V,EAAO,SAAS+U,EAAUa,EAAS,CAC1E,IAAIC,EAAoB,CAAE,EAAG,EAAG,EAAG,CAAE,EACrCA,EAAkB,EAAID,EAAQ,EAAIA,EAAQ,MAAQ,EAClDC,EAAkB,EAAID,EAAQ,EAAIA,EAAQ,OAAS,EACnD,IAAIE,EAAahB,GAAkBC,EAAUc,CAAiB,EAC9DA,EAAkB,EAAId,EAAS,EAAIA,EAAS,MAAQ,EACpDc,EAAkB,EAAId,EAAS,EAAIA,EAAS,OAAS,EACrD,IAAIC,EAAWF,GAAkBc,EAASC,CAAiB,EAC3D,MAAO,CAAE,WAAAC,EAAY,SAAAd,CAAS,CAChC,EAAG,oBAAoB,EACnBe,GAA4B/V,EAAO,SAAS4T,EAAUnF,EAAOuH,EAAehE,EAAS,CACvF,IAAI1K,EAAI,EACR,QAASkC,KAAOiF,EAAO,CACrBnH,EAAIA,EAAI,EACR,IAAI2O,EAAczM,EAAI,MAAQsI,EAAK,KAC/BoE,EAAUjD,GAAYnB,CAAI,EACZE,EAAQ,GAAG,UAAU,IACnB,cAClBxI,EAAI,MAAM,KAAOlC,EAAI,KAAOkC,EAAI,MAAM,MAExC,IAAI8J,EAAiBI,GAAmBlK,EAAI,MAAM,KAAM0M,CAAO,EAC/DhD,EAAkB,QAAS1J,EAAKyM,EAAaC,EAAS5C,CAAc,EAChE9J,EAAI,OAASA,EAAI,MAAM,OAAS,KAClC8J,EAAiBI,GAAmBlK,EAAI,MAAM,KAAM0M,CAAO,EAC3DhD,EAAkB,QAAS1J,EAAKyM,EAAaC,EAAS5C,CAAc,GAElE9J,EAAI,OAASA,EAAI,MAAM,OAAS,KAClC8J,EAAiBI,GAAmBlK,EAAI,MAAM,KAAM0M,CAAO,EAC3DhD,EAAkB,QAAS1J,EAAKyM,EAAaC,EAAS5C,CAAc,GAEtE,IAAIyB,EAAWiB,EAAcxM,EAAI,IAAI,EACjCoM,EAAUI,EAAcxM,EAAI,EAAE,EAC9B2M,EAASR,GAAmBZ,EAAUa,CAAO,EACjDpM,EAAI,WAAa2M,EAAO,WACxB3M,EAAI,SAAW2M,EAAO,QACxB,CACA1E,EAAgB,SAASmC,EAAUnF,EAAOqD,CAAI,CAChD,EAAG,UAAU,EACb,SAASsE,GAAmBxC,EAAUyC,EAAqBC,EAAcC,EAAmBvE,EAAS,CACnG,IAAIiC,EAAgB,IAAIlC,GAAOC,CAAO,EACtCiC,EAAc,KAAK,WAAaqC,EAAa,KAAK,WAAa,KAAK,IAAIzE,GAAkB0E,EAAkB,MAAM,EAClH,OAAS,CAACjP,EAAGkP,CAAe,IAAKD,EAAkB,QAAQ,EAAG,CAC5D,IAAInC,EAAI,EACRoC,EAAgB,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,CAAE,EAChDA,EAAgB,SAClBA,EAAgB,MAAM,MAAQ,GAC9BA,EAAgB,MAAM,OAAS,GAC/BA,EAAgB,MAAM,EAAIpC,EAC1BA,EAAIoC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,QAEtD,IAAIC,EAA0BD,EAAgB,MAAQ1E,EAAK,KACvD4E,EAA2B1D,GAAalB,CAAI,EAYhD,GAXA4E,EAAyB,SAAWA,EAAyB,SAAW,EACxEA,EAAyB,WAAa,OACtCxD,EACE,QACAsD,EACAC,EACAC,EACAzC,EAAc,KAAK,UACrB,EACAuC,EAAgB,MAAM,EAAIpC,EAAI,EAC9BA,EAAIoC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,OAChDA,EAAgB,MAAQA,EAAgB,KAAK,OAAS,GAAI,CAC5DA,EAAgB,KAAK,KAAO,IAAMA,EAAgB,KAAK,KAAO,IAC9D,IAAIG,EAA0B3D,GAAalB,CAAI,EAC/CoB,EACE,OACAsD,EACAC,EACAE,EACA1C,EAAc,KAAK,UACrB,EACAuC,EAAgB,KAAK,EAAIpC,EAAI,EAC7BA,EAAIoC,EAAgB,KAAK,EAAIA,EAAgB,KAAK,MACpD,CACA,GAAIA,EAAgB,OAASA,EAAgB,MAAM,OAAS,GAAI,CAC9D,IAAII,EAA2B5D,GAAalB,CAAI,EAChD8E,EAAyB,SAAWA,EAAyB,SAAW,EACxE1D,EACE,QACAsD,EACAC,EACAG,EACA3C,EAAc,KAAK,UACrB,EACAuC,EAAgB,MAAM,EAAIpC,EAAI,GAC9BA,EAAIoC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,MACtD,CACA,GAAIlP,GAAK,GAAKA,EAAIuK,KAAqB,EAAG,CACxC,IAAIgF,EAAKP,EAAa,KAAK,OAASxE,EAAK,eACrCgF,EAAKR,EAAa,KAAK,MAAQxE,EAAK,eAAiBsC,EACzDH,EAAc,QAAQ4C,EAAIA,EAAIC,EAAIA,CAAE,CACtC,KAAO,CACL,IAAID,EAAK5C,EAAc,KAAK,QAAUA,EAAc,KAAK,OAASA,EAAc,KAAK,MAAQnC,EAAK,eAAiBmC,EAAc,KAAK,OAClI6C,EAAK7C,EAAc,KAAK,OAC5BA,EAAc,QAAQ4C,EAAIA,EAAIC,EAAIA,CAAE,CACtC,CACA7C,EAAc,KAAOuC,EAAgB,MACrC,IAAIO,EAA6B/E,EAAQ,GAAG,gBAAgBwE,EAAgB,KAAK,EAC7EQ,EAA4BhF,EAAQ,GAAG,eAAewE,EAAgB,KAAK,EAC3EQ,EAA0B,OAAS,GACrChD,GACEC,EACAL,EACAmD,EACAC,CACF,EAEFX,EAAsBG,EAAgB,MACtC,IAAIS,EAAwBjF,EAAQ,GAAG,aAAaqE,CAAmB,EACnEY,EAAsB,OAAS,GACjCb,GACExC,EACAyC,EACApC,EACAgD,EACAjF,CACF,EAEEwE,EAAgB,QAAU,UAC5B7C,GAAcC,EAAU4C,EAAiBvC,CAAa,EAExDqC,EAAa,KAAK,MAAQ,KAAK,IAC7BrC,EAAc,KAAK,MAAQnC,EAAK,cAChCwE,EAAa,KAAK,KACpB,EACAA,EAAa,KAAK,MAAQ,KAAK,IAC7BrC,EAAc,KAAK,MAAQnC,EAAK,cAChCwE,EAAa,KAAK,KACpB,EACA5E,GAAqB,KAAK,IAAIA,GAAoB4E,EAAa,KAAK,KAAK,EACzE3E,GAAqB,KAAK,IAAIA,GAAoB2E,EAAa,KAAK,KAAK,CAC3E,CACF,CACAtW,EAAOoW,GAAoB,oBAAoB,EAC/C,IAAIc,GAAuBlX,EAAO,SAASmX,EAAOC,EAAIC,EAAUrF,EAAS,CACvEF,EAAOjJ,GAAU,EAAE,GACnB,IAAMyO,EAAgBzO,GAAU,EAAE,cAC9B0O,EACAD,IAAkB,YACpBC,EAAiBC,GAAO,KAAOJ,CAAE,GAEnC,IAAMK,EAAOH,IAAkB,UAAYE,GAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EAAIC,GAAO,MAAM,EAC7GE,EAAK1F,EAAQ,GACjBA,EAAQ,GAAG,QAAQF,EAAK,IAAI,EAC5BF,GAAgB8F,EAAG,gBAAgB,EACnC7F,GAAmB6F,EAAG,mBAAmB,EACzCC,GAAI,MAAM,KAAK,KAAK,UAAU7F,EAAM,KAAM,CAAC,CAAC,EAAE,EAC9C,IAAM8B,EAAW0D,IAAkB,UAAYG,EAAK,OAAO,QAAQL,CAAE,IAAI,EAAII,GAAO,QAAQJ,CAAE,IAAI,EAClG3F,EAAgB,mBAAmBmC,CAAQ,EAC3CnC,EAAgB,mBAAmBmC,CAAQ,EAC3CnC,EAAgB,gBAAgBmC,CAAQ,EACxC,IAAIgE,EAAe,IAAI7F,GAAOC,CAAO,EACrC4F,EAAa,QACX9F,EAAK,eACLA,EAAK,eACLA,EAAK,eACLA,EAAK,cACP,EACA8F,EAAa,KAAK,WAAa,OAAO,WACtClG,GAAqBI,EAAK,eAC1BH,GAAqBG,EAAK,eAC1B,IAAM+F,EAAS7F,EAAQ,GAAG,SAAS,EAC/BuE,EAAoBvE,EAAQ,GAAG,aAAa,EAAE,EAClDoE,GAAmBxC,EAAU,GAAIgE,EAAcrB,EAAmBvE,CAAO,EACzEP,EAAgB,gBAAgBmC,CAAQ,EACxCnC,EAAgB,eAAemC,CAAQ,EACvCnC,EAAgB,qBAAqBmC,CAAQ,EAC7CnC,EAAgB,sBAAsBmC,CAAQ,EAC9CmC,GAAUnC,EAAU5B,EAAQ,GAAG,QAAQ,EAAGA,EAAQ,GAAG,WAAYA,CAAO,EACxE4F,EAAa,KAAK,MAAQlG,GAC1BkG,EAAa,KAAK,MAAQjG,GAC1B,IAAMmG,EAAMF,EAAa,KAErBzJ,EADY2J,EAAI,MAAQA,EAAI,OACP,EAAIhG,EAAK,eAE5B5D,EADS4J,EAAI,MAAQA,EAAI,OACN,EAAIhG,EAAK,eAC9B+F,GACFjE,EAAS,OAAO,MAAM,EAAE,KAAKiE,CAAM,EAAE,KAAK,KAAMC,EAAI,MAAQA,EAAI,QAAU,EAAI,EAAIhG,EAAK,cAAc,EAAE,KAAK,IAAKgG,EAAI,OAAShG,EAAK,cAAc,EAEnJiG,GAAiBnE,EAAUzF,EAAQD,EAAO4D,EAAK,WAAW,EAC1D,IAAMkG,EAAoBH,EAAS,GAAK,EACxCjE,EAAS,KACP,UACAkE,EAAI,OAAShG,EAAK,eAAiB,MAAQA,EAAK,eAAiBkG,GAAqB,IAAM9J,EAAQ,KAAOC,EAAS6J,EACtH,EACAL,GAAI,MAAM,UAAWG,CAAG,CAC1B,EAAG,MAAM,EACLG,GAAqB,CACvB,wBAAyBjE,GACzB,aAAcL,GACd,QAAA1B,GACA,KAAAiF,EACF,EAGIgB,GAA4BlY,EAAQmY,GAAY;AAAA,cACtCA,EAAQ,YAAY;AAAA,YACtBA,EAAQ,SAAS;AAAA;AAAA,EAE1B,WAAW,EACVC,GAAiBF,GAGjBG,GAAU,CACZ,OAAQxQ,GACR,GAAI2F,GACJ,SAAUyK,GACV,OAAQG,GACR,KAAsBpY,EAAO,CAAC,CAAE,GAAAsY,EAAI,KAAAC,CAAK,IAAM,CAC7CN,GAAmB,QAAQK,CAAE,EAC7B9K,GAAa,QAAQ+K,CAAI,CAC3B,EAAG,MAAM,CACX", "names": ["import_sanitize_url", "parser", "o", "__name", "k", "v", "o2", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "kv", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer2", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "next", "pre", "c2", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "c4Diagram_default", "c4ShapeArray", "boundaryParseStack", "currentBoundaryParse", "parentBoundaryParse", "boundaries", "rels", "title", "wrapEnabled", "c4ShapeInRow", "c4BoundaryInRow", "c4Type", "getC4Type", "setC4Type", "c4TypeParam", "sanitizeText", "getConfig2", "addRel", "type", "from", "to", "label", "techn", "descr", "sprite", "tags", "link", "rel", "old", "rel2", "key", "value", "autoWrap", "addPersonOrSystem", "typeC4Shape", "alias", "personOrSystem", "personOrSystem2", "addContainer", "container", "container2", "addComponent", "component", "component2", "addPersonOrSystemBoundary", "boundary", "boundary2", "addContainerBoundary", "addDeploymentNode", "nodeType", "popBoundaryParseStack", "updateElStyle", "elementName", "bgColor", "fontColor", "borderColor", "shadowing", "shape", "legendText", "legendSprite", "element", "updateRelStyle", "textColor", "lineColor", "offsetX", "offsetY", "updateLayoutConfig", "c4ShapeInRowParam", "c4BoundaryInRowParam", "c4ShapeInRowValue", "c4BoundaryInRowValue", "getC4ShapeInRow", "getC4BoundaryInRow", "getCurrentBoundaryParse", "getParentBoundaryParse", "getC4ShapeArray", "parentBoundary", "getC4Shape", "getC4ShapeKeys", "getBoundaries", "getBoundarys", "getRels", "getTitle", "setWrap", "wrapSetting", "clear", "LINETYPE", "ARROWTYPE", "PLACEMENT", "setTitle", "txt", "c4Db_default", "setAccTitle", "getAccTitle", "getAccDescription", "setAccDescription", "drawRect2", "elem", "rectData", "drawRect", "drawImage", "width", "height", "x", "y", "imageElem", "sanitizedLink", "drawRels", "rels2", "conf2", "relsElem", "strokeColor", "url", "line", "messageConf", "_drawTextCandidateFunc", "drawBoundary", "boundaryElem", "fillColor", "attrsValue", "boundaryConf", "drawC4Shape", "c4Shape", "personImg", "c4ShapeElem", "rect", "getNoteRect", "c4ShapeFontConf", "getC4ShapeFont", "textFontConf", "insertDatabaseIcon", "insertComputerIcon", "insertClockIcon", "insertArrowHead", "insertArrowEnd", "insertArrowFilledHead", "insertDynamicNumber", "insertArrowCrossHead", "marker", "cnf", "byText", "content", "g", "textAttrs", "text", "_setTextAttrs", "byTspan", "fontSize", "fontFamily", "fontWeight", "common_default", "dy", "byFo", "s", "toText", "fromTextAttrsDict", "svgDraw_default", "globalBoundaryMaxX", "globalBoundaryMaxY", "c4ShapeInRow2", "c4BoundaryInRow2", "conf", "Bounds", "diagObj", "setConf", "startx", "stopx", "starty", "stopy", "obj", "val", "fun", "_startx", "_stopx", "_starty", "_stopy", "margin", "assignWithDepth_default", "c4ShapeFont", "boundaryFont", "messageFont", "calcC4ShapeTextWH", "textType", "c4ShapeTextWrap", "textConf", "textLimitWidth", "wrapLabel", "calculateTextHeight", "lineHeight", "calculateTextWidth", "drawBoundary2", "diagram2", "bounds", "boundaryTextWrap", "boundaryLabelConf", "drawC4ShapeArray", "currentBounds", "c4ShapeArray2", "c4Shape<PERSON>eys", "Y", "c4Shape<PERSON>ey", "c4ShapeTypeConf", "c4ShapeLabelConf", "c4ShapeTypeConf2", "c4ShapeTechnConf", "rectHeight", "rectWidth", "c4ShapeDescrConf", "Point", "getIntersectPoint", "fromNode", "endPoint", "x1", "y1", "x2", "y2", "fromCenterX", "fromCenterY", "dx", "tanDYX", "fromDYX", "returnPoint", "getIntersectPoints", "endNode", "endIntersectPoint", "startPoint", "drawRels2", "getC4ShapeObj", "relTextWrap", "rel<PERSON>onf", "points", "drawInsideBoundary", "parentBoundaryAlias", "parentBounds", "currentBoundaries", "currentBoundary", "currentBoundaryTextWrap", "currentBoundaryLabelConf", "currentBoundaryTypeConf", "currentBoundaryDescrConf", "_x", "_y", "currentPersonOrSystemArray", "currentPersonOrSystemKeys", "nextCurrentBoundaries", "draw", "_text", "id", "_version", "securityLevel", "sandboxElement", "select_default", "root", "db", "log", "screenBounds", "title2", "box", "configureSvgSize", "extraVertForTitle", "c4Renderer_default", "getStyles", "options", "styles_default", "diagram", "c4", "wrap"]}