### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType
  commentId: T:DrawnUi.Draw.SkiaControl.CacheValidityType
  id: SkiaControl.CacheValidityType
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.SkiaControl.CacheValidityType.GraphicContextMismatch
  - DrawnUi.Draw.SkiaControl.CacheValidityType.Missing
  - DrawnUi.Draw.SkiaControl.CacheValidityType.SizeMismatch
  - DrawnUi.Draw.SkiaControl.CacheValidityType.Valid
  langs:
  - csharp
  - vb
  name: SkiaControl.CacheValidityType
  nameWithType: SkiaControl.CacheValidityType
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Cache.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CacheValidityType
    path: ../src/Shared/Draw/Base/SkiaControl.Cache.cs
    startLine: 636
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum SkiaControl.CacheValidityType
    content.vb: Public Enum SkiaControl.CacheValidityType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType.Valid
  commentId: F:DrawnUi.Draw.SkiaControl.CacheValidityType.Valid
  id: Valid
  parent: DrawnUi.Draw.SkiaControl.CacheValidityType
  langs:
  - csharp
  - vb
  name: Valid
  nameWithType: SkiaControl.CacheValidityType.Valid
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType.Valid
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Cache.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Valid
    path: ../src/Shared/Draw/Base/SkiaControl.Cache.cs
    startLine: 638
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Valid = 0
    return:
      type: DrawnUi.Draw.SkiaControl.CacheValidityType
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType.Missing
  commentId: F:DrawnUi.Draw.SkiaControl.CacheValidityType.Missing
  id: Missing
  parent: DrawnUi.Draw.SkiaControl.CacheValidityType
  langs:
  - csharp
  - vb
  name: Missing
  nameWithType: SkiaControl.CacheValidityType.Missing
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType.Missing
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Cache.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Missing
    path: ../src/Shared/Draw/Base/SkiaControl.Cache.cs
    startLine: 639
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Missing = 1
    return:
      type: DrawnUi.Draw.SkiaControl.CacheValidityType
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType.SizeMismatch
  commentId: F:DrawnUi.Draw.SkiaControl.CacheValidityType.SizeMismatch
  id: SizeMismatch
  parent: DrawnUi.Draw.SkiaControl.CacheValidityType
  langs:
  - csharp
  - vb
  name: SizeMismatch
  nameWithType: SkiaControl.CacheValidityType.SizeMismatch
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType.SizeMismatch
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Cache.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SizeMismatch
    path: ../src/Shared/Draw/Base/SkiaControl.Cache.cs
    startLine: 640
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: SizeMismatch = 2
    return:
      type: DrawnUi.Draw.SkiaControl.CacheValidityType
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType.GraphicContextMismatch
  commentId: F:DrawnUi.Draw.SkiaControl.CacheValidityType.GraphicContextMismatch
  id: GraphicContextMismatch
  parent: DrawnUi.Draw.SkiaControl.CacheValidityType
  langs:
  - csharp
  - vb
  name: GraphicContextMismatch
  nameWithType: SkiaControl.CacheValidityType.GraphicContextMismatch
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType.GraphicContextMismatch
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Cache.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GraphicContextMismatch
    path: ../src/Shared/Draw/Base/SkiaControl.Cache.cs
    startLine: 641
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: GraphicContextMismatch = 3
    return:
      type: DrawnUi.Draw.SkiaControl.CacheValidityType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaControl.CacheValidityType
  commentId: T:DrawnUi.Draw.SkiaControl.CacheValidityType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl.CacheValidityType
  nameWithType: SkiaControl.CacheValidityType
  fullName: DrawnUi.Draw.SkiaControl.CacheValidityType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.CacheValidityType
    name: CacheValidityType
    href: DrawnUi.Draw.SkiaControl.CacheValidityType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.CacheValidityType
    name: CacheValidityType
    href: DrawnUi.Draw.SkiaControl.CacheValidityType.html
