{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-EJ4ZWXGL.mjs"], "sourcesContent": ["import {\n  __name,\n  getConfig2 as getConfig\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/rendering-util/selectSvgElement.ts\nimport { select } from \"d3\";\nvar selectSvgElement = /* @__PURE__ */ __name((id) => {\n  const { securityLevel } = getConfig();\n  let root = select(\"body\");\n  if (securityLevel === \"sandbox\") {\n    const sandboxElement = select(`#i${id}`);\n    const doc = sandboxElement.node()?.contentDocument ?? document;\n    root = select(doc.body);\n  }\n  const svg = root.select(`#${id}`);\n  return svg;\n}, \"selectSvgElement\");\n\nexport {\n  selectSvgElement\n};\n"], "mappings": "2DAOA,IAAIA,EAAmCC,EAAQC,GAAO,CACpD,GAAM,CAAE,cAAAC,CAAc,EAAIC,EAAU,EAChCC,EAAOC,EAAO,MAAM,EACxB,GAAIH,IAAkB,UAAW,CAE/B,IAAMI,EADiBD,EAAO,KAAKJ,CAAE,EAAE,EACZ,KAAK,GAAG,iBAAmB,SACtDG,EAAOC,EAAOC,EAAI,IAAI,CACxB,CAEA,OADYF,EAAK,OAAO,IAAIH,CAAE,EAAE,CAElC,EAAG,kBAAkB", "names": ["selectSvgElement", "__name", "id", "securityLevel", "getConfig2", "root", "select_default", "doc"]}