<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>News Feed Scroller Tutorial | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="News Feed Scroller Tutorial | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="When MAUI CollectionView is not enough.. Think Drawn! DrawnUI News Feed Scroller Tutorial.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/drawnui/blob/master/docs/articles/news-feed-tutorial.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="news-feed-scroller-tutorial">News Feed Scroller Tutorial</h1>

<p>When .NET MAUI CollectionView is not enough.. Think Drawn!<br>
We will be building a news feed scroller with mixed content: text posts, images, videos, articles, ads: an infinite scroll of recycled cells with LoadMore mechanics.</p>
<h2 id="-this-tutorial-features">🚀 This Tutorial Features:</h2>
<ul>
<li><strong>📏 Uneven row heights</strong> - because real content isn't uniform!</li>
<li><strong>✨ Shadows behind cells</strong> - adds visual depth to the interface</li>
<li><strong>🌐 Real internet images</strong> for avatars and banners from actual APIs</li>
<li><strong>📊 Large dataset handling</strong> - measures only visible items at startup, then works in background</li>
<li><strong>♾️ Load more functionality</strong> - you never know how far users will scroll!</li>
</ul>
<img src="../images/scroller.jpg" alt="News Feed Tutorial" width="350" style="margin-top: 16px;">
<p>Want to see this in action first? Check out the <a href="https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Tutorials"><strong>DrawnUI Tutorials Project</strong></a><br>
Clone the repo and run the Tutorials project to explore all examples!</p>
<h2 id="-what-youll-learn">🎓 What You'll Learn:</h2>
<ul>
<li><strong>🏗️ Smart caching strategies</strong> - organize layers to redraw only what changed</li>
<li><strong>⚡ Performance optimization</strong> - handle thousands of items smoothly</li>
<li><strong>🔄 Recycling mastery</strong> - one cell type handles all content variations</li>
<li><strong>📱 DrawnUI nuances</strong> - real-world techniques for building performant UIs</li>
</ul>
<h2 id="-what-we-want-to-build">🎯 What We Want to Build</h2>
<p>A news feed with mixed content types (text posts, images, videos, articles, ads). We will be using a combination of <code>SkiaScroll</code> and <code>SkiaLayout</code> to obtain a recycled cells scrolling view. We will also use <code>SkiaDynamicDrawnCell</code> custom control as our cell base. This is optional - you could use any <code>SkiaControl</code> as your cell, but it's a helpful utility for handling BindingContext changes smoothly and provides useful override methods.</p>
<h2 id="-the-tech-behind">⚙️ The Tech Behind</h2>
<p><code>SkiaScroll</code> can scroll any content. When paired with a <code>SkiaLayout</code> it can communicate the viewport size/position to its child and retrieve some information back. With special properties <code>SkiaLayout</code> can act like a bindable item layout, and inside <code>SkiaScroll</code> it can show its full potential with recycling and virtualization! 💪</p>
<p>So what we will do is simply placing a SkiaLayout inside the scroll, defining an ItemTemplate and ItemsSource, plus setting some related properties.</p>
<p>Another important point is the databinding for the recycled view - the cell. We'll do it in code-behind for better performance. <code>SkiaDynamicDrawnCell</code> helper provides us with a <code>SetContent</code> method we can override to update the cell content based on the new BindingContext. This code is wrapped by the helper with a batch update lock, so no intermediate rendering happens. We could also override <code>ContextPropertyChanged</code> if we wanted to react to property changes in the bound object (for example <code>IsOnline</code> changing for a person and updating the avatar color to green), but we'll keep this tutorial simple.</p>
<p>We will be using real internet resources to get images for avatars and banners to be realistic with performance. We'll also be using shadow effects for visual appeal.
You can display debugging information over the scroll to see displayed/created/measured number of cells along with FPS.</p>
<p>With DrawnUI, we can use a layout as a cell that simply shows or hides elements based on content type - no complex <code>DataTemplateSelector</code> needed! Recycling and height calculation happen automatically ✨</p>
<h2 id="performance-key-requirements">Performance Key Requirements</h2>
<h3 id="stack-optimisations"><strong>Stack Optimisations</strong></h3>
<p>Let's look at critical SkiaLayout properties for this scenario:</p>
<p><code> MeasureItemsStrategy=&quot;MeasureVisible&quot;</code></p>
<p>this <strong>experimental</strong> measurement strategy for <code>SkiaLayout</code> works well for large lists with uneven rows. It measures only visible items initially, then progressively measures off-screen items in the background. This can provide good scrolling performance with thousands of items of varying heights. At the</p>
<p><code>ReserveTemplates=&quot;10&quot;</code></p>
<p>The layout views adapter creates new istances of cells only when needed. When a new one is instantiated this can create a UI lag spike. This property indicates that we want it to pre-create a specifc number of cells, to avoid a potential lag spike when the user just starts scrolling and new cells are created. This would not be needed for &quot;same size&quot; type of rows, but for &quot;uneven rows&quot; adapter tries to have some reasonable number of cells for different heights to return appropriate one from the pool when requested.</p>
<p><code>VirtualisationInflated=&quot;200&quot;</code></p>
<p>We are drawing only cells visible inside scrolling viewport, but with double-buffered cache we want cells to start rendering before they enter the viewport, to avoid seing unrendered content. This property defines how much of the hidden content out of visible bounds should be considered visible for rendering.</p>
<h3 id="scroll-optimisations"><strong>Scroll Optimisations</strong></h3>
<p>Let's take a look what spices we added to ou scroll:</p>
<p><code>LoadMoreOffset=&quot;500&quot;</code></p>
<p>It would ask content's permission to execute LoadMoreCommand by calling <code>IInsideViewport.ShouldTriggerLoadMore</code> when the user scrolls within 500 points (not pixels) of the end of the content. This allows our stack to make a decision about when to load more data, more spicifically it would allow it only if the background measurement of the existing content ended.</p>
<p><code>FrictionScrolled</code> and <code>ChangeVelocityScrolled</code></p>
<p>Notice we customized scrolling to stop faster with <code>FrictionScrolled</code> for news feed case were user would read content but help kick swipes with <code>ChangeVelocityScrolled</code>.</p>
<h3 id="layering"><strong>Layering</strong></h3>
<p>When designed a drawn UI, it's important to think about layering and caching. We know that there would be a static layer with unchanged data, and one that would be redrawn when something changes, for example image gets loaded from internet. In such case we would want to fast-draw static layer from cache and rebuild the dynamic one.  Our background has a shadow effect, so we cache it into a separate layer with <code>SkiaShape</code> and draw content on top. If you would want to clip your content with the shape form your would just need to wrap it with a shape if same parameters than the background layer.</p>
<pre><code class="lang-xml">    &lt;!--cached background layer with shadow--&gt;
    &lt;draw:SkiaLayout
        UseCache=&quot;Image&quot;
        VerticalOptions=&quot;Fill&quot;
        HorizontalOptions=&quot;Fill&quot;
        x:Name=&quot;BackgroundLayer&quot;
        Padding=&quot;16,6,16,10&quot;&gt;
        &lt;draw:SkiaShape
            CornerRadius=&quot;0&quot;
            BackgroundColor=&quot;White&quot;
            VerticalOptions=&quot;Fill&quot;
            HorizontalOptions=&quot;Fill&quot;&gt;
            &lt;draw:SkiaShape.VisualEffects&gt;
                &lt;draw:DropShadowEffect
                    Color=&quot;#33000000&quot; Blur=&quot;3&quot; X=&quot;3&quot; Y=&quot;3&quot; /&gt;
            &lt;/draw:SkiaShape.VisualEffects&gt;
        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaLayout&gt;

    &lt;!--content layer goes here--&gt;   
</code></pre>
<h3 id="loadmore-implementation"><strong>LoadMore Implementation</strong></h3>
<p>We want to load data by chunks when the user scrolls, and append them to the existing collection, creating a potentially infinite scroll.<br>
We use an <code>ObservableRangeCollection</code> to hold our news items. This allows us to change collection (UI thread is needed for that) in the middle of the scrolling without resetting the ItemsSource, the stack would pick up our changes automatically.</p>
<h3 id="-implementation">📱 Implementation</h3>
<p>Proceed as described in the <a href="getting-started.html">Getting Started</a> section. When working on desktop you'll normally want to set your app window to a phone-like size, to be consistent with mobile platforms:</p>
<pre><code class="lang-csharp">        .UseDrawnUi(new DrawnUiStartupSettings
        {
            DesktopWindow = new()
            {
                Width = 375,
                Height = 800
            }
        })
</code></pre>
<h3 id="define-content-types">Define Content Types</h3>
<p>We have several possible feed types, we handle all of them with one model. Notice that we didn't implement INotifyPropertyChanged for this example. If your app is updating already existing cells at runtime, for example changing <code>IsUnread</code> for a feed or <code>IsOnline</code> for avatar, you would need to implement it and then override <code>ContextPropertyChanged</code> inside the cell to reflect dynamic changes in model to your UI.</p>
<pre><code class="lang-csharp">public enum NewsType
{
    Text,
    Image,
    Video,
    Article,
    Ad
}

public class NewsItem
{
    public long Id { get; set; }
    public NewsType Type { get; set; }
    public string Title { get; set; }
    public string Content { get; set; }
    public string ImageUrl { get; set; }
    public string VideoUrl { get; set; }
    public string ArticleUrl { get; set; }
    public string AuthorName { get; set; }
    public string AuthorAvatarUrl { get; set; }
    public DateTime PublishedAt { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
}
</code></pre>
<h3 id="-scroll-and-stack">🪽 Scroll and Stack</h3>
<p>Thise are friends when it come to creating recycled or &quot;bindablelayout-like&quot; scenario. They interact via <code>IInsideViewport</code> interface that content could implement and is implementing in case of <code>SkiaLayout</code>:</p>
<pre><code class="lang-xml">                &lt;draw:SkiaScroll
                    x:Name=&quot;NewsScroll&quot;
                    Orientation=&quot;Vertical&quot;
                    FrictionScrolled=&quot;0.5&quot;
                    ChangeVelocityScrolled=&quot;1.35&quot;
                    RefreshCommand=&quot;{Binding RefreshCommand}&quot;
                    LoadMoreCommand=&quot;{Binding LoadMoreCommand}&quot;
                    RefreshEnabled=&quot;True&quot;
                    HorizontalOptions=&quot;Fill&quot;
                    VerticalOptions=&quot;Fill&quot;&gt;

                    &lt;draw:SkiaScroll.Header&gt;

                        &lt;draw:SkiaLayer HeightRequest=&quot;40&quot; UseCache=&quot;Image&quot;&gt;
                            &lt;draw:SkiaRichLabel
                                Text=&quot;DrawnUI News Feed Tutorial&quot;
                                HorizontalOptions=&quot;Center&quot; VerticalOptions=&quot;Center&quot; /&gt;
                        &lt;/draw:SkiaLayer&gt;

                    &lt;/draw:SkiaScroll.Header&gt;

                    &lt;draw:SkiaScroll.Footer&gt;

                        &lt;draw:SkiaLayer HeightRequest=&quot;50&quot; /&gt;

                    &lt;/draw:SkiaScroll.Footer&gt;

                    &lt;draw:SkiaLayout
                        x:Name=&quot;NewsStack&quot;
                        Type=&quot;Column&quot;
                        ItemsSource=&quot;{Binding NewsItems}&quot;
                        RecyclingTemplate=&quot;Enabled&quot;
                        MeasureItemsStrategy=&quot;MeasureVisible&quot;
                        ReserveTemplates=&quot;10&quot;
                        VirtualisationInflated=&quot;200&quot;
                        Spacing=&quot;0&quot;
                        ItemTemplateType=&quot;{x:Type newsFeed:NewsCell}&quot;
                        HorizontalOptions=&quot;Fill&quot; /&gt;

                &lt;/draw:SkiaScroll&gt;
</code></pre>
<h3 id="-create-your-cell">🏗️ Create Your Cell</h3>
<blockquote>
<p><strong>Caching Strategy Note</strong>: For recycled cells <code>UseCache=&quot;ImageDoubleBuffered&quot;</code> is a must - it displays the previous cache while the next one is being prepared in background, allowing smooth scrolling. It supports painting placeholders when no cache is available at all.</p>
</blockquote>
<blockquote>
<p><strong>Shadow Performance</strong>: Shadows are cached in a separate background layer to avoid performance issues. The shadow layer is cached independently from the content.</p>
</blockquote>
<blockquote>
<p><strong>Spacing Strategy</strong>: Stack spacing is set to 0 because the cell margin/padding acts as general spacing between items. If we had no special layer for saving background with shadows you could use Spacing normally, but we need that space for shadows.</p>
</blockquote>
<pre><code class="lang-xml">&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;

&lt;draw:SkiaDynamicDrawnCell
    x:Class=&quot;DrawnUI.Tutorials.NewsFeed.NewsCell&quot;
    xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
    xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
    xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
    xmlns:models=&quot;clr-namespace:DrawnUI.Tutorials.NewsFeed.Models&quot;
    HorizontalOptions=&quot;Fill&quot;
    x:DataType=&quot;models:NewsItem&quot;
    UseCache=&quot;ImageDoubleBuffered&quot;&gt;

    &lt;!--cached background layer with shadow--&gt;
    &lt;draw:SkiaLayout
        VerticalOptions=&quot;Fill&quot;
        HorizontalOptions=&quot;Fill&quot;
        UseCache=&quot;Image&quot;
        x:Name=&quot;BackgroundLayer&quot;
        Padding=&quot;16,6,16,10&quot;&gt;

        &lt;draw:SkiaShape
            CornerRadius=&quot;0&quot;
            BackgroundColor=&quot;White&quot;
            VerticalOptions=&quot;Fill&quot;
            HorizontalOptions=&quot;Fill&quot;&gt;

            &lt;draw:SkiaShape.VisualEffects&gt;
                &lt;draw:DropShadowEffect
                    Color=&quot;#33000000&quot; Blur=&quot;3&quot; X=&quot;3&quot; Y=&quot;3&quot; /&gt;
            &lt;/draw:SkiaShape.VisualEffects&gt;

        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaLayout&gt;

    &lt;draw:SkiaLayout
        Margin=&quot;16,6,16,10&quot;
        Padding=&quot;16&quot;
        Type=&quot;Column&quot; Spacing=&quot;12&quot;
        HorizontalOptions=&quot;Fill&quot;&gt;

        &lt;!-- Author Header --&gt;
        &lt;draw:SkiaLayout Type=&quot;Row&quot; Spacing=&quot;8&quot;
                         UseCache=&quot;Image&quot;
                         HorizontalOptions=&quot;Fill&quot;&gt;

            &lt;!--avatar image--&gt;
            &lt;draw:SkiaShape
                x:Name=&quot;AvatarFrame&quot;
                Type=&quot;Circle&quot;
                WidthRequest=&quot;40&quot;
                HeightRequest=&quot;40&quot;
                BackgroundColor=&quot;LightGray&quot;&gt;

                &lt;draw:SkiaImage
                    x:Name=&quot;AvatarImage&quot;
                    Aspect=&quot;AspectFill&quot;
                    HorizontalOptions=&quot;Fill&quot;
                    VerticalOptions=&quot;Fill&quot; /&gt;

            &lt;/draw:SkiaShape&gt;

            &lt;!--avatar initials--&gt;
            &lt;draw:SkiaLayout Type=&quot;Column&quot;
                             UseCache=&quot;Operations&quot;
                             HorizontalOptions=&quot;Fill&quot;&gt;
                &lt;draw:SkiaLabel
                    x:Name=&quot;AuthorLabel&quot;
                    FontSize=&quot;14&quot;
                    FontAttributes=&quot;Bold&quot;
                    TextColor=&quot;Black&quot; /&gt;
                &lt;draw:SkiaLabel
                    x:Name=&quot;TimeLabel&quot;
                    FontSize=&quot;12&quot;
                    TextColor=&quot;Gray&quot; /&gt;
            &lt;/draw:SkiaLayout&gt;
        &lt;/draw:SkiaLayout&gt;

        &lt;!-- Content Title --&gt;
        &lt;draw:SkiaRichLabel
            UseCache=&quot;Operations&quot;
            x:Name=&quot;TitleLabel&quot;
            FontSize=&quot;16&quot;
            FontAttributes=&quot;Bold&quot;
            TextColor=&quot;Black&quot;
            IsVisible=&quot;False&quot; /&gt;

        &lt;!-- Text Content --&gt;
        &lt;draw:SkiaRichLabel
            UseCache=&quot;Operations&quot;
            x:Name=&quot;ContentLabel&quot;
            FontSize=&quot;14&quot;
            TextColor=&quot;#333333&quot;
            LineBreakMode=&quot;WordWrap&quot;
            IsVisible=&quot;False&quot; /&gt;

        &lt;!-- Image Content and optional Play Button --&gt;
        &lt;draw:SkiaShape x:Name=&quot;ContentImage&quot;
                        IsVisible=&quot;False&quot;
                        CornerRadius=&quot;16,0,0,0&quot;
                        HorizontalOptions=&quot;Fill&quot;
                        HeightRequest=&quot;200&quot;&gt;

            &lt;draw:SkiaImage
                BackgroundColor=&quot;LightGray&quot;
                x:Name=&quot;ContentImg&quot;
                Aspect=&quot;AspectCover&quot;
                VerticalOptions=&quot;Fill&quot;
                HorizontalOptions=&quot;Fill&quot; /&gt;

            &lt;draw:SkiaSvg
                x:Name=&quot;VideoLayout&quot;
                UseCache=&quot;Operations&quot;
                SvgString=&quot;{x:StaticResource SvgPlay}&quot;
                WidthRequest=&quot;50&quot;
                LockRatio=&quot;1&quot;
                TintColor=&quot;White&quot;
                Opacity=&quot;0.66&quot;
                HorizontalOptions=&quot;Center&quot;
                VerticalOptions=&quot;Center&quot; /&gt;

        &lt;/draw:SkiaShape&gt;

        &lt;!-- Article Preview --&gt;
        &lt;draw:SkiaLayout
            HorizontalOptions=&quot;Fill&quot;
            UseCache=&quot;Image&quot;
            x:Name=&quot;ArticleLayout&quot;
            Type=&quot;Row&quot;
            Spacing=&quot;12&quot;
            IsVisible=&quot;False&quot;&gt;

            &lt;draw:SkiaShape
                UseCache=&quot;Image&quot;
                CornerRadius=&quot;8,0,0,8&quot;
                WidthRequest=&quot;80&quot;
                HeightRequest=&quot;80&quot;&gt;
                &lt;draw:SkiaImage
                    HorizontalOptions=&quot;Fill&quot;
                    VerticalOptions=&quot;Fill&quot;
                    BackgroundColor=&quot;LightGray&quot;
                    x:Name=&quot;ArticleThumbnail&quot;
                    Aspect=&quot;AspectCover&quot; /&gt;
            &lt;/draw:SkiaShape&gt;

            &lt;draw:SkiaLayout Type=&quot;Column&quot; HorizontalOptions=&quot;Fill&quot; UseCache=&quot;Operations&quot;&gt;
                &lt;draw:SkiaLabel
                    x:Name=&quot;ArticleTitle&quot;
                    FontSize=&quot;14&quot;
                    FontAttributes=&quot;Bold&quot;
                    TextColor=&quot;Black&quot;
                    LineBreakMode=&quot;TailTruncation&quot;
                    MaxLines=&quot;2&quot; /&gt;
                &lt;draw:SkiaLabel
                    x:Name=&quot;ArticleDescription&quot;
                    FontSize=&quot;12&quot;
                    TextColor=&quot;Gray&quot;
                    LineBreakMode=&quot;TailTruncation&quot;
                    MaxLines=&quot;3&quot; /&gt;
            &lt;/draw:SkiaLayout&gt;

        &lt;/draw:SkiaLayout&gt;

        &lt;!-- Ad Content --&gt;
        &lt;draw:SkiaShape
            HeightRequest=&quot;150&quot;
            BackgroundColor=&quot;LightGray&quot;
            HorizontalOptions=&quot;Fill&quot;
            UseCache=&quot;Image&quot;
            x:Name=&quot;AdLayout&quot;
            IsVisible=&quot;False&quot;&gt;

            &lt;draw:SkiaLabel
                UseCache=&quot;Operations&quot;
                Text=&quot;Sponsored&quot;
                FontSize=&quot;10&quot;
                TextColor=&quot;Gray&quot;
                Margin=&quot;4,0&quot;
                HorizontalOptions=&quot;End&quot; /&gt;

            &lt;draw:SkiaImage
                Margin=&quot;0,16,0,32&quot;
                UseCache=&quot;Image&quot;
                x:Name=&quot;AdImage&quot;
                VerticalOptions=&quot;Fill&quot;
                HorizontalOptions=&quot;Fill&quot;
                Aspect=&quot;AspectFill&quot; /&gt;

            &lt;draw:SkiaLabel
                VerticalOptions=&quot;End&quot;
                UseCache=&quot;Operations&quot;
                x:Name=&quot;AdTitle&quot;
                FontSize=&quot;14&quot;
                Margin=&quot;8&quot;
                FontAttributes=&quot;Bold&quot;
                MaxLines=&quot;1&quot;
                TextColor=&quot;Black&quot; /&gt;

        &lt;/draw:SkiaShape&gt;

        &lt;!-- Interaction Bar --&gt;
        &lt;draw:SkiaLayout Type=&quot;Grid&quot;
                         UseCache=&quot;Operations&quot;
                         ColumnDefinitions=&quot;33*,33*,33*&quot;
                         ColumnSpacing=&quot;0&quot;
                         HorizontalOptions=&quot;Fill&quot;&gt;

            &lt;draw:SkiaRichLabel
                HorizontalOptions=&quot;Center&quot;
                Grid.Column=&quot;0&quot;
                x:Name=&quot;LikeButton&quot;
                Text=&quot;👍&quot;
                BackgroundColor=&quot;Transparent&quot;
                TextColor=&quot;Gray&quot;
                FontSize=&quot;14&quot; /&gt;

            &lt;draw:SkiaRichLabel
                Grid.Column=&quot;1&quot;
                HorizontalOptions=&quot;Center&quot;
                x:Name=&quot;CommentButton&quot;
                Text=&quot;💬&quot;
                BackgroundColor=&quot;Transparent&quot;
                TextColor=&quot;Gray&quot;
                FontSize=&quot;14&quot; /&gt;

            &lt;draw:SkiaRichLabel
                Grid.Column=&quot;2&quot;
                HorizontalOptions=&quot;Center&quot;
                x:Name=&quot;ShareButton&quot;
                Text=&quot;📤&quot;
                BackgroundColor=&quot;Transparent&quot;
                TextColor=&quot;Gray&quot;
                FontSize=&quot;14&quot; /&gt;

        &lt;/draw:SkiaLayout&gt;

    &lt;/draw:SkiaLayout&gt;

    &lt;!--used for debug Id--&gt;
    &lt;draw:SkiaLabel
        Margin=&quot;32,24&quot;
        x:Name=&quot;DebugId&quot;
        TextColor=&quot;Red&quot;
        HorizontalOptions=&quot;End&quot; UseCache=&quot;Operations&quot; /&gt;

&lt;/draw:SkiaDynamicDrawnCell&gt;
</code></pre>
<p>You could enable showing debugging information by uncommenting the following code on the sample page, this would give you the idea what is happening with your cells, how much of them you are currently using and have in the pool:</p>
<pre><code class="lang-xml">                &lt;draw:SkiaLabel
                    UseCache=&quot;Operations&quot;
                    Margin=&quot;8&quot;
                    Padding=&quot;2&quot;
                    AddMarginBottom=&quot;50&quot;
                    BackgroundColor=&quot;#CC000000&quot;
                    HorizontalOptions=&quot;Start&quot;
                    InputTransparent=&quot;True&quot;
                    Text=&quot;{Binding Source={x:Reference NewsStack}, Path=DebugString}&quot;
                    TextColor=&quot;LawnGreen&quot;
                    VerticalOptions=&quot;End&quot;
                    ZIndex=&quot;100&quot; /&gt;

                &lt;draw:SkiaLabelFps
                    Margin=&quot;0,0,4,24&quot;
                    BackgroundColor=&quot;DarkRed&quot;
                    HorizontalOptions=&quot;End&quot;
                    Rotation=&quot;-45&quot;
                    TextColor=&quot;White&quot;
                    VerticalOptions=&quot;End&quot;
                    ZIndex=&quot;100&quot;/&gt;
</code></pre>
<h3 id="-key-concept">🧠 Key Concept</h3>
<blockquote>
<ul>
<li>In performance critical scenarios we do not use MAUI bindings, we patch cells properties in one frame from code-behind. Notice we do not need UI thread to access properties of drawn virtual controls. If you need thread-safe bindings use DrawnUI fluent extensions, they provide INotifyPropertyChanged oberver pattern that is background thread-friendly.</li>
</ul>
</blockquote>
<h4 id="core-recycling-pattern"><strong>Core Recycling Pattern</strong></h4>
<p>The <code>SetContent</code> method is called every time <code>BindingContext</code> changes for cell, and it's internally wrapped with batch-update lock, so no intermediate rendering happens, very important for performance.</p>
<pre><code class="lang-csharp">protected override void SetContent(object ctx)
{
    base.SetContent(ctx);

    if (ctx is NewsItem news)
    {
        ConfigureForContentType(news);
    }
}
</code></pre>
<h4 id="smart-content-configuration"><strong>Smart Content Configuration</strong></h4>
<p>Since we paint what we need instead of using MAUI <code>DataTemplateSelector</code>, we can simply hide/show elements based on content type. The hide/show concent is very efficient with virtual controls, hidden controls do not participate in layout and drawing and they since they do not create any native views they affect no pressure.</p>
<pre><code class="lang-csharp">private void ConfigureForContentType(NewsItem news)
{
    // Reset all content visibility first
    HideAllContent();

    // Configure common elements (author, time, etc.)
    AuthorLabel.Text = news.AuthorName;
    TimeLabel.Text = GetRelativeTime(news.PublishedAt);

    // Then configure based on content type
    switch (news.Type)
    {
        case NewsType.Text:
            ConfigureTextPost(news);
            break;
        case NewsType.Image:
            ConfigureImagePost(news);
            break;
        // ... other types
    }
}
</code></pre>
<h4 id="custom-placeholder-drawing"><strong>Custom Placeholder Drawing</strong></h4>
<p>When using cache type <code>ImageDoubleBuffered</code> we can use <code>DrawPlaceholder</code> method to draw a custom placeholder while the first  cache is being prepared in background. Here we simulate an empty cell background layer, we use its existing padding to calculate the exact area. Notice we reuse the SKPaint and it would be disposed when the cell is disposed, instead of creating a new one for each call, keeping the UI-freezing GC collector away as much as possible.</p>
<pre><code class="lang-csharp">public override void DrawPlaceholder(DrawingContext context)
{
        var margins = BackgroundLayer.Padding;
        var area =
                new SKRect((float)(context.Destination.Left + margins.Left * context.Scale),
            (float)(context.Destination.Top + margins.Top * context.Scale),
        (float)(context.Destination.Right - margins.Right * context.Scale),
        (float)(context.Destination.Bottom - margins.Bottom * context.Scale));

    PaintPlaceholder ??= new SKPaint
    {
        Color = SKColor.Parse(&quot;#FFFFFF&quot;),
        Style = SKPaintStyle.Fill,
    };

    context.Context.Canvas.DrawRect(area, PaintPlaceholder);
}
</code></pre>
<pre><code class="lang-csharp">public override void OnWillDisposeWithChildren()
{
    base.OnWillDisposeWithChildren();
    PaintPlaceholder?.Dispose(); // Clean up SKPaint resources
}
</code></pre>
<blockquote>
<p><strong>📁 Complete Code:</strong> Find the full implementation in the <a href="https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Tutorials/Tutorials/NewsFeed/NewsCell.xaml.cs">Tutorials project</a></p>
</blockquote>
<h3 id="-data-provider">🌐 Data Provider</h3>
<blockquote>
<p><strong>Real Avatar Images</strong>: Uses RandomUser.me API for 100x100px professional avatars<br>
<strong>Real Content Images</strong>: Uses Picsum Photos API for high-quality random images</p>
</blockquote>
<pre><code class="lang-csharp"> 
public class NewsDataProvider
{
 
    private void ConfigureItemByType(NewsItem item)
    {
        switch (item.Type)
        {
            case NewsType.Text:
                item.Content = postTexts[random.Next(postTexts.Length)];
                break;

            case NewsType.Image:
                item.Content = postTexts[random.Next(postTexts.Length)];
                // High-quality random images from Picsum
                item.ImageUrl = $&quot;https://picsum.photos/seed/{index}/600/400&quot;;
                break;

            case NewsType.Video:
                item.Title = &quot;Amazing Video Content&quot;;
                item.Content = &quot;Check out this incredible footage!&quot;;
                // Video thumbnail from Picsum
                item.VideoUrl = $&quot;https://picsum.photos/seed/{index}/600/400&quot;;
                break;

            case NewsType.Article:
                item.Title = articleTitles[random.Next(articleTitles.Length)];
                item.Content = articleDescriptions[random.Next(articleDescriptions.Length)];
                item.ImageUrl = $&quot;https://picsum.photos/seed/{index}/400/300&quot;;
                item.ArticleUrl = &quot;https://example.com/article&quot;;
                break;

            case NewsType.Ad:
                item.Title = &quot;Special Offer - Don't Miss Out!&quot;;
                item.Content = &quot;Limited time offer on premium features&quot;;
                item.ImageUrl = $&quot;https://picsum.photos/seed/{index}/600/200&quot;;
                break;
        }

        // Random engagement numbers
        item.LikesCount = random.Next(0, 1000);
        item.CommentsCount = random.Next(0, 150);
    }

    private NewsType GetRandomNewsType()
    {
        // Weighted distribution for realistic feed
        var typeWeights = new (NewsType type, int weight)[]
        {
            (NewsType.Text, 30),    // 30% text posts
            (NewsType.Image, 40),   // 40% image posts
            (NewsType.Video, 15),   // 15% videos
            (NewsType.Article, 10), // 10% articles
            (NewsType.Ad, 5)        // 5% ads
        };

        var totalWeight = typeWeights.Sum(x =&gt; x.weight);
        var randomValue = random.Next(totalWeight);

        var currentWeight = 0;
        foreach (var (type, weight) in typeWeights)
        {
            currentWeight += weight;
            if (randomValue &lt; currentWeight)
                return type;
        }

        return NewsType.Text;
    }

    private (string name, string avatarUrl) GetRandomAuthor()
    {
        return authors[random.Next(authors.Length)];
    }
}
</code></pre>
<p>You would see something you'd normally expect in our viewmodel:</p>
<pre><code class="lang-csharp">
public class NewsViewModel : BaseViewModel
{
 
    public ObservableRangeCollection&lt;NewsItem&gt; NewsItems { get; }

    public ICommand RefreshCommand { get; }
    public ICommand LoadMoreCommand { get; }

    private const int DataChunkSize = 50;

    private async Task RefreshFeed()
    {
        if (IsBusy) return;

        IsBusy = true;

        try
        {
            // Cancel previous preloading
            _preloadCancellation?.Cancel();

            Debug.WriteLine($&quot;Loading news feed !!!&quot;);

            // Generate fresh content
            var newItems = _dataProvider.GetNewsFeed(DataChunkSize);

            // Preload images in background (DrawnUI's SkiaImageManager)
            _preloadCancellation = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            _ = PreloadImages(newItems, _preloadCancellation.Token);

            // Update UI - Replace all items for refresh
            MainThread.BeginInvokeOnMainThread(() =&gt;
            {
                NewsItems.Clear();
                NewsItems.AddRange(newItems);
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($&quot;Error refreshing feed: {ex.Message}&quot;);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task LoadMore()
    {
        if (IsBusy) return;

        IsBusy = true;

        try
        {
            Debug.WriteLine(&quot;Loading more items !!!&quot;);
            var newItems = _dataProvider.GetNewsFeed(15);

            // Preload new images
            _preloadCancellation = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            _ = PreloadImages(newItems, _preloadCancellation.Token);

            // Add new items to the end of the collection
            MainThread.BeginInvokeOnMainThread(() =&gt;
            {
                NewsItems.AddRange(newItems);
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($&quot;Error loading more: {ex.Message}&quot;);
        }
        finally
        {
            IsBusy = false;
        }
    }
}
 
</code></pre>
<h2 id="conclusion">Conclusion</h2>
<p>DrawnUI gives you the freedom to <strong>just draw what you need</strong>. This tutorial demonstrates a challenging real-world scenario:</p>
<h3 id="-we-accomplished">✅ <strong>We Accomplished</strong></h3>
<ul>
<li><strong>One universal cell</strong> handling 5 different content types with uneven heights</li>
<li><strong>Real internet images</strong> from RandomUser.me (avatars) and Picsum Photos (content)</li>
<li><strong>Image preloading</strong> for both avatars and content images using SkiaImageManager</li>
<li><strong>Smart caching strategy</strong> using <code>UseCache=&quot;ImageDoubleBuffered&quot;</code> with MeasureVisible</li>
<li><strong>Shadow performance optimization</strong> with separate cached background layer</li>
<li><strong>Proper LoadMore</strong> implementation with <code>AddRange()</code> vs <code>Clear()</code> + <code>AddRange()</code></li>
<li><strong>Strategic spacing</strong> using cell margin/padding instead of stack spacing</li>
<li><strong>Experimental MeasureVisible</strong> strategy for optimal large list performance</li>
<li><strong>VirtualisationInflated</strong> for smoother scrolling with pre-inflated items</li>
<li><strong>Debug information</strong> display for monitoring performance</li>
</ul>
<h3 id="-performance-remainder">🎯 <strong>Performance Remainder</strong></h3>
<ul>
<li><strong>Caching</strong>: <code>UseCache=&quot;ImageDoubleBuffered&quot;</code> for cells, <code>UseCache=&quot;Image&quot;</code> for heavy content, <code>UseCache=&quot;Operations&quot;</code> for simple text and vectors.</li>
<li><strong>Layering</strong>: Separate UI into layers for caching</li>
<li><strong>Debug</strong>: Monitor how your optimizations affect FPS in realtime to notice drastic difference with and without caching and other techniques.</li>
</ul>
<h3 id="-the-drawnui-advantage">🚀 <strong>The DrawnUI Advantage</strong></h3>
<p>A smooth, efficient news feed that handles the challenging case of uneven row heights while loading real images from the internet. <strong>Draw what you want!</strong> 🎨</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/drawnui/blob/master/docs/articles/news-feed-tutorial.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          Made by <a href="https://taublast.github.io/about/">Nick Kovalsky aka AppoMobi (@taublast)</a>
        </div>
      </div>
    </footer>
  </body>
</html>
