### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ElementRenderer
  commentId: T:DrawnUi.Draw.ElementRenderer
  id: ElementRenderer
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ElementRenderer.#ctor(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.ElementRenderer.CreateRenderObject(DrawnUi.Draw.DrawingContext)
  - DrawnUi.Draw.ElementRenderer.Dispose
  - DrawnUi.Draw.ElementRenderer.Element
  - DrawnUi.Draw.ElementRenderer.PaintWithOpacity
  langs:
  - csharp
  - vb
  name: ElementRenderer
  nameWithType: ElementRenderer
  fullName: DrawnUi.Draw.ElementRenderer
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ElementRenderer
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class ElementRenderer
    content.vb: Public Class ElementRenderer
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ElementRenderer.#ctor(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.ElementRenderer.#ctor(DrawnUi.Draw.SkiaControl)
  id: '#ctor(DrawnUi.Draw.SkiaControl)'
  parent: DrawnUi.Draw.ElementRenderer
  langs:
  - csharp
  - vb
  name: ElementRenderer(SkiaControl)
  nameWithType: ElementRenderer.ElementRenderer(SkiaControl)
  fullName: DrawnUi.Draw.ElementRenderer.ElementRenderer(DrawnUi.Draw.SkiaControl)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ElementRenderer(SkiaControl control)
    parameters:
    - id: control
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub New(control As SkiaControl)
  overload: DrawnUi.Draw.ElementRenderer.#ctor*
  nameWithType.vb: ElementRenderer.New(SkiaControl)
  fullName.vb: DrawnUi.Draw.ElementRenderer.New(DrawnUi.Draw.SkiaControl)
  name.vb: New(SkiaControl)
- uid: DrawnUi.Draw.ElementRenderer.Dispose
  commentId: M:DrawnUi.Draw.ElementRenderer.Dispose
  id: Dispose
  parent: DrawnUi.Draw.ElementRenderer
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: ElementRenderer.Dispose()
  fullName: DrawnUi.Draw.ElementRenderer.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 35
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.ElementRenderer.Dispose*
- uid: DrawnUi.Draw.ElementRenderer.Element
  commentId: F:DrawnUi.Draw.ElementRenderer.Element
  id: Element
  parent: DrawnUi.Draw.ElementRenderer
  langs:
  - csharp
  - vb
  name: Element
  nameWithType: ElementRenderer.Element
  fullName: DrawnUi.Draw.ElementRenderer.Element
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Element
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Must not be used while rendering, only for createing render object
  example: []
  syntax:
    content: protected readonly SkiaControl Element
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Protected ReadOnly Element As SkiaControl
- uid: DrawnUi.Draw.ElementRenderer.PaintWithOpacity
  commentId: P:DrawnUi.Draw.ElementRenderer.PaintWithOpacity
  id: PaintWithOpacity
  parent: DrawnUi.Draw.ElementRenderer
  langs:
  - csharp
  - vb
  name: PaintWithOpacity
  nameWithType: ElementRenderer.PaintWithOpacity
  fullName: DrawnUi.Draw.ElementRenderer.PaintWithOpacity
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PaintWithOpacity
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 48
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Can be reused for drawing, single threaded only
  example: []
  syntax:
    content: public SKPaint PaintWithOpacity { get; protected set; }
    parameters: []
    return:
      type: SkiaSharp.SKPaint
    content.vb: Public Property PaintWithOpacity As SKPaint
  overload: DrawnUi.Draw.ElementRenderer.PaintWithOpacity*
- uid: DrawnUi.Draw.ElementRenderer.CreateRenderObject(DrawnUi.Draw.DrawingContext)
  commentId: M:DrawnUi.Draw.ElementRenderer.CreateRenderObject(DrawnUi.Draw.DrawingContext)
  id: CreateRenderObject(DrawnUi.Draw.DrawingContext)
  parent: DrawnUi.Draw.ElementRenderer
  langs:
  - csharp
  - vb
  name: CreateRenderObject(DrawingContext)
  nameWithType: ElementRenderer.CreateRenderObject(DrawingContext)
  fullName: DrawnUi.Draw.ElementRenderer.CreateRenderObject(DrawnUi.Draw.DrawingContext)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateRenderObject
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 50
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual RenderObject CreateRenderObject(DrawingContext ctx)
    parameters:
    - id: ctx
      type: DrawnUi.Draw.DrawingContext
    return:
      type: DrawnUi.Draw.RenderObject
    content.vb: Public Overridable Function CreateRenderObject(ctx As DrawingContext) As RenderObject
  overload: DrawnUi.Draw.ElementRenderer.CreateRenderObject*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ElementRenderer.#ctor*
  commentId: Overload:DrawnUi.Draw.ElementRenderer.#ctor
  href: DrawnUi.Draw.ElementRenderer.html#DrawnUi_Draw_ElementRenderer__ctor_DrawnUi_Draw_SkiaControl_
  name: ElementRenderer
  nameWithType: ElementRenderer.ElementRenderer
  fullName: DrawnUi.Draw.ElementRenderer.ElementRenderer
  nameWithType.vb: ElementRenderer.New
  fullName.vb: DrawnUi.Draw.ElementRenderer.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.ElementRenderer.Dispose*
  commentId: Overload:DrawnUi.Draw.ElementRenderer.Dispose
  href: DrawnUi.Draw.ElementRenderer.html#DrawnUi_Draw_ElementRenderer_Dispose
  name: Dispose
  nameWithType: ElementRenderer.Dispose
  fullName: DrawnUi.Draw.ElementRenderer.Dispose
- uid: DrawnUi.Draw.ElementRenderer.PaintWithOpacity*
  commentId: Overload:DrawnUi.Draw.ElementRenderer.PaintWithOpacity
  href: DrawnUi.Draw.ElementRenderer.html#DrawnUi_Draw_ElementRenderer_PaintWithOpacity
  name: PaintWithOpacity
  nameWithType: ElementRenderer.PaintWithOpacity
  fullName: DrawnUi.Draw.ElementRenderer.PaintWithOpacity
- uid: SkiaSharp.SKPaint
  commentId: T:SkiaSharp.SKPaint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpaint
  name: SKPaint
  nameWithType: SKPaint
  fullName: SkiaSharp.SKPaint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ElementRenderer.CreateRenderObject*
  commentId: Overload:DrawnUi.Draw.ElementRenderer.CreateRenderObject
  href: DrawnUi.Draw.ElementRenderer.html#DrawnUi_Draw_ElementRenderer_CreateRenderObject_DrawnUi_Draw_DrawingContext_
  name: CreateRenderObject
  nameWithType: ElementRenderer.CreateRenderObject
  fullName: DrawnUi.Draw.ElementRenderer.CreateRenderObject
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
- uid: DrawnUi.Draw.RenderObject
  commentId: T:DrawnUi.Draw.RenderObject
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderObject.html
  name: RenderObject
  nameWithType: RenderObject
  fullName: DrawnUi.Draw.RenderObject
