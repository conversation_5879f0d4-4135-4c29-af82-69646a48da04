### YamlMime:ManagedReference
items:
- uid: DrawnUi.Features.Images.ImagesExtensions
  commentId: T:DrawnUi.Features.Images.ImagesExtensions
  id: ImagesExtensions
  parent: DrawnUi.Features.Images
  children:
  - DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient(System.IServiceProvider)
  - DrawnUi.Features.Images.ImagesExtensions.HttpClientKey
  langs:
  - csharp
  - vb
  name: ImagesExtensions
  nameWithType: ImagesExtensions
  fullName: DrawnUi.Features.Images.ImagesExtensions
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ImagesExtensions
    path: ../src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Features.Images
  syntax:
    content: public static class ImagesExtensions
    content.vb: Public Module ImagesExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Features.Images.ImagesExtensions.HttpClientKey
  commentId: F:DrawnUi.Features.Images.ImagesExtensions.HttpClientKey
  id: HttpClientKey
  parent: DrawnUi.Features.Images.ImagesExtensions
  langs:
  - csharp
  - vb
  name: HttpClientKey
  nameWithType: ImagesExtensions.HttpClientKey
  fullName: DrawnUi.Features.Images.ImagesExtensions.HttpClientKey
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HttpClientKey
    path: ../src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Features.Images
  summary: Can customize this to your needs
  example: []
  syntax:
    content: public static string HttpClientKey
    return:
      type: System.String
    content.vb: Public Shared HttpClientKey As String
- uid: DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient(System.IServiceProvider)
  commentId: M:DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient(System.IServiceProvider)
  id: CreateHttpClient(System.IServiceProvider)
  isExtensionMethod: true
  parent: DrawnUi.Features.Images.ImagesExtensions
  langs:
  - csharp
  - vb
  name: CreateHttpClient(IServiceProvider)
  nameWithType: ImagesExtensions.CreateHttpClient(IServiceProvider)
  fullName: DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient(System.IServiceProvider)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateHttpClient
    path: ../src/Maui/DrawnUi/Features/Images/ImagesExtensions.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Features.Images
  summary: >-
    Will create a HttpClient with a UserAgent in headers defined by `Super.UserAgent`.

    You can define your own delegate to create HttpClient by setting Super.CreateHttpClient.

    Do not forget to dispose the client after usage, we do not use IHttpClientFactory by default.
  example: []
  syntax:
    content: public static HttpClient? CreateHttpClient(this IServiceProvider services)
    parameters:
    - id: services
      type: System.IServiceProvider
      description: ''
    return:
      type: System.Net.Http.HttpClient
      description: ''
    content.vb: Public Shared Function CreateHttpClient(services As IServiceProvider) As HttpClient
  overload: DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient*
references:
- uid: DrawnUi.Features.Images
  commentId: N:DrawnUi.Features.Images
  href: DrawnUi.html
  name: DrawnUi.Features.Images
  nameWithType: DrawnUi.Features.Images
  fullName: DrawnUi.Features.Images
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Features
    name: Features
    href: DrawnUi.Features.html
  - name: .
  - uid: DrawnUi.Features.Images
    name: Images
    href: DrawnUi.Features.Images.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Features
    name: Features
    href: DrawnUi.Features.html
  - name: .
  - uid: DrawnUi.Features.Images
    name: Images
    href: DrawnUi.Features.Images.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient*
  commentId: Overload:DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient
  href: DrawnUi.Features.Images.ImagesExtensions.html#DrawnUi_Features_Images_ImagesExtensions_CreateHttpClient_System_IServiceProvider_
  name: CreateHttpClient
  nameWithType: ImagesExtensions.CreateHttpClient
  fullName: DrawnUi.Features.Images.ImagesExtensions.CreateHttpClient
- uid: System.IServiceProvider
  commentId: T:System.IServiceProvider
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  name: IServiceProvider
  nameWithType: IServiceProvider
  fullName: System.IServiceProvider
- uid: System.Net.Http.HttpClient
  commentId: T:System.Net.Http.HttpClient
  parent: System.Net.Http
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.net.http.httpclient
  name: HttpClient
  nameWithType: HttpClient
  fullName: System.Net.Http.HttpClient
- uid: System.Net.Http
  commentId: N:System.Net.Http
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Net.Http
  nameWithType: System.Net.Http
  fullName: System.Net.Http
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Net
    name: Net
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net
  - name: .
  - uid: System.Net.Http
    name: Http
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Net
    name: Net
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net
  - name: .
  - uid: System.Net.Http
    name: Http
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http
