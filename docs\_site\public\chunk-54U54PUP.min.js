import{a as kt,b as bt}from"./chunk-ISDTAGDN.min.js";import{b as ft,c as At}from"./chunk-JL3VILNY.min.js";import{m as me,p as dt}from"./chunk-PYPO7LRM.min.js";import{H as at,J as R,N as x,S as nt,T as ut,U as lt,V as ot,W as ct,X as ht,Y as pt,Z as T,h as n,ia as K,j as ge}from"./chunk-U3SD26FK.min.js";var Ye=function(){var e=n(function(O,o,c,d){for(c=c||{},d=O.length;d--;c[O[d]]=o);return c},"o"),r=[1,18],u=[1,19],l=[1,20],i=[1,41],p=[1,42],f=[1,26],b=[1,24],F=[1,25],S=[1,32],ye=[1,33],Te=[1,34],g=[1,45],Fe=[1,35],De=[1,36],_e=[1,37],Be=[1,38],Se=[1,27],ve=[1,28],Ne=[1,29],Le=[1,30],xe=[1,31],m=[1,44],C=[1,46],E=[1,43],D=[1,47],Ie=[1,9],h=[1,8,9],ae=[1,58],ne=[1,59],ue=[1,60],le=[1,61],oe=[1,62],Oe=[1,63],we=[1,64],ce=[1,8,9,41],He=[1,76],U=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],he=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],pe=[13,58,84,99,101,102],W=[13,58,71,72,84,99,101,102],qe=[13,58,66,67,68,69,70,84,99,101,102],Ve=[1,98],j=[1,115],X=[1,107],H=[1,113],q=[1,108],J=[1,109],Z=[1,110],$=[1,111],ee=[1,112],te=[1,114],Je=[22,58,59,80,84,85,86,87,88,89],Pe=[1,8,9,39,41,44],de=[1,8,9,22],Ze=[1,143],$e=[1,8,9,59],N=[1,8,9,22,58,59,80,84,85,86,87,88,89],Re={trace:n(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:n(function(o,c,d,a,A,t,se){var s=t.length-1;switch(A){case 8:this.$=t[s-1];break;case 9:case 12:case 14:this.$=t[s];break;case 10:case 13:this.$=t[s-2]+"."+t[s];break;case 11:case 15:this.$=t[s-1]+t[s];break;case 16:case 17:this.$=t[s-1]+"~"+t[s]+"~";break;case 18:a.addRelation(t[s]);break;case 19:t[s-1].title=a.cleanupLabel(t[s]),a.addRelation(t[s-1]);break;case 30:this.$=t[s].trim(),a.setAccTitle(this.$);break;case 31:case 32:this.$=t[s].trim(),a.setAccDescription(this.$);break;case 33:a.addClassesToNamespace(t[s-3],t[s-1]);break;case 34:a.addClassesToNamespace(t[s-4],t[s-1]);break;case 35:this.$=t[s],a.addNamespace(t[s]);break;case 36:this.$=[t[s]];break;case 37:this.$=[t[s-1]];break;case 38:t[s].unshift(t[s-2]),this.$=t[s];break;case 40:a.setCssClass(t[s-2],t[s]);break;case 41:a.addMembers(t[s-3],t[s-1]);break;case 42:a.setCssClass(t[s-5],t[s-3]),a.addMembers(t[s-5],t[s-1]);break;case 43:this.$=t[s],a.addClass(t[s]);break;case 44:this.$=t[s-1],a.addClass(t[s-1]),a.setClassLabel(t[s-1],t[s]);break;case 45:a.addAnnotation(t[s],t[s-2]);break;case 46:case 59:this.$=[t[s]];break;case 47:t[s].push(t[s-1]),this.$=t[s];break;case 48:break;case 49:a.addMember(t[s-1],a.cleanupLabel(t[s]));break;case 50:break;case 51:break;case 52:this.$={id1:t[s-2],id2:t[s],relation:t[s-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:t[s-3],id2:t[s],relation:t[s-1],relationTitle1:t[s-2],relationTitle2:"none"};break;case 54:this.$={id1:t[s-3],id2:t[s],relation:t[s-2],relationTitle1:"none",relationTitle2:t[s-1]};break;case 55:this.$={id1:t[s-4],id2:t[s],relation:t[s-2],relationTitle1:t[s-3],relationTitle2:t[s-1]};break;case 56:a.addNote(t[s],t[s-1]);break;case 57:a.addNote(t[s]);break;case 58:this.$=t[s-2],a.defineClass(t[s-1],t[s]);break;case 60:this.$=t[s-2].concat([t[s]]);break;case 61:a.setDirection("TB");break;case 62:a.setDirection("BT");break;case 63:a.setDirection("RL");break;case 64:a.setDirection("LR");break;case 65:this.$={type1:t[s-2],type2:t[s],lineType:t[s-1]};break;case 66:this.$={type1:"none",type2:t[s],lineType:t[s-1]};break;case 67:this.$={type1:t[s-1],type2:"none",lineType:t[s]};break;case 68:this.$={type1:"none",type2:"none",lineType:t[s]};break;case 69:this.$=a.relationType.AGGREGATION;break;case 70:this.$=a.relationType.EXTENSION;break;case 71:this.$=a.relationType.COMPOSITION;break;case 72:this.$=a.relationType.DEPENDENCY;break;case 73:this.$=a.relationType.LOLLIPOP;break;case 74:this.$=a.lineType.LINE;break;case 75:this.$=a.lineType.DOTTED_LINE;break;case 76:case 82:this.$=t[s-2],a.setClickEvent(t[s-1],t[s]);break;case 77:case 83:this.$=t[s-3],a.setClickEvent(t[s-2],t[s-1]),a.setTooltip(t[s-2],t[s]);break;case 78:this.$=t[s-2],a.setLink(t[s-1],t[s]);break;case 79:this.$=t[s-3],a.setLink(t[s-2],t[s-1],t[s]);break;case 80:this.$=t[s-3],a.setLink(t[s-2],t[s-1]),a.setTooltip(t[s-2],t[s]);break;case 81:this.$=t[s-4],a.setLink(t[s-3],t[s-2],t[s]),a.setTooltip(t[s-3],t[s-1]);break;case 84:this.$=t[s-3],a.setClickEvent(t[s-2],t[s-1],t[s]);break;case 85:this.$=t[s-4],a.setClickEvent(t[s-3],t[s-2],t[s-1]),a.setTooltip(t[s-3],t[s]);break;case 86:this.$=t[s-3],a.setLink(t[s-2],t[s]);break;case 87:this.$=t[s-4],a.setLink(t[s-3],t[s-1],t[s]);break;case 88:this.$=t[s-4],a.setLink(t[s-3],t[s-1]),a.setTooltip(t[s-3],t[s]);break;case 89:this.$=t[s-5],a.setLink(t[s-4],t[s-2],t[s]),a.setTooltip(t[s-4],t[s-1]);break;case 90:this.$=t[s-2],a.setCssStyle(t[s-1],t[s]);break;case 91:a.setCssClass(t[s-1],t[s]);break;case 92:this.$=[t[s]];break;case 93:t[s-2].push(t[s]),this.$=t[s-2];break;case 95:this.$=t[s-1]+t[s];break}},"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:r,35:u,37:l,38:22,42:i,43:23,46:p,47:f,49:b,50:F,52:S,54:ye,55:Te,58:g,60:Fe,61:De,62:_e,63:Be,73:Se,74:ve,76:Ne,80:Le,81:xe,84:m,99:C,101:E,102:D},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},e(Ie,[2,5],{8:[1,48]}),{8:[1,49]},e(h,[2,18],{22:[1,50]}),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(h,[2,23]),e(h,[2,24]),e(h,[2,25]),e(h,[2,26]),e(h,[2,27]),e(h,[2,28]),e(h,[2,29]),{34:[1,51]},{36:[1,52]},e(h,[2,32]),e(h,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:ae,67:ne,68:ue,69:le,70:oe,71:Oe,72:we}),{39:[1,65]},e(ce,[2,39],{39:[1,67],44:[1,66]}),e(h,[2,50]),e(h,[2,51]),{16:68,58:g,84:m,99:C,101:E},{16:39,18:69,19:40,58:g,84:m,99:C,101:E,102:D},{16:39,18:70,19:40,58:g,84:m,99:C,101:E,102:D},{16:39,18:71,19:40,58:g,84:m,99:C,101:E,102:D},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:g,84:m,99:C,101:E,102:D},{13:He,53:75},{56:77,58:[1,78]},e(h,[2,61]),e(h,[2,62]),e(h,[2,63]),e(h,[2,64]),e(U,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:g,84:m,99:C,101:E,102:D}),e(U,[2,14],{20:[1,82]}),{15:83,16:84,58:g,84:m,99:C,101:E},{16:39,18:85,19:40,58:g,84:m,99:C,101:E,102:D},e(he,[2,118]),e(he,[2,119]),e(he,[2,120]),e(he,[2,121]),e([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),e(Ie,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:r,35:u,37:l,42:i,46:p,47:f,49:b,50:F,52:S,54:ye,55:Te,58:g,60:Fe,61:De,62:_e,63:Be,73:Se,74:ve,76:Ne,80:Le,81:xe,84:m,99:C,101:E,102:D}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:r,35:u,37:l,38:22,42:i,43:23,46:p,47:f,49:b,50:F,52:S,54:ye,55:Te,58:g,60:Fe,61:De,62:_e,63:Be,73:Se,74:ve,76:Ne,80:Le,81:xe,84:m,99:C,101:E,102:D},e(h,[2,19]),e(h,[2,30]),e(h,[2,31]),{13:[1,89],16:39,18:88,19:40,58:g,84:m,99:C,101:E,102:D},{51:90,64:56,65:57,66:ae,67:ne,68:ue,69:le,70:oe,71:Oe,72:we},e(h,[2,49]),{65:91,71:Oe,72:we},e(pe,[2,68],{64:92,66:ae,67:ne,68:ue,69:le,70:oe}),e(W,[2,69]),e(W,[2,70]),e(W,[2,71]),e(W,[2,72]),e(W,[2,73]),e(qe,[2,74]),e(qe,[2,75]),{8:[1,94],24:95,40:93,43:23,46:p},{16:96,58:g,84:m,99:C,101:E},{45:97,49:Ve},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:j,57:104,58:X,80:H,82:105,83:106,84:q,85:J,86:Z,87:$,88:ee,89:te},{58:[1,116]},{13:He,53:117},e(h,[2,57]),e(h,[2,123]),{22:j,57:118,58:X,59:[1,119],80:H,82:105,83:106,84:q,85:J,86:Z,87:$,88:ee,89:te},e(Je,[2,59]),{16:39,18:120,19:40,58:g,84:m,99:C,101:E,102:D},e(U,[2,15]),e(U,[2,16]),e(U,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:g,84:m,99:C,101:E},e(Pe,[2,43],{11:123,12:[1,124]}),e(Ie,[2,7]),{9:[1,125]},e(de,[2,52]),{16:39,18:126,19:40,58:g,84:m,99:C,101:E,102:D},{13:[1,128],16:39,18:127,19:40,58:g,84:m,99:C,101:E,102:D},e(pe,[2,67],{64:129,66:ae,67:ne,68:ue,69:le,70:oe}),e(pe,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:p},{8:[1,132],41:[2,36]},e(ce,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:Ve},{16:39,18:136,19:40,58:g,84:m,99:C,101:E,102:D},e(h,[2,76],{13:[1,137]}),e(h,[2,78],{13:[1,139],75:[1,138]}),e(h,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},e(h,[2,90],{59:Ze}),e($e,[2,92],{83:144,22:j,58:X,80:H,84:q,85:J,86:Z,87:$,88:ee,89:te}),e(N,[2,94]),e(N,[2,96]),e(N,[2,97]),e(N,[2,98]),e(N,[2,99]),e(N,[2,100]),e(N,[2,101]),e(N,[2,102]),e(N,[2,103]),e(N,[2,104]),e(h,[2,91]),e(h,[2,56]),e(h,[2,58],{59:Ze}),{58:[1,145]},e(U,[2,13]),{15:146,16:84,58:g,84:m,99:C,101:E},{39:[2,11]},e(Pe,[2,44]),{13:[1,147]},{1:[2,4]},e(de,[2,54]),e(de,[2,53]),{16:39,18:148,19:40,58:g,84:m,99:C,101:E,102:D},e(pe,[2,65]),e(h,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:p},{45:151,49:Ve},e(ce,[2,41]),{41:[2,47]},e(h,[2,45]),e(h,[2,77]),e(h,[2,79]),e(h,[2,80],{75:[1,152]}),e(h,[2,83]),e(h,[2,84],{13:[1,153]}),e(h,[2,86],{13:[1,155],75:[1,154]}),{22:j,58:X,80:H,82:156,83:106,84:q,85:J,86:Z,87:$,88:ee,89:te},e(N,[2,95]),e(Je,[2,60]),{39:[2,10]},{14:[1,157]},e(de,[2,55]),e(h,[2,34]),{41:[2,38]},{41:[1,158]},e(h,[2,81]),e(h,[2,85]),e(h,[2,87]),e(h,[2,88],{75:[1,159]}),e($e,[2,93],{83:144,22:j,58:X,80:H,84:q,85:J,86:Z,87:$,88:ee,89:te}),e(Pe,[2,8]),e(ce,[2,42]),e(h,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:n(function(o,c){if(c.recoverable)this.trace(o);else{var d=new Error(o);throw d.hash=c,d}},"parseError"),parse:n(function(o){var c=this,d=[0],a=[],A=[null],t=[],se=this.table,s="",Ae=0,et=0,tt=0,St=2,st=1,vt=t.slice.call(arguments,1),y=Object.create(this.lexer),V={yy:{}};for(var Me in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Me)&&(V.yy[Me]=this.yy[Me]);y.setInput(o,V.yy),V.yy.lexer=y,V.yy.parser=this,typeof y.yylloc>"u"&&(y.yylloc={});var Ge=y.yylloc;t.push(Ge);var Nt=y.options&&y.options.ranges;typeof V.yy.parseError=="function"?this.parseError=V.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Lt(B){d.length=d.length-2*B,A.length=A.length-B,t.length=t.length-B}n(Lt,"popStack");function rt(){var B;return B=a.pop()||y.lex()||st,typeof B!="number"&&(B instanceof Array&&(a=B,B=a.pop()),B=c.symbols_[B]||B),B}n(rt,"lex");for(var _,Ue,P,v,ls,ze,z={},ke,I,it,be;;){if(P=d[d.length-1],this.defaultActions[P]?v=this.defaultActions[P]:((_===null||typeof _>"u")&&(_=rt()),v=se[P]&&se[P][_]),typeof v>"u"||!v.length||!v[0]){var Ke="";be=[];for(ke in se[P])this.terminals_[ke]&&ke>St&&be.push("'"+this.terminals_[ke]+"'");y.showPosition?Ke="Parse error on line "+(Ae+1)+`:
`+y.showPosition()+`
Expecting `+be.join(", ")+", got '"+(this.terminals_[_]||_)+"'":Ke="Parse error on line "+(Ae+1)+": Unexpected "+(_==st?"end of input":"'"+(this.terminals_[_]||_)+"'"),this.parseError(Ke,{text:y.match,token:this.terminals_[_]||_,line:y.yylineno,loc:Ge,expected:be})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+P+", token: "+_);switch(v[0]){case 1:d.push(_),A.push(y.yytext),t.push(y.yylloc),d.push(v[1]),_=null,Ue?(_=Ue,Ue=null):(et=y.yyleng,s=y.yytext,Ae=y.yylineno,Ge=y.yylloc,tt>0&&tt--);break;case 2:if(I=this.productions_[v[1]][1],z.$=A[A.length-I],z._$={first_line:t[t.length-(I||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(I||1)].first_column,last_column:t[t.length-1].last_column},Nt&&(z._$.range=[t[t.length-(I||1)].range[0],t[t.length-1].range[1]]),ze=this.performAction.apply(z,[s,et,Ae,V.yy,v[1],A,t].concat(vt)),typeof ze<"u")return ze;I&&(d=d.slice(0,-1*I*2),A=A.slice(0,-1*I),t=t.slice(0,-1*I)),d.push(this.productions_[v[1]][0]),A.push(z.$),t.push(z._$),it=se[d[d.length-2]][d[d.length-1]],d.push(it);break;case 3:return!0}}return!0},"parse")},Bt=function(){var O={EOF:1,parseError:n(function(c,d){if(this.yy.parser)this.yy.parser.parseError(c,d);else throw new Error(c)},"parseError"),setInput:n(function(o,c){return this.yy=c||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:n(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var c=o.match(/(?:\r\n?|\n).*/g);return c?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:n(function(o){var c=o.length,d=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c),this.offset-=c;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),d.length-1&&(this.yylineno-=d.length-1);var A=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:d?(d.length===a.length?this.yylloc.first_column:0)+a[a.length-d.length].length-d[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[A[0],A[0]+this.yyleng-c]),this.yyleng=this.yytext.length,this},"unput"),more:n(function(){return this._more=!0,this},"more"),reject:n(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:n(function(o){this.unput(this.match.slice(o))},"less"),pastInput:n(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:n(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:n(function(){var o=this.pastInput(),c=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+c+"^"},"showPosition"),test_match:n(function(o,c){var d,a,A;if(this.options.backtrack_lexer&&(A={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(A.yylloc.range=this.yylloc.range.slice(0))),a=o[0].match(/(?:\r\n?|\n).*/g),a&&(this.yylineno+=a.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],d=this.performAction.call(this,this.yy,this,c,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),d)return d;if(this._backtrack){for(var t in A)this[t]=A[t];return!1}return!1},"test_match"),next:n(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,c,d,a;this._more||(this.yytext="",this.match="");for(var A=this._currentRules(),t=0;t<A.length;t++)if(d=this._input.match(this.rules[A[t]]),d&&(!c||d[0].length>c[0].length)){if(c=d,a=t,this.options.backtrack_lexer){if(o=this.test_match(d,A[t]),o!==!1)return o;if(this._backtrack){c=!1;continue}else return!1}else if(!this.options.flex)break}return c?(o=this.test_match(c,A[a]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:n(function(){var c=this.next();return c||this.lex()},"lex"),begin:n(function(c){this.conditionStack.push(c)},"begin"),popState:n(function(){var c=this.conditionStack.length-1;return c>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:n(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:n(function(c){return c=this.conditionStack.length-1-Math.abs(c||0),c>=0?this.conditionStack[c]:"INITIAL"},"topState"),pushState:n(function(c){this.begin(c)},"pushState"),stateStackSize:n(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:n(function(c,d,a,A){var t=A;switch(a){case 0:return 60;case 1:return 61;case 2:return 62;case 3:return 63;case 4:break;case 5:break;case 6:return this.begin("acc_title"),33;break;case 7:return this.popState(),"acc_title_value";break;case 8:return this.begin("acc_descr"),35;break;case 9:return this.popState(),"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 8;case 14:break;case 15:return 7;case 16:return 7;case 17:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 77;case 22:this.popState();break;case 23:return 78;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("string");break;case 27:return 80;case 28:return 55;case 29:return this.begin("namespace"),42;break;case 30:return this.popState(),8;break;case 31:break;case 32:return this.begin("namespace-body"),39;break;case 33:return this.popState(),41;break;case 34:return"EOF_IN_STRUCT";case 35:return 8;case 36:break;case 37:return"EDGE_STATE";case 38:return this.begin("class"),46;break;case 39:return this.popState(),8;break;case 40:break;case 41:return this.popState(),this.popState(),41;break;case 42:return this.begin("class-body"),39;break;case 43:return this.popState(),41;break;case 44:return"EOF_IN_STRUCT";case 45:return"EDGE_STATE";case 46:return"OPEN_IN_STRUCT";case 47:break;case 48:return"MEMBER";case 49:return 81;case 50:return 73;case 51:return 74;case 52:return 76;case 53:return 52;case 54:return 54;case 55:return 47;case 56:return 48;case 57:return 79;case 58:this.popState();break;case 59:return"GENERICTYPE";case 60:this.begin("generic");break;case 61:this.popState();break;case 62:return"BQUOTE_STR";case 63:this.begin("bqstring");break;case 64:return 75;case 65:return 75;case 66:return 75;case 67:return 75;case 68:return 67;case 69:return 67;case 70:return 69;case 71:return 69;case 72:return 68;case 73:return 66;case 74:return 70;case 75:return 71;case 76:return 72;case 77:return 22;case 78:return 44;case 79:return 99;case 80:return 17;case 81:return"PLUS";case 82:return 85;case 83:return 59;case 84:return 88;case 85:return 88;case 86:return 89;case 87:return"EQUALS";case 88:return"EQUALS";case 89:return 58;case 90:return 12;case 91:return 14;case 92:return"PUNCTUATION";case 93:return 84;case 94:return 101;case 95:return 87;case 96:return 87;case 97:return 9}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:!0}}};return O}();Re.lexer=Bt;function fe(){this.yy={}}return n(fe,"Parser"),fe.prototype=Re,Re.Parser=fe,new fe}();Ye.parser=Ye;var ds=Ye,gt=["#","+","~","-",""],mt=class{static{n(this,"ClassMember")}constructor(e,r){this.memberType=r,this.visibility="",this.classifier="",this.text="";let u=at(e,T());this.parseMember(u)}getDisplayDetails(){let e=this.visibility+R(this.id);this.memberType==="method"&&(e+=`(${R(this.parameters.trim())})`,this.returnType&&(e+=" : "+R(this.returnType))),e=e.trim();let r=this.parseClassifier();return{displayText:e,cssStyle:r}}parseMember(e){let r="";if(this.memberType==="method"){let i=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/.exec(e);if(i){let p=i[1]?i[1].trim():"";if(gt.includes(p)&&(this.visibility=p),this.id=i[2],this.parameters=i[3]?i[3].trim():"",r=i[4]?i[4].trim():"",this.returnType=i[5]?i[5].trim():"",r===""){let f=this.returnType.substring(this.returnType.length-1);/[$*]/.exec(f)&&(r=f,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{let l=e.length,i=e.substring(0,1),p=e.substring(l-1);gt.includes(i)&&(this.visibility=i),/[$*]/.exec(p)&&(r=p),this.id=e.substring(this.visibility===""?0:1,r===""?l:l-1)}this.classifier=r,this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();let u=`${this.visibility?"\\"+this.visibility:""}${R(this.id)}${this.memberType==="method"?`(${R(this.parameters)})${this.returnType?" : "+R(this.returnType):""}`:""}`;this.text=u.replaceAll("<","&lt;").replaceAll(">","&gt;"),this.text.startsWith("\\&lt;")&&(this.text=this.text.replace("\\&lt;","~"))}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}},Ce="classId-",Ee=[],k=new Map,Ct=new Map,re=[],Q=[],Et=0,L=new Map,Qe=0,ie=[],M=n(e=>x.sanitizeText(e,T()),"sanitizeText"),G=n(function(e){let r=x.sanitizeText(e,T()),u="",l=r;if(r.indexOf("~")>0){let i=r.split("~");l=M(i[0]),u=M(i[1])}return{className:l,type:u}},"splitClassNameAndType"),xt=n(function(e,r){let u=x.sanitizeText(e,T());r&&(r=M(r));let{className:l}=G(u);k.get(l).label=r,k.get(l).text=`${r}${k.get(l).type?`<${k.get(l).type}>`:""}`},"setClassLabel"),Y=n(function(e){let r=x.sanitizeText(e,T()),{className:u,type:l}=G(r);if(k.has(u))return;let i=x.sanitizeText(u,T());k.set(i,{id:i,type:l,label:i,text:`${i}${l?`&lt;${l}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:Ce+i+"-"+Et}),Et++},"addClass"),yt=n(function(e,r){let u={id:`interface${Q.length}`,label:e,classId:r};Q.push(u)},"addInterface"),Tt=n(function(e){let r=x.sanitizeText(e,T());if(k.has(r))return k.get(r).domId;throw new Error("Class not found: "+r)},"lookUpDomId"),It=n(function(){Ee=[],k=new Map,re=[],Q=[],ie=[],ie.push(Dt),L=new Map,Qe=0,Xe="TB",nt()},"clear"),Ot=n(function(e){return k.get(e)},"getClass"),wt=n(function(){return k},"getClasses"),Vt=n(function(){return Ee},"getRelations"),Pt=n(function(){return re},"getNotes"),Rt=n(function(e){ge.debug("Adding relation: "+JSON.stringify(e));let r=[w.LOLLIPOP,w.AGGREGATION,w.COMPOSITION,w.DEPENDENCY,w.EXTENSION];e.relation.type1===w.LOLLIPOP&&!r.includes(e.relation.type2)?(Y(e.id2),yt(e.id1,e.id2),e.id1=`interface${Q.length-1}`):e.relation.type2===w.LOLLIPOP&&!r.includes(e.relation.type1)?(Y(e.id1),yt(e.id2,e.id1),e.id2=`interface${Q.length-1}`):(Y(e.id1),Y(e.id2)),e.id1=G(e.id1).className,e.id2=G(e.id2).className,e.relationTitle1=x.sanitizeText(e.relationTitle1.trim(),T()),e.relationTitle2=x.sanitizeText(e.relationTitle2.trim(),T()),Ee.push(e)},"addRelation"),Mt=n(function(e,r){let u=G(e).className;k.get(u).annotations.push(r)},"addAnnotation"),Ft=n(function(e,r){Y(e);let u=G(e).className,l=k.get(u);if(typeof r=="string"){let i=r.trim();i.startsWith("<<")&&i.endsWith(">>")?l.annotations.push(M(i.substring(2,i.length-2))):i.indexOf(")")>0?l.methods.push(new mt(i,"method")):i&&l.members.push(new mt(i,"attribute"))}},"addMember"),Gt=n(function(e,r){Array.isArray(r)&&(r.reverse(),r.forEach(u=>Ft(e,u)))},"addMembers"),Ut=n(function(e,r){let u={id:`note${re.length}`,class:r,text:e};re.push(u)},"addNote"),zt=n(function(e){return e.startsWith(":")&&(e=e.substring(1)),M(e.trim())},"cleanupLabel"),je=n(function(e,r){e.split(",").forEach(function(u){let l=u;/\d/.exec(u[0])&&(l=Ce+l);let i=k.get(l);i&&(i.cssClasses+=" "+r)})},"setCssClass"),Kt=n(function(e,r){for(let u of e){let l=Ct.get(u);l===void 0&&(l={id:u,styles:[],textStyles:[]},Ct.set(u,l)),r&&r.forEach(function(i){if(/color/.exec(i)){let p=i.replace("fill","bgFill");l.textStyles.push(p)}l.styles.push(i)}),k.forEach(i=>{i.cssClasses.includes(u)&&i.styles.push(...r.flatMap(p=>p.split(",")))})}},"defineClass"),Yt=n(function(e,r){e.split(",").forEach(function(u){r!==void 0&&(k.get(u).tooltip=M(r))})},"setTooltip"),Qt=n(function(e,r){return r&&L.has(r)?L.get(r).classes.get(e).tooltip:k.get(e).tooltip},"getTooltip"),Wt=n(function(e,r,u){let l=T();e.split(",").forEach(function(i){let p=i;/\d/.exec(i[0])&&(p=Ce+p);let f=k.get(p);f&&(f.link=me.formatUrl(r,l),l.securityLevel==="sandbox"?f.linkTarget="_top":typeof u=="string"?f.linkTarget=M(u):f.linkTarget="_blank")}),je(e,"clickable")},"setLink"),jt=n(function(e,r,u){e.split(",").forEach(function(l){Xt(l,r,u),k.get(l).haveCallback=!0}),je(e,"clickable")},"setClickEvent"),Xt=n(function(e,r,u){let l=x.sanitizeText(e,T());if(T().securityLevel!=="loose"||r===void 0)return;let p=l;if(k.has(p)){let f=Tt(p),b=[];if(typeof u=="string"){b=u.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let F=0;F<b.length;F++){let S=b[F].trim();S.startsWith('"')&&S.endsWith('"')&&(S=S.substr(1,S.length-2)),b[F]=S}}b.length===0&&b.push(f),ie.push(function(){let F=document.querySelector(`[id="${f}"]`);F!==null&&F.addEventListener("click",function(){me.runFunc(r,...b)},!1)})}},"setClickFunc"),Ht=n(function(e){ie.forEach(function(r){r(e)})},"bindFunctions"),qt={LINE:0,DOTTED_LINE:1},w={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},Dt=n(function(e){let r=K(".mermaidTooltip");(r._groups||r)[0][0]===null&&(r=K("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),K(e).select("svg").selectAll("g.node").on("mouseover",function(){let i=K(this);if(i.attr("title")===null)return;let f=this.getBoundingClientRect();r.transition().duration(200).style("opacity",".9"),r.text(i.attr("title")).style("left",window.scrollX+f.left+(f.right-f.left)/2+"px").style("top",window.scrollY+f.top-14+document.body.scrollTop+"px"),r.html(r.html().replace(/&lt;br\/&gt;/g,"<br/>")),i.classed("hover",!0)}).on("mouseout",function(){r.transition().duration(500).style("opacity",0),K(this).classed("hover",!1)})},"setupToolTips");ie.push(Dt);var Xe="TB",_t=n(()=>Xe,"getDirection"),Jt=n(e=>{Xe=e},"setDirection"),Zt=n(function(e){L.has(e)||(L.set(e,{id:e,classes:new Map,children:{},domId:Ce+e+"-"+Qe}),Qe++)},"addNamespace"),$t=n(function(e){return L.get(e)},"getNamespace"),es=n(function(){return L},"getNamespaces"),ts=n(function(e,r){if(L.has(e))for(let u of r){let{className:l}=G(u);k.get(l).parent=e,L.get(e).classes.set(l,k.get(l))}},"addClassesToNamespace"),ss=n(function(e,r){let u=k.get(e);if(!(!r||!u))for(let l of r)l.includes(",")?u.styles.push(...l.split(",")):u.styles.push(l)},"setCssStyle");function We(e){let r;switch(e){case 0:r="aggregation";break;case 1:r="extension";break;case 2:r="composition";break;case 3:r="dependency";break;case 4:r="lollipop";break;default:r="none"}return r}n(We,"getArrowMarker");var rs=n(()=>{let e=[],r=[],u=T();for(let i of L.keys()){let p=L.get(i);if(p){let f={id:p.id,label:p.id,isGroup:!0,padding:u.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:u.look};e.push(f)}}for(let i of k.keys()){let p=k.get(i);if(p){let f=p;f.parentId=p.parent,f.look=u.look,e.push(f)}}let l=0;for(let i of re){l++;let p={id:i.id,label:i.text,isGroup:!1,shape:"note",padding:u.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${u.themeVariables.noteBkgColor}`,`stroke: ${u.themeVariables.noteBorderColor}`],look:u.look};e.push(p);let f=k.get(i.class)?.id??"";if(f){let b={id:`edgeNote${l}`,start:i.id,end:f,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:u.look};r.push(b)}}for(let i of Q){let p={id:i.id,label:i.label,isGroup:!1,shape:"rect",cssStyles:["opacity: 0;"],look:u.look};e.push(p)}l=0;for(let i of Ee){l++;let p={id:dt(i.id1,i.id2,{prefix:"id",counter:l}),start:i.id1,end:i.id2,type:"normal",label:i.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:We(i.relation.type1),arrowTypeEnd:We(i.relation.type2),startLabelRight:i.relationTitle1==="none"?"":i.relationTitle1,endLabelLeft:i.relationTitle2==="none"?"":i.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:i.style||"",pattern:i.relation.lineType==1?"dashed":"solid",look:u.look};r.push(p)}return{nodes:e,edges:r,other:{},config:u,direction:_t()}},"getData"),As={setAccTitle:ut,getAccTitle:lt,getAccDescription:ct,setAccDescription:ot,getConfig:n(()=>T().class,"getConfig"),addClass:Y,bindFunctions:Ht,clear:It,getClass:Ot,getClasses:wt,getNotes:Pt,addAnnotation:Mt,addNote:Ut,getRelations:Vt,addRelation:Rt,getDirection:_t,setDirection:Jt,addMember:Ft,addMembers:Gt,cleanupLabel:zt,lineType:qt,relationType:w,setClickEvent:jt,setCssClass:je,defineClass:Kt,setLink:Wt,getTooltip:Qt,setTooltip:Yt,lookUpDomId:Tt,setDiagramTitle:ht,getDiagramTitle:pt,setClassLabel:xt,addNamespace:Zt,addClassesToNamespace:ts,getNamespace:$t,getNamespaces:es,setCssStyle:ss,getData:rs},is=n(e=>`g.classGroup text {
  fill: ${e.nodeBorder||e.classText};
  stroke: none;
  font-family: ${e.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${e.classText};
}
.edgeLabel .label rect {
  fill: ${e.mainBkg};
}
.label text {
  fill: ${e.classText};
}

.labelBkg {
  background: ${e.mainBkg};
}
.edgeLabel .label span {
  background: ${e.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${e.mainBkg};
  stroke: ${e.nodeBorder};
}

g.classGroup line {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${e.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${e.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${e.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${e.textColor};
}
`,"getStyles"),ks=is,as=n((e,r="TB")=>{if(!e.doc)return r;let u=r;for(let l of e.doc)l.stmt==="dir"&&(u=l.value);return u},"getDir"),ns=n(function(e,r){return r.db.getClasses()},"getClasses"),us=n(async function(e,r,u,l){ge.info("REF0:"),ge.info("Drawing class diagram (v3)",r);let{securityLevel:i,state:p,layout:f}=T(),b=l.db.getData(),F=kt(r,i);b.type=l.type,b.layoutAlgorithm=At(f),b.nodeSpacing=p?.nodeSpacing||50,b.rankSpacing=p?.rankSpacing||50,b.markers=["aggregation","extension","composition","dependency","lollipop"],b.diagramId=r,await ft(b,F);let S=8;me.insertTitle(F,"classDiagramTitleText",p?.titleTopMargin??25,l.db.getDiagramTitle()),bt(F,S,"classDiagram",p?.useMaxWidth??!0)},"draw"),bs={getClasses:ns,draw:us,getDir:as};export{ds as a,As as b,ks as c,bs as d};
//# sourceMappingURL=chunk-54U54PUP.min.js.map
