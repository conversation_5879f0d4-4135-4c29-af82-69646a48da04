import{a as t,b as a,c as o,d as i,e as f,f as e,g as u,h as d,n as s,o as l}from"./chunk-BIJFJY5F.min.js";var m=class extends l{static{e(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},v={parser:{TokenBuilder:e(()=>new m,"TokenBuilder"),ValueConverter:e(()=>new s,"ValueConverter")}};function I(c=i){let r=o(a(c),u),n=o(t({shared:r}),d,v);return r.ServiceRegistry.register(n),{shared:r,Info:n}}e(I,"createInfoServices");export{v as a,I as b};
//# sourceMappingURL=chunk-33FU46FA.min.js.map
