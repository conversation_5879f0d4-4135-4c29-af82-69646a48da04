import{a as Ne}from"./chunk-WMZJ2DJX.min.js";import{a as $e}from"./chunk-5IIW54K6.min.js";import{a as Ze}from"./chunk-EKP7MBOP.min.js";import{a as qe}from"./chunk-WXIN66R4.min.js";import{a as ke}from"./chunk-I4ZXTPQC.min.js";import"./chunk-33FU46FA.min.js";import"./chunk-OZ2RCKQJ.min.js";import"./chunk-PDS7545E.min.js";import"./chunk-IJ4BRSPX.min.js";import"./chunk-UEFJDIUO.min.js";import"./chunk-BIJFJY5F.min.js";import"./chunk-U4DUTLYF.min.js";import"./chunk-IQQ46AC6.min.js";import{a as Ve,b as ze,c as fe}from"./chunk-BD4P4Z7J.min.js";import{d as ce}from"./chunk-AUO2PXKS.min.js";import"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{P as be,S as Pe,T as Ge,U as Ue,V as Ye,W as Xe,X as He,Y as We,Z as ae,h as at,ia as Be,j as Te,v as Fe}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import{a as me,d as ur}from"./chunk-OSRY5VT3.min.js";var Ce=me((ne,Le)=>{(function(w,L){typeof ne=="object"&&typeof Le=="object"?Le.exports=L():typeof define=="function"&&define.amd?define([],L):typeof ne=="object"?ne.layoutBase=L():w.layoutBase=L()})(ne,function(){return function(m){var w={};function L(u){if(w[u])return w[u].exports;var o=w[u]={i:u,l:!1,exports:{}};return m[u].call(o.exports,o,o.exports,L),o.l=!0,o.exports}return L.m=m,L.c=w,L.i=function(u){return u},L.d=function(u,o,n){L.o(u,o)||Object.defineProperty(u,o,{configurable:!1,enumerable:!0,get:n})},L.n=function(u){var o=u&&u.__esModule?function(){return u.default}:function(){return u};return L.d(o,"a",o),o},L.o=function(u,o){return Object.prototype.hasOwnProperty.call(u,o)},L.p="",L(L.s=28)}([function(m,w,L){"use strict";function u(){}u.QUALITY=1,u.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,u.DEFAULT_INCREMENTAL=!1,u.DEFAULT_ANIMATION_ON_LAYOUT=!0,u.DEFAULT_ANIMATION_DURING_LAYOUT=!1,u.DEFAULT_ANIMATION_PERIOD=50,u.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,u.DEFAULT_GRAPH_MARGIN=15,u.NODE_DIMENSIONS_INCLUDE_LABELS=!1,u.SIMPLE_NODE_SIZE=40,u.SIMPLE_NODE_HALF_SIZE=u.SIMPLE_NODE_SIZE/2,u.EMPTY_COMPOUND_NODE_SIZE=40,u.MIN_EDGE_LENGTH=1,u.WORLD_BOUNDARY=1e6,u.INITIAL_WORLD_BOUNDARY=u.WORLD_BOUNDARY/1e3,u.WORLD_CENTER_X=1200,u.WORLD_CENTER_Y=900,m.exports=u},function(m,w,L){"use strict";var u=L(2),o=L(8),n=L(9);function t(f,e,v){u.call(this,v),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=v,this.bendpoints=[],this.source=f,this.target=e}t.prototype=Object.create(u.prototype);for(var a in u)t[a]=u[a];t.prototype.getSource=function(){return this.source},t.prototype.getTarget=function(){return this.target},t.prototype.isInterGraph=function(){return this.isInterGraph},t.prototype.getLength=function(){return this.length},t.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},t.prototype.getBendpoints=function(){return this.bendpoints},t.prototype.getLca=function(){return this.lca},t.prototype.getSourceInLca=function(){return this.sourceInLca},t.prototype.getTargetInLca=function(){return this.targetInLca},t.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},t.prototype.getOtherEndInGraph=function(f,e){for(var v=this.getOtherEnd(f),i=e.getGraphManager().getRoot();;){if(v.getOwner()==e)return v;if(v.getOwner()==i)break;v=v.getOwner().getParent()}return null},t.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=o.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},t.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},m.exports=t},function(m,w,L){"use strict";function u(o){this.vGraphObject=o}m.exports=u},function(m,w,L){"use strict";var u=L(2),o=L(10),n=L(13),t=L(0),a=L(16),f=L(5);function e(i,r,h,c){h==null&&c==null&&(c=r),u.call(this,c),i.graphManager!=null&&(i=i.graphManager),this.estimatedSize=o.MIN_VALUE,this.inclusionTreeDepth=o.MAX_VALUE,this.vGraphObject=c,this.edges=[],this.graphManager=i,h!=null&&r!=null?this.rect=new n(r.x,r.y,h.width,h.height):this.rect=new n}e.prototype=Object.create(u.prototype);for(var v in u)e[v]=u[v];e.prototype.getEdges=function(){return this.edges},e.prototype.getChild=function(){return this.child},e.prototype.getOwner=function(){return this.owner},e.prototype.getWidth=function(){return this.rect.width},e.prototype.setWidth=function(i){this.rect.width=i},e.prototype.getHeight=function(){return this.rect.height},e.prototype.setHeight=function(i){this.rect.height=i},e.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},e.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},e.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},e.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},e.prototype.getRect=function(){return this.rect},e.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},e.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},e.prototype.setRect=function(i,r){this.rect.x=i.x,this.rect.y=i.y,this.rect.width=r.width,this.rect.height=r.height},e.prototype.setCenter=function(i,r){this.rect.x=i-this.rect.width/2,this.rect.y=r-this.rect.height/2},e.prototype.setLocation=function(i,r){this.rect.x=i,this.rect.y=r},e.prototype.moveBy=function(i,r){this.rect.x+=i,this.rect.y+=r},e.prototype.getEdgeListToNode=function(i){var r=[],h,c=this;return c.edges.forEach(function(l){if(l.target==i){if(l.source!=c)throw"Incorrect edge source!";r.push(l)}}),r},e.prototype.getEdgesBetween=function(i){var r=[],h,c=this;return c.edges.forEach(function(l){if(!(l.source==c||l.target==c))throw"Incorrect edge source and/or target";(l.target==i||l.source==i)&&r.push(l)}),r},e.prototype.getNeighborsList=function(){var i=new Set,r=this;return r.edges.forEach(function(h){if(h.source==r)i.add(h.target);else{if(h.target!=r)throw"Incorrect incidency!";i.add(h.source)}}),i},e.prototype.withChildren=function(){var i=new Set,r,h;if(i.add(this),this.child!=null)for(var c=this.child.getNodes(),l=0;l<c.length;l++)r=c[l],h=r.withChildren(),h.forEach(function(N){i.add(N)});return i},e.prototype.getNoOfChildren=function(){var i=0,r;if(this.child==null)i=1;else for(var h=this.child.getNodes(),c=0;c<h.length;c++)r=h[c],i+=r.getNoOfChildren();return i==0&&(i=1),i},e.prototype.getEstimatedSize=function(){if(this.estimatedSize==o.MIN_VALUE)throw"assert failed";return this.estimatedSize},e.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},e.prototype.scatter=function(){var i,r,h=-t.INITIAL_WORLD_BOUNDARY,c=t.INITIAL_WORLD_BOUNDARY;i=t.WORLD_CENTER_X+a.nextDouble()*(c-h)+h;var l=-t.INITIAL_WORLD_BOUNDARY,N=t.INITIAL_WORLD_BOUNDARY;r=t.WORLD_CENTER_Y+a.nextDouble()*(N-l)+l,this.rect.x=i,this.rect.y=r},e.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var i=this.getChild();if(i.updateBounds(!0),this.rect.x=i.getLeft(),this.rect.y=i.getTop(),this.setWidth(i.getRight()-i.getLeft()),this.setHeight(i.getBottom()-i.getTop()),t.NODE_DIMENSIONS_INCLUDE_LABELS){var r=i.getRight()-i.getLeft(),h=i.getBottom()-i.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(r+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>r?(this.rect.x-=(this.labelWidth-r)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(r+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(h+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>h?(this.rect.y-=(this.labelHeight-h)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(h+this.labelHeight))}}},e.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==o.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},e.prototype.transform=function(i){var r=this.rect.x;r>t.WORLD_BOUNDARY?r=t.WORLD_BOUNDARY:r<-t.WORLD_BOUNDARY&&(r=-t.WORLD_BOUNDARY);var h=this.rect.y;h>t.WORLD_BOUNDARY?h=t.WORLD_BOUNDARY:h<-t.WORLD_BOUNDARY&&(h=-t.WORLD_BOUNDARY);var c=new f(r,h),l=i.inverseTransformPoint(c);this.setLocation(l.x,l.y)},e.prototype.getLeft=function(){return this.rect.x},e.prototype.getRight=function(){return this.rect.x+this.rect.width},e.prototype.getTop=function(){return this.rect.y},e.prototype.getBottom=function(){return this.rect.y+this.rect.height},e.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},m.exports=e},function(m,w,L){"use strict";var u=L(0);function o(){}for(var n in u)o[n]=u[n];o.MAX_ITERATIONS=2500,o.DEFAULT_EDGE_LENGTH=50,o.DEFAULT_SPRING_STRENGTH=.45,o.DEFAULT_REPULSION_STRENGTH=4500,o.DEFAULT_GRAVITY_STRENGTH=.4,o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,o.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,o.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,o.COOLING_ADAPTATION_FACTOR=.33,o.ADAPTATION_LOWER_NODE_LIMIT=1e3,o.ADAPTATION_UPPER_NODE_LIMIT=5e3,o.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,o.MAX_NODE_DISPLACEMENT=o.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,o.MIN_REPULSION_DIST=o.DEFAULT_EDGE_LENGTH/10,o.CONVERGENCE_CHECK_PERIOD=100,o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,o.MIN_EDGE_LENGTH=1,o.GRID_CALCULATION_CHECK_PERIOD=10,m.exports=o},function(m,w,L){"use strict";function u(o,n){o==null&&n==null?(this.x=0,this.y=0):(this.x=o,this.y=n)}u.prototype.getX=function(){return this.x},u.prototype.getY=function(){return this.y},u.prototype.setX=function(o){this.x=o},u.prototype.setY=function(o){this.y=o},u.prototype.getDifference=function(o){return new DimensionD(this.x-o.x,this.y-o.y)},u.prototype.getCopy=function(){return new u(this.x,this.y)},u.prototype.translate=function(o){return this.x+=o.width,this.y+=o.height,this},m.exports=u},function(m,w,L){"use strict";var u=L(2),o=L(10),n=L(0),t=L(7),a=L(3),f=L(1),e=L(13),v=L(12),i=L(11);function r(c,l,N){u.call(this,N),this.estimatedSize=o.MIN_VALUE,this.margin=n.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=c,l!=null&&l instanceof t?this.graphManager=l:l!=null&&l instanceof Layout&&(this.graphManager=l.graphManager)}r.prototype=Object.create(u.prototype);for(var h in u)r[h]=u[h];r.prototype.getNodes=function(){return this.nodes},r.prototype.getEdges=function(){return this.edges},r.prototype.getGraphManager=function(){return this.graphManager},r.prototype.getParent=function(){return this.parent},r.prototype.getLeft=function(){return this.left},r.prototype.getRight=function(){return this.right},r.prototype.getTop=function(){return this.top},r.prototype.getBottom=function(){return this.bottom},r.prototype.isConnected=function(){return this.isConnected},r.prototype.add=function(c,l,N){if(l==null&&N==null){var p=c;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(p)>-1)throw"Node already in graph!";return p.owner=this,this.getNodes().push(p),p}else{var y=c;if(!(this.getNodes().indexOf(l)>-1&&this.getNodes().indexOf(N)>-1))throw"Source or target not in graph!";if(!(l.owner==N.owner&&l.owner==this))throw"Both owners must be this graph!";return l.owner!=N.owner?null:(y.source=l,y.target=N,y.isInterGraph=!1,this.getEdges().push(y),l.edges.push(y),N!=l&&N.edges.push(y),y)}},r.prototype.remove=function(c){var l=c;if(c instanceof a){if(l==null)throw"Node is null!";if(!(l.owner!=null&&l.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var N=l.edges.slice(),p,y=N.length,C=0;C<y;C++)p=N[C],p.isInterGraph?this.graphManager.remove(p):p.source.owner.remove(p);var F=this.nodes.indexOf(l);if(F==-1)throw"Node not in owner node list!";this.nodes.splice(F,1)}else if(c instanceof f){var p=c;if(p==null)throw"Edge is null!";if(!(p.source!=null&&p.target!=null))throw"Source and/or target is null!";if(!(p.source.owner!=null&&p.target.owner!=null&&p.source.owner==this&&p.target.owner==this))throw"Source and/or target owner is invalid!";var M=p.source.edges.indexOf(p),P=p.target.edges.indexOf(p);if(!(M>-1&&P>-1))throw"Source and/or target doesn't know this edge!";p.source.edges.splice(M,1),p.target!=p.source&&p.target.edges.splice(P,1);var F=p.source.owner.getEdges().indexOf(p);if(F==-1)throw"Not in owner's edge list!";p.source.owner.getEdges().splice(F,1)}},r.prototype.updateLeftTop=function(){for(var c=o.MAX_VALUE,l=o.MAX_VALUE,N,p,y,C=this.getNodes(),F=C.length,M=0;M<F;M++){var P=C[M];N=P.getTop(),p=P.getLeft(),c>N&&(c=N),l>p&&(l=p)}return c==o.MAX_VALUE?null:(C[0].getParent().paddingLeft!=null?y=C[0].getParent().paddingLeft:y=this.margin,this.left=l-y,this.top=c-y,new v(this.left,this.top))},r.prototype.updateBounds=function(c){for(var l=o.MAX_VALUE,N=-o.MAX_VALUE,p=o.MAX_VALUE,y=-o.MAX_VALUE,C,F,M,P,V,Y=this.nodes,et=Y.length,I=0;I<et;I++){var k=Y[I];c&&k.child!=null&&k.updateBounds(),C=k.getLeft(),F=k.getRight(),M=k.getTop(),P=k.getBottom(),l>C&&(l=C),N<F&&(N=F),p>M&&(p=M),y<P&&(y=P)}var s=new e(l,p,N-l,y-p);l==o.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),Y[0].getParent().paddingLeft!=null?V=Y[0].getParent().paddingLeft:V=this.margin,this.left=s.x-V,this.right=s.x+s.width+V,this.top=s.y-V,this.bottom=s.y+s.height+V},r.calculateBounds=function(c){for(var l=o.MAX_VALUE,N=-o.MAX_VALUE,p=o.MAX_VALUE,y=-o.MAX_VALUE,C,F,M,P,V=c.length,Y=0;Y<V;Y++){var et=c[Y];C=et.getLeft(),F=et.getRight(),M=et.getTop(),P=et.getBottom(),l>C&&(l=C),N<F&&(N=F),p>M&&(p=M),y<P&&(y=P)}var I=new e(l,p,N-l,y-p);return I},r.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},r.prototype.getEstimatedSize=function(){if(this.estimatedSize==o.MIN_VALUE)throw"assert failed";return this.estimatedSize},r.prototype.calcEstimatedSize=function(){for(var c=0,l=this.nodes,N=l.length,p=0;p<N;p++){var y=l[p];c+=y.calcEstimatedSize()}return c==0?this.estimatedSize=n.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=c/Math.sqrt(this.nodes.length),this.estimatedSize},r.prototype.updateConnected=function(){var c=this;if(this.nodes.length==0){this.isConnected=!0;return}var l=new i,N=new Set,p=this.nodes[0],y,C,F=p.withChildren();for(F.forEach(function(I){l.push(I),N.add(I)});l.length!==0;){p=l.shift(),y=p.getEdges();for(var M=y.length,P=0;P<M;P++){var V=y[P];if(C=V.getOtherEndInGraph(p,this),C!=null&&!N.has(C)){var Y=C.withChildren();Y.forEach(function(I){l.push(I),N.add(I)})}}}if(this.isConnected=!1,N.size>=this.nodes.length){var et=0;N.forEach(function(I){I.owner==c&&et++}),et==this.nodes.length&&(this.isConnected=!0)}},m.exports=r},function(m,w,L){"use strict";var u,o=L(1);function n(t){u=L(6),this.layout=t,this.graphs=[],this.edges=[]}n.prototype.addRoot=function(){var t=this.layout.newGraph(),a=this.layout.newNode(null),f=this.add(t,a);return this.setRootGraph(f),this.rootGraph},n.prototype.add=function(t,a,f,e,v){if(f==null&&e==null&&v==null){if(t==null)throw"Graph is null!";if(a==null)throw"Parent node is null!";if(this.graphs.indexOf(t)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(t),t.parent!=null)throw"Already has a parent!";if(a.child!=null)throw"Already has a child!";return t.parent=a,a.child=t,t}else{v=f,e=a,f=t;var i=e.getOwner(),r=v.getOwner();if(!(i!=null&&i.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(r!=null&&r.getGraphManager()==this))throw"Target not in this graph mgr!";if(i==r)return f.isInterGraph=!1,i.add(f,e,v);if(f.isInterGraph=!0,f.source=e,f.target=v,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},n.prototype.remove=function(t){if(t instanceof u){var a=t;if(a.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(a==this.rootGraph||a.parent!=null&&a.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(a.getEdges());for(var e,v=f.length,i=0;i<v;i++)e=f[i],a.remove(e);var r=[];r=r.concat(a.getNodes());var h;v=r.length;for(var i=0;i<v;i++)h=r[i],a.remove(h);a==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(a);this.graphs.splice(c,1),a.parent=null}else if(t instanceof o){if(e=t,e==null)throw"Edge is null!";if(!e.isInterGraph)throw"Not an inter-graph edge!";if(!(e.source!=null&&e.target!=null))throw"Source and/or target is null!";if(!(e.source.edges.indexOf(e)!=-1&&e.target.edges.indexOf(e)!=-1))throw"Source and/or target doesn't know this edge!";var c=e.source.edges.indexOf(e);if(e.source.edges.splice(c,1),c=e.target.edges.indexOf(e),e.target.edges.splice(c,1),!(e.source.owner!=null&&e.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(e.source.owner.getGraphManager().edges.indexOf(e)==-1)throw"Not in owner graph manager's edge list!";var c=e.source.owner.getGraphManager().edges.indexOf(e);e.source.owner.getGraphManager().edges.splice(c,1)}},n.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},n.prototype.getGraphs=function(){return this.graphs},n.prototype.getAllNodes=function(){if(this.allNodes==null){for(var t=[],a=this.getGraphs(),f=a.length,e=0;e<f;e++)t=t.concat(a[e].getNodes());this.allNodes=t}return this.allNodes},n.prototype.resetAllNodes=function(){this.allNodes=null},n.prototype.resetAllEdges=function(){this.allEdges=null},n.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},n.prototype.getAllEdges=function(){if(this.allEdges==null){for(var t=[],a=this.getGraphs(),f=a.length,e=0;e<a.length;e++)t=t.concat(a[e].getEdges());t=t.concat(this.edges),this.allEdges=t}return this.allEdges},n.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},n.prototype.setAllNodesToApplyGravitation=function(t){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=t},n.prototype.getRoot=function(){return this.rootGraph},n.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=t,t.parent==null&&(t.parent=this.layout.newNode("Root node"))},n.prototype.getLayout=function(){return this.layout},n.prototype.isOneAncestorOfOther=function(t,a){if(!(t!=null&&a!=null))throw"assert failed";if(t==a)return!0;var f=t.getOwner(),e;do{if(e=f.getParent(),e==null)break;if(e==a)return!0;if(f=e.getOwner(),f==null)break}while(!0);f=a.getOwner();do{if(e=f.getParent(),e==null)break;if(e==t)return!0;if(f=e.getOwner(),f==null)break}while(!0);return!1},n.prototype.calcLowestCommonAncestors=function(){for(var t,a,f,e,v,i=this.getAllEdges(),r=i.length,h=0;h<r;h++){if(t=i[h],a=t.source,f=t.target,t.lca=null,t.sourceInLca=a,t.targetInLca=f,a==f){t.lca=a.getOwner();continue}for(e=a.getOwner();t.lca==null;){for(t.targetInLca=f,v=f.getOwner();t.lca==null;){if(v==e){t.lca=v;break}if(v==this.rootGraph)break;if(t.lca!=null)throw"assert failed";t.targetInLca=v.getParent(),v=t.targetInLca.getOwner()}if(e==this.rootGraph)break;t.lca==null&&(t.sourceInLca=e.getParent(),e=t.sourceInLca.getOwner())}if(t.lca==null)throw"assert failed"}},n.prototype.calcLowestCommonAncestor=function(t,a){if(t==a)return t.getOwner();var f=t.getOwner();do{if(f==null)break;var e=a.getOwner();do{if(e==null)break;if(e==f)return e;e=e.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},n.prototype.calcInclusionTreeDepths=function(t,a){t==null&&a==null&&(t=this.rootGraph,a=1);for(var f,e=t.getNodes(),v=e.length,i=0;i<v;i++)f=e[i],f.inclusionTreeDepth=a,f.child!=null&&this.calcInclusionTreeDepths(f.child,a+1)},n.prototype.includesInvalidEdge=function(){for(var t,a=[],f=this.edges.length,e=0;e<f;e++)t=this.edges[e],this.isOneAncestorOfOther(t.source,t.target)&&a.push(t);for(var e=0;e<a.length;e++)this.remove(a[e]);return!1},m.exports=n},function(m,w,L){"use strict";var u=L(12);function o(){}o.calcSeparationAmount=function(n,t,a,f){if(!n.intersects(t))throw"assert failed";var e=new Array(2);this.decideDirectionsForOverlappingNodes(n,t,e),a[0]=Math.min(n.getRight(),t.getRight())-Math.max(n.x,t.x),a[1]=Math.min(n.getBottom(),t.getBottom())-Math.max(n.y,t.y),n.getX()<=t.getX()&&n.getRight()>=t.getRight()?a[0]+=Math.min(t.getX()-n.getX(),n.getRight()-t.getRight()):t.getX()<=n.getX()&&t.getRight()>=n.getRight()&&(a[0]+=Math.min(n.getX()-t.getX(),t.getRight()-n.getRight())),n.getY()<=t.getY()&&n.getBottom()>=t.getBottom()?a[1]+=Math.min(t.getY()-n.getY(),n.getBottom()-t.getBottom()):t.getY()<=n.getY()&&t.getBottom()>=n.getBottom()&&(a[1]+=Math.min(n.getY()-t.getY(),t.getBottom()-n.getBottom()));var v=Math.abs((t.getCenterY()-n.getCenterY())/(t.getCenterX()-n.getCenterX()));t.getCenterY()===n.getCenterY()&&t.getCenterX()===n.getCenterX()&&(v=1);var i=v*a[0],r=a[1]/v;a[0]<r?r=a[0]:i=a[1],a[0]=-1*e[0]*(r/2+f),a[1]=-1*e[1]*(i/2+f)},o.decideDirectionsForOverlappingNodes=function(n,t,a){n.getCenterX()<t.getCenterX()?a[0]=-1:a[0]=1,n.getCenterY()<t.getCenterY()?a[1]=-1:a[1]=1},o.getIntersection2=function(n,t,a){var f=n.getCenterX(),e=n.getCenterY(),v=t.getCenterX(),i=t.getCenterY();if(n.intersects(t))return a[0]=f,a[1]=e,a[2]=v,a[3]=i,!0;var r=n.getX(),h=n.getY(),c=n.getRight(),l=n.getX(),N=n.getBottom(),p=n.getRight(),y=n.getWidthHalf(),C=n.getHeightHalf(),F=t.getX(),M=t.getY(),P=t.getRight(),V=t.getX(),Y=t.getBottom(),et=t.getRight(),I=t.getWidthHalf(),k=t.getHeightHalf(),s=!1,E=!1;if(f===v){if(e>i)return a[0]=f,a[1]=h,a[2]=v,a[3]=Y,!1;if(e<i)return a[0]=f,a[1]=N,a[2]=v,a[3]=M,!1}else if(e===i){if(f>v)return a[0]=r,a[1]=e,a[2]=P,a[3]=i,!1;if(f<v)return a[0]=c,a[1]=e,a[2]=F,a[3]=i,!1}else{var g=n.height/n.width,T=t.height/t.width,d=(i-e)/(v-f),D=void 0,O=void 0,b=void 0,R=void 0,x=void 0,Z=void 0;if(-g===d?f>v?(a[0]=l,a[1]=N,s=!0):(a[0]=c,a[1]=h,s=!0):g===d&&(f>v?(a[0]=r,a[1]=h,s=!0):(a[0]=p,a[1]=N,s=!0)),-T===d?v>f?(a[2]=V,a[3]=Y,E=!0):(a[2]=P,a[3]=M,E=!0):T===d&&(v>f?(a[2]=F,a[3]=M,E=!0):(a[2]=et,a[3]=Y,E=!0)),s&&E)return!1;if(f>v?e>i?(D=this.getCardinalDirection(g,d,4),O=this.getCardinalDirection(T,d,2)):(D=this.getCardinalDirection(-g,d,3),O=this.getCardinalDirection(-T,d,1)):e>i?(D=this.getCardinalDirection(-g,d,1),O=this.getCardinalDirection(-T,d,3)):(D=this.getCardinalDirection(g,d,2),O=this.getCardinalDirection(T,d,4)),!s)switch(D){case 1:R=h,b=f+-C/d,a[0]=b,a[1]=R;break;case 2:b=p,R=e+y*d,a[0]=b,a[1]=R;break;case 3:R=N,b=f+C/d,a[0]=b,a[1]=R;break;case 4:b=l,R=e+-y*d,a[0]=b,a[1]=R;break}if(!E)switch(O){case 1:Z=M,x=v+-k/d,a[2]=x,a[3]=Z;break;case 2:x=et,Z=i+I*d,a[2]=x,a[3]=Z;break;case 3:Z=Y,x=v+k/d,a[2]=x,a[3]=Z;break;case 4:x=V,Z=i+-I*d,a[2]=x,a[3]=Z;break}}return!1},o.getCardinalDirection=function(n,t,a){return n>t?a:1+a%4},o.getIntersection=function(n,t,a,f){if(f==null)return this.getIntersection2(n,t,a);var e=n.x,v=n.y,i=t.x,r=t.y,h=a.x,c=a.y,l=f.x,N=f.y,p=void 0,y=void 0,C=void 0,F=void 0,M=void 0,P=void 0,V=void 0,Y=void 0,et=void 0;return C=r-v,M=e-i,V=i*v-e*r,F=N-c,P=h-l,Y=l*c-h*N,et=C*P-F*M,et===0?null:(p=(M*Y-P*V)/et,y=(F*V-C*Y)/et,new u(p,y))},o.angleOfVector=function(n,t,a,f){var e=void 0;return n!==a?(e=Math.atan((f-t)/(a-n)),a<n?e+=Math.PI:f<t&&(e+=this.TWO_PI)):f<t?e=this.ONE_AND_HALF_PI:e=this.HALF_PI,e},o.doIntersect=function(n,t,a,f){var e=n.x,v=n.y,i=t.x,r=t.y,h=a.x,c=a.y,l=f.x,N=f.y,p=(i-e)*(N-c)-(l-h)*(r-v);if(p===0)return!1;var y=((N-c)*(l-e)+(h-l)*(N-v))/p,C=((v-r)*(l-e)+(i-e)*(N-v))/p;return 0<y&&y<1&&0<C&&C<1},o.findCircleLineIntersections=function(n,t,a,f,e,v,i){var r=(a-n)*(a-n)+(f-t)*(f-t),h=2*((n-e)*(a-n)+(t-v)*(f-t)),c=(n-e)*(n-e)+(t-v)*(t-v)-i*i,l=h*h-4*r*c;if(l>=0){var N=(-h+Math.sqrt(h*h-4*r*c))/(2*r),p=(-h-Math.sqrt(h*h-4*r*c))/(2*r),y=null;return N>=0&&N<=1?[N]:p>=0&&p<=1?[p]:y}else return null},o.HALF_PI=.5*Math.PI,o.ONE_AND_HALF_PI=1.5*Math.PI,o.TWO_PI=2*Math.PI,o.THREE_PI=3*Math.PI,m.exports=o},function(m,w,L){"use strict";function u(){}u.sign=function(o){return o>0?1:o<0?-1:0},u.floor=function(o){return o<0?Math.ceil(o):Math.floor(o)},u.ceil=function(o){return o<0?Math.floor(o):Math.ceil(o)},m.exports=u},function(m,w,L){"use strict";function u(){}u.MAX_VALUE=2147483647,u.MIN_VALUE=-2147483648,m.exports=u},function(m,w,L){"use strict";var u=function(){function e(v,i){for(var r=0;r<i.length;r++){var h=i[r];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,i,r){return i&&e(v.prototype,i),r&&e(v,r),v}}();function o(e,v){if(!(e instanceof v))throw new TypeError("Cannot call a class as a function")}var n=function(v){return{value:v,next:null,prev:null}},t=function(v,i,r,h){return v!==null?v.next=i:h.head=i,r!==null?r.prev=i:h.tail=i,i.prev=v,i.next=r,h.length++,i},a=function(v,i){var r=v.prev,h=v.next;return r!==null?r.next=h:i.head=h,h!==null?h.prev=r:i.tail=r,v.prev=v.next=null,i.length--,v},f=function(){function e(v){var i=this;o(this,e),this.length=0,this.head=null,this.tail=null,v?.forEach(function(r){return i.push(r)})}return u(e,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(i,r){return t(r.prev,n(i),r,this)}},{key:"insertAfter",value:function(i,r){return t(r,n(i),r.next,this)}},{key:"insertNodeBefore",value:function(i,r){return t(r.prev,i,r,this)}},{key:"insertNodeAfter",value:function(i,r){return t(r,i,r.next,this)}},{key:"push",value:function(i){return t(this.tail,n(i),null,this)}},{key:"unshift",value:function(i){return t(null,n(i),this.head,this)}},{key:"remove",value:function(i){return a(i,this)}},{key:"pop",value:function(){return a(this.tail,this).value}},{key:"popNode",value:function(){return a(this.tail,this)}},{key:"shift",value:function(){return a(this.head,this).value}},{key:"shiftNode",value:function(){return a(this.head,this)}},{key:"get_object_at",value:function(i){if(i<=this.length()){for(var r=1,h=this.head;r<i;)h=h.next,r++;return h.value}}},{key:"set_object_at",value:function(i,r){if(i<=this.length()){for(var h=1,c=this.head;h<i;)c=c.next,h++;c.value=r}}}]),e}();m.exports=f},function(m,w,L){"use strict";function u(o,n,t){this.x=null,this.y=null,o==null&&n==null&&t==null?(this.x=0,this.y=0):typeof o=="number"&&typeof n=="number"&&t==null?(this.x=o,this.y=n):o.constructor.name=="Point"&&n==null&&t==null&&(t=o,this.x=t.x,this.y=t.y)}u.prototype.getX=function(){return this.x},u.prototype.getY=function(){return this.y},u.prototype.getLocation=function(){return new u(this.x,this.y)},u.prototype.setLocation=function(o,n,t){o.constructor.name=="Point"&&n==null&&t==null?(t=o,this.setLocation(t.x,t.y)):typeof o=="number"&&typeof n=="number"&&t==null&&(parseInt(o)==o&&parseInt(n)==n?this.move(o,n):(this.x=Math.floor(o+.5),this.y=Math.floor(n+.5)))},u.prototype.move=function(o,n){this.x=o,this.y=n},u.prototype.translate=function(o,n){this.x+=o,this.y+=n},u.prototype.equals=function(o){if(o.constructor.name=="Point"){var n=o;return this.x==n.x&&this.y==n.y}return this==o},u.prototype.toString=function(){return new u().constructor.name+"[x="+this.x+",y="+this.y+"]"},m.exports=u},function(m,w,L){"use strict";function u(o,n,t,a){this.x=0,this.y=0,this.width=0,this.height=0,o!=null&&n!=null&&t!=null&&a!=null&&(this.x=o,this.y=n,this.width=t,this.height=a)}u.prototype.getX=function(){return this.x},u.prototype.setX=function(o){this.x=o},u.prototype.getY=function(){return this.y},u.prototype.setY=function(o){this.y=o},u.prototype.getWidth=function(){return this.width},u.prototype.setWidth=function(o){this.width=o},u.prototype.getHeight=function(){return this.height},u.prototype.setHeight=function(o){this.height=o},u.prototype.getRight=function(){return this.x+this.width},u.prototype.getBottom=function(){return this.y+this.height},u.prototype.intersects=function(o){return!(this.getRight()<o.x||this.getBottom()<o.y||o.getRight()<this.x||o.getBottom()<this.y)},u.prototype.getCenterX=function(){return this.x+this.width/2},u.prototype.getMinX=function(){return this.getX()},u.prototype.getMaxX=function(){return this.getX()+this.width},u.prototype.getCenterY=function(){return this.y+this.height/2},u.prototype.getMinY=function(){return this.getY()},u.prototype.getMaxY=function(){return this.getY()+this.height},u.prototype.getWidthHalf=function(){return this.width/2},u.prototype.getHeightHalf=function(){return this.height/2},m.exports=u},function(m,w,L){"use strict";var u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n};function o(){}o.lastID=0,o.createID=function(n){return o.isPrimitive(n)?n:(n.uniqueID!=null||(n.uniqueID=o.getString(),o.lastID++),n.uniqueID)},o.getString=function(n){return n==null&&(n=o.lastID),"Object#"+n},o.isPrimitive=function(n){var t=typeof n>"u"?"undefined":u(n);return n==null||t!="object"&&t!="function"},m.exports=o},function(m,w,L){"use strict";function u(h){if(Array.isArray(h)){for(var c=0,l=Array(h.length);c<h.length;c++)l[c]=h[c];return l}else return Array.from(h)}var o=L(0),n=L(7),t=L(3),a=L(1),f=L(6),e=L(5),v=L(17),i=L(29);function r(h){i.call(this),this.layoutQuality=o.QUALITY,this.createBendsAsNeeded=o.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=o.DEFAULT_INCREMENTAL,this.animationOnLayout=o.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=o.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=o.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=o.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new n(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,h!=null&&(this.isRemoteUse=h)}r.RANDOM_SEED=1,r.prototype=Object.create(i.prototype),r.prototype.getGraphManager=function(){return this.graphManager},r.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},r.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},r.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},r.prototype.newGraphManager=function(){var h=new n(this);return this.graphManager=h,h},r.prototype.newGraph=function(h){return new f(null,this.graphManager,h)},r.prototype.newNode=function(h){return new t(this.graphManager,h)},r.prototype.newEdge=function(h){return new a(null,null,h)},r.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},r.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var h;return this.checkLayoutSuccess()?h=!1:h=this.layout(),o.ANIMATE==="during"?!1:(h&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,h)},r.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},r.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var h,c=this.graphManager.getAllEdges(),l=0;l<c.length;l++)h=c[l];for(var N,p=this.graphManager.getRoot().getNodes(),l=0;l<p.length;l++)N=p[l];this.update(this.graphManager.getRoot())}},r.prototype.update=function(h){if(h==null)this.update2();else if(h instanceof t){var c=h;if(c.getChild()!=null)for(var l=c.getChild().getNodes(),N=0;N<l.length;N++)update(l[N]);if(c.vGraphObject!=null){var p=c.vGraphObject;p.update(c)}}else if(h instanceof a){var y=h;if(y.vGraphObject!=null){var C=y.vGraphObject;C.update(y)}}else if(h instanceof f){var F=h;if(F.vGraphObject!=null){var M=F.vGraphObject;M.update(F)}}},r.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=o.QUALITY,this.animationDuringLayout=o.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=o.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=o.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=o.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=o.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=o.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},r.prototype.transform=function(h){if(h==null)this.transform(new e(0,0));else{var c=new v,l=this.graphManager.getRoot().updateLeftTop();if(l!=null){c.setWorldOrgX(h.x),c.setWorldOrgY(h.y),c.setDeviceOrgX(l.x),c.setDeviceOrgY(l.y);for(var N=this.getAllNodes(),p,y=0;y<N.length;y++)p=N[y],p.transform(c)}}},r.prototype.positionNodesRandomly=function(h){if(h==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var c,l,N=h.getNodes(),p=0;p<N.length;p++)c=N[p],l=c.getChild(),l==null||l.getNodes().length==0?c.scatter():(this.positionNodesRandomly(l),c.updateBounds())},r.prototype.getFlatForest=function(){for(var h=[],c=!0,l=this.graphManager.getRoot().getNodes(),N=!0,p=0;p<l.length;p++)l[p].getChild()!=null&&(N=!1);if(!N)return h;var y=new Set,C=[],F=new Map,M=[];for(M=M.concat(l);M.length>0&&c;){for(C.push(M[0]);C.length>0&&c;){var P=C[0];C.splice(0,1),y.add(P);for(var V=P.getEdges(),p=0;p<V.length;p++){var Y=V[p].getOtherEnd(P);if(F.get(P)!=Y)if(!y.has(Y))C.push(Y),F.set(Y,P);else{c=!1;break}}}if(!c)h=[];else{var et=[].concat(u(y));h.push(et);for(var p=0;p<et.length;p++){var I=et[p],k=M.indexOf(I);k>-1&&M.splice(k,1)}y=new Set,F=new Map}}return h},r.prototype.createDummyNodesForBendpoints=function(h){for(var c=[],l=h.source,N=this.graphManager.calcLowestCommonAncestor(h.source,h.target),p=0;p<h.bendpoints.length;p++){var y=this.newNode(null);y.setRect(new Point(0,0),new Dimension(1,1)),N.add(y);var C=this.newEdge(null);this.graphManager.add(C,l,y),c.add(y),l=y}var C=this.newEdge(null);return this.graphManager.add(C,l,h.target),this.edgeToDummyNodes.set(h,c),h.isInterGraph()?this.graphManager.remove(h):N.remove(h),c},r.prototype.createBendpointsFromDummyNodes=function(){var h=[];h=h.concat(this.graphManager.getAllEdges()),h=[].concat(u(this.edgeToDummyNodes.keys())).concat(h);for(var c=0;c<h.length;c++){var l=h[c];if(l.bendpoints.length>0){for(var N=this.edgeToDummyNodes.get(l),p=0;p<N.length;p++){var y=N[p],C=new e(y.getCenterX(),y.getCenterY()),F=l.bendpoints.get(p);F.x=C.x,F.y=C.y,y.getOwner().remove(y)}this.graphManager.add(l,l.source,l.target)}}},r.transform=function(h,c,l,N){if(l!=null&&N!=null){var p=c;if(h<=50){var y=c/l;p-=(c-y)/50*(50-h)}else{var C=c*N;p+=(C-c)/50*(h-50)}return p}else{var F,M;return h<=50?(F=9*c/500,M=c/10):(F=9*c/50,M=-8*c),F*h+M}},r.findCenterOfTree=function(h){var c=[];c=c.concat(h);var l=[],N=new Map,p=!1,y=null;(c.length==1||c.length==2)&&(p=!0,y=c[0]);for(var C=0;C<c.length;C++){var F=c[C],M=F.getNeighborsList().size;N.set(F,F.getNeighborsList().size),M==1&&l.push(F)}var P=[];for(P=P.concat(l);!p;){var V=[];V=V.concat(P),P=[];for(var C=0;C<c.length;C++){var F=c[C],Y=c.indexOf(F);Y>=0&&c.splice(Y,1);var et=F.getNeighborsList();et.forEach(function(s){if(l.indexOf(s)<0){var E=N.get(s),g=E-1;g==1&&P.push(s),N.set(s,g)}})}l=l.concat(P),(c.length==1||c.length==2)&&(p=!0,y=c[0])}return y},r.prototype.setGraphManager=function(h){this.graphManager=h},m.exports=r},function(m,w,L){"use strict";function u(){}u.seed=1,u.x=0,u.nextDouble=function(){return u.x=Math.sin(u.seed++)*1e4,u.x-Math.floor(u.x)},m.exports=u},function(m,w,L){"use strict";var u=L(5);function o(n,t){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}o.prototype.getWorldOrgX=function(){return this.lworldOrgX},o.prototype.setWorldOrgX=function(n){this.lworldOrgX=n},o.prototype.getWorldOrgY=function(){return this.lworldOrgY},o.prototype.setWorldOrgY=function(n){this.lworldOrgY=n},o.prototype.getWorldExtX=function(){return this.lworldExtX},o.prototype.setWorldExtX=function(n){this.lworldExtX=n},o.prototype.getWorldExtY=function(){return this.lworldExtY},o.prototype.setWorldExtY=function(n){this.lworldExtY=n},o.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},o.prototype.setDeviceOrgX=function(n){this.ldeviceOrgX=n},o.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},o.prototype.setDeviceOrgY=function(n){this.ldeviceOrgY=n},o.prototype.getDeviceExtX=function(){return this.ldeviceExtX},o.prototype.setDeviceExtX=function(n){this.ldeviceExtX=n},o.prototype.getDeviceExtY=function(){return this.ldeviceExtY},o.prototype.setDeviceExtY=function(n){this.ldeviceExtY=n},o.prototype.transformX=function(n){var t=0,a=this.lworldExtX;return a!=0&&(t=this.ldeviceOrgX+(n-this.lworldOrgX)*this.ldeviceExtX/a),t},o.prototype.transformY=function(n){var t=0,a=this.lworldExtY;return a!=0&&(t=this.ldeviceOrgY+(n-this.lworldOrgY)*this.ldeviceExtY/a),t},o.prototype.inverseTransformX=function(n){var t=0,a=this.ldeviceExtX;return a!=0&&(t=this.lworldOrgX+(n-this.ldeviceOrgX)*this.lworldExtX/a),t},o.prototype.inverseTransformY=function(n){var t=0,a=this.ldeviceExtY;return a!=0&&(t=this.lworldOrgY+(n-this.ldeviceOrgY)*this.lworldExtY/a),t},o.prototype.inverseTransformPoint=function(n){var t=new u(this.inverseTransformX(n.x),this.inverseTransformY(n.y));return t},m.exports=o},function(m,w,L){"use strict";function u(i){if(Array.isArray(i)){for(var r=0,h=Array(i.length);r<i.length;r++)h[r]=i[r];return h}else return Array.from(i)}var o=L(15),n=L(4),t=L(0),a=L(8),f=L(9);function e(){o.call(this),this.useSmartIdealEdgeLengthCalculation=n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=n.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=n.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=n.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=n.MAX_ITERATIONS}e.prototype=Object.create(o.prototype);for(var v in o)e[v]=o[v];e.prototype.initParameters=function(){o.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},e.prototype.calcIdealEdgeLengths=function(){for(var i,r,h,c,l,N,p,y=this.getGraphManager().getAllEdges(),C=0;C<y.length;C++)i=y[C],r=i.idealLength,i.isInterGraph&&(c=i.getSource(),l=i.getTarget(),N=i.getSourceInLca().getEstimatedSize(),p=i.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(i.idealLength+=N+p-2*t.SIMPLE_NODE_SIZE),h=i.getLca().getInclusionTreeDepth(),i.idealLength+=r*n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+l.getInclusionTreeDepth()-2*h))},e.prototype.initSpringEmbedder=function(){var i=this.getAllNodes().length;this.incremental?(i>n.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*n.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(i-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-n.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT_INCREMENTAL):(i>n.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(n.COOLING_ADAPTATION_FACTOR,1-(i-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*(1-n.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},e.prototype.calcSpringForces=function(){for(var i=this.getAllEdges(),r,h=0;h<i.length;h++)r=i[h],this.calcSpringForce(r,r.idealLength)},e.prototype.calcRepulsionForces=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,h,c,l,N,p=this.getAllNodes(),y;if(this.useFRGridVariant)for(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&i&&this.updateGrid(),y=new Set,h=0;h<p.length;h++)l=p[h],this.calculateRepulsionForceOfANode(l,y,i,r),y.add(l);else for(h=0;h<p.length;h++)for(l=p[h],c=h+1;c<p.length;c++)N=p[c],l.getOwner()==N.getOwner()&&this.calcRepulsionForce(l,N)},e.prototype.calcGravitationalForces=function(){for(var i,r=this.getAllNodesToApplyGravitation(),h=0;h<r.length;h++)i=r[h],this.calcGravitationalForce(i)},e.prototype.moveNodes=function(){for(var i=this.getAllNodes(),r,h=0;h<i.length;h++)r=i[h],r.move()},e.prototype.calcSpringForce=function(i,r){var h=i.getSource(),c=i.getTarget(),l,N,p,y;if(this.uniformLeafNodeSizes&&h.getChild()==null&&c.getChild()==null)i.updateLengthSimple();else if(i.updateLength(),i.isOverlapingSourceAndTarget)return;l=i.getLength(),l!=0&&(N=i.edgeElasticity*(l-r),p=N*(i.lengthX/l),y=N*(i.lengthY/l),h.springForceX+=p,h.springForceY+=y,c.springForceX-=p,c.springForceY-=y)},e.prototype.calcRepulsionForce=function(i,r){var h=i.getRect(),c=r.getRect(),l=new Array(2),N=new Array(4),p,y,C,F,M,P,V;if(h.intersects(c)){a.calcSeparationAmount(h,c,l,n.DEFAULT_EDGE_LENGTH/2),P=2*l[0],V=2*l[1];var Y=i.noOfChildren*r.noOfChildren/(i.noOfChildren+r.noOfChildren);i.repulsionForceX-=Y*P,i.repulsionForceY-=Y*V,r.repulsionForceX+=Y*P,r.repulsionForceY+=Y*V}else this.uniformLeafNodeSizes&&i.getChild()==null&&r.getChild()==null?(p=c.getCenterX()-h.getCenterX(),y=c.getCenterY()-h.getCenterY()):(a.getIntersection(h,c,N),p=N[2]-N[0],y=N[3]-N[1]),Math.abs(p)<n.MIN_REPULSION_DIST&&(p=f.sign(p)*n.MIN_REPULSION_DIST),Math.abs(y)<n.MIN_REPULSION_DIST&&(y=f.sign(y)*n.MIN_REPULSION_DIST),C=p*p+y*y,F=Math.sqrt(C),M=(i.nodeRepulsion/2+r.nodeRepulsion/2)*i.noOfChildren*r.noOfChildren/C,P=M*p/F,V=M*y/F,i.repulsionForceX-=P,i.repulsionForceY-=V,r.repulsionForceX+=P,r.repulsionForceY+=V},e.prototype.calcGravitationalForce=function(i){var r,h,c,l,N,p,y,C;r=i.getOwner(),h=(r.getRight()+r.getLeft())/2,c=(r.getTop()+r.getBottom())/2,l=i.getCenterX()-h,N=i.getCenterY()-c,p=Math.abs(l)+i.getWidth()/2,y=Math.abs(N)+i.getHeight()/2,i.getOwner()==this.graphManager.getRoot()?(C=r.getEstimatedSize()*this.gravityRangeFactor,(p>C||y>C)&&(i.gravitationForceX=-this.gravityConstant*l,i.gravitationForceY=-this.gravityConstant*N)):(C=r.getEstimatedSize()*this.compoundGravityRangeFactor,(p>C||y>C)&&(i.gravitationForceX=-this.gravityConstant*l*this.compoundGravityConstant,i.gravitationForceY=-this.gravityConstant*N*this.compoundGravityConstant))},e.prototype.isConverged=function(){var i,r=!1;return this.totalIterations>this.maxIterations/3&&(r=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),i=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,i||r},e.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},e.prototype.calcNoOfChildrenForAllNodes=function(){for(var i,r=this.graphManager.getAllNodes(),h=0;h<r.length;h++)i=r[h],i.noOfChildren=i.getNoOfChildren()},e.prototype.calcGrid=function(i){var r=0,h=0;r=parseInt(Math.ceil((i.getRight()-i.getLeft())/this.repulsionRange)),h=parseInt(Math.ceil((i.getBottom()-i.getTop())/this.repulsionRange));for(var c=new Array(r),l=0;l<r;l++)c[l]=new Array(h);for(var l=0;l<r;l++)for(var N=0;N<h;N++)c[l][N]=new Array;return c},e.prototype.addNodeToGrid=function(i,r,h){var c=0,l=0,N=0,p=0;c=parseInt(Math.floor((i.getRect().x-r)/this.repulsionRange)),l=parseInt(Math.floor((i.getRect().width+i.getRect().x-r)/this.repulsionRange)),N=parseInt(Math.floor((i.getRect().y-h)/this.repulsionRange)),p=parseInt(Math.floor((i.getRect().height+i.getRect().y-h)/this.repulsionRange));for(var y=c;y<=l;y++)for(var C=N;C<=p;C++)this.grid[y][C].push(i),i.setGridCoordinates(c,l,N,p)},e.prototype.updateGrid=function(){var i,r,h=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),i=0;i<h.length;i++)r=h[i],this.addNodeToGrid(r,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},e.prototype.calculateRepulsionForceOfANode=function(i,r,h,c){if(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&h||c){var l=new Set;i.surrounding=new Array;for(var N,p=this.grid,y=i.startX-1;y<i.finishX+2;y++)for(var C=i.startY-1;C<i.finishY+2;C++)if(!(y<0||C<0||y>=p.length||C>=p[0].length)){for(var F=0;F<p[y][C].length;F++)if(N=p[y][C][F],!(i.getOwner()!=N.getOwner()||i==N)&&!r.has(N)&&!l.has(N)){var M=Math.abs(i.getCenterX()-N.getCenterX())-(i.getWidth()/2+N.getWidth()/2),P=Math.abs(i.getCenterY()-N.getCenterY())-(i.getHeight()/2+N.getHeight()/2);M<=this.repulsionRange&&P<=this.repulsionRange&&l.add(N)}}i.surrounding=[].concat(u(l))}for(y=0;y<i.surrounding.length;y++)this.calcRepulsionForce(i,i.surrounding[y])},e.prototype.calcRepulsionRange=function(){return 0},m.exports=e},function(m,w,L){"use strict";var u=L(1),o=L(4);function n(a,f,e){u.call(this,a,f,e),this.idealLength=o.DEFAULT_EDGE_LENGTH,this.edgeElasticity=o.DEFAULT_SPRING_STRENGTH}n.prototype=Object.create(u.prototype);for(var t in u)n[t]=u[t];m.exports=n},function(m,w,L){"use strict";var u=L(3),o=L(4);function n(a,f,e,v){u.call(this,a,f,e,v),this.nodeRepulsion=o.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}n.prototype=Object.create(u.prototype);for(var t in u)n[t]=u[t];n.prototype.setGridCoordinates=function(a,f,e,v){this.startX=a,this.finishX=f,this.startY=e,this.finishY=v},m.exports=n},function(m,w,L){"use strict";function u(o,n){this.width=0,this.height=0,o!==null&&n!==null&&(this.height=n,this.width=o)}u.prototype.getWidth=function(){return this.width},u.prototype.setWidth=function(o){this.width=o},u.prototype.getHeight=function(){return this.height},u.prototype.setHeight=function(o){this.height=o},m.exports=u},function(m,w,L){"use strict";var u=L(14);function o(){this.map={},this.keys=[]}o.prototype.put=function(n,t){var a=u.createID(n);this.contains(a)||(this.map[a]=t,this.keys.push(n))},o.prototype.contains=function(n){var t=u.createID(n);return this.map[n]!=null},o.prototype.get=function(n){var t=u.createID(n);return this.map[t]},o.prototype.keySet=function(){return this.keys},m.exports=o},function(m,w,L){"use strict";var u=L(14);function o(){this.set={}}o.prototype.add=function(n){var t=u.createID(n);this.contains(t)||(this.set[t]=n)},o.prototype.remove=function(n){delete this.set[u.createID(n)]},o.prototype.clear=function(){this.set={}},o.prototype.contains=function(n){return this.set[u.createID(n)]==n},o.prototype.isEmpty=function(){return this.size()===0},o.prototype.size=function(){return Object.keys(this.set).length},o.prototype.addAllTo=function(n){for(var t=Object.keys(this.set),a=t.length,f=0;f<a;f++)n.push(this.set[t[f]])},o.prototype.size=function(){return Object.keys(this.set).length},o.prototype.addAll=function(n){for(var t=n.length,a=0;a<t;a++){var f=n[a];this.add(f)}},m.exports=o},function(m,w,L){"use strict";function u(){}u.multMat=function(o,n){for(var t=[],a=0;a<o.length;a++){t[a]=[];for(var f=0;f<n[0].length;f++){t[a][f]=0;for(var e=0;e<o[0].length;e++)t[a][f]+=o[a][e]*n[e][f]}}return t},u.transpose=function(o){for(var n=[],t=0;t<o[0].length;t++){n[t]=[];for(var a=0;a<o.length;a++)n[t][a]=o[a][t]}return n},u.multCons=function(o,n){for(var t=[],a=0;a<o.length;a++)t[a]=o[a]*n;return t},u.minusOp=function(o,n){for(var t=[],a=0;a<o.length;a++)t[a]=o[a]-n[a];return t},u.dotProduct=function(o,n){for(var t=0,a=0;a<o.length;a++)t+=o[a]*n[a];return t},u.mag=function(o){return Math.sqrt(this.dotProduct(o,o))},u.normalize=function(o){for(var n=[],t=this.mag(o),a=0;a<o.length;a++)n[a]=o[a]/t;return n},u.multGamma=function(o){for(var n=[],t=0,a=0;a<o.length;a++)t+=o[a];t*=-1/o.length;for(var f=0;f<o.length;f++)n[f]=t+o[f];return n},u.multL=function(o,n,t){for(var a=[],f=[],e=[],v=0;v<n[0].length;v++){for(var i=0,r=0;r<n.length;r++)i+=-.5*n[r][v]*o[r];f[v]=i}for(var h=0;h<t.length;h++){for(var c=0,l=0;l<t.length;l++)c+=t[h][l]*f[l];e[h]=c}for(var N=0;N<n.length;N++){for(var p=0,y=0;y<n[0].length;y++)p+=n[N][y]*e[y];a[N]=p}return a},m.exports=u},function(m,w,L){"use strict";var u=function(){function a(f,e){for(var v=0;v<e.length;v++){var i=e[v];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(f,i.key,i)}}return function(f,e,v){return e&&a(f.prototype,e),v&&a(f,v),f}}();function o(a,f){if(!(a instanceof f))throw new TypeError("Cannot call a class as a function")}var n=L(11),t=function(){function a(f,e){o(this,a),(e!==null||e!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var v=void 0;f instanceof n?v=f.size():v=f.length,this._quicksort(f,0,v-1)}return u(a,[{key:"_quicksort",value:function(e,v,i){if(v<i){var r=this._partition(e,v,i);this._quicksort(e,v,r),this._quicksort(e,r+1,i)}}},{key:"_partition",value:function(e,v,i){for(var r=this._get(e,v),h=v,c=i;;){for(;this.compareFunction(r,this._get(e,c));)c--;for(;this.compareFunction(this._get(e,h),r);)h++;if(h<c)this._swap(e,h,c),h++,c--;else return c}}},{key:"_get",value:function(e,v){return e instanceof n?e.get_object_at(v):e[v]}},{key:"_set",value:function(e,v,i){e instanceof n?e.set_object_at(v,i):e[v]=i}},{key:"_swap",value:function(e,v,i){var r=this._get(e,v);this._set(e,v,this._get(e,i)),this._set(e,i,r)}},{key:"_defaultCompareFunction",value:function(e,v){return v>e}}]),a}();m.exports=t},function(m,w,L){"use strict";function u(){}u.svd=function(o){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=o.length,this.n=o[0].length;var n=Math.min(this.m,this.n);this.s=function(xt){for(var Lt=[];xt-- >0;)Lt.push(0);return Lt}(Math.min(this.m+1,this.n)),this.U=function(xt){var Lt=function kt(St){if(St.length==0)return 0;for(var Wt=[],$t=0;$t<St[0];$t++)Wt.push(kt(St.slice(1)));return Wt};return Lt(xt)}([this.m,n]),this.V=function(xt){var Lt=function kt(St){if(St.length==0)return 0;for(var Wt=[],$t=0;$t<St[0];$t++)Wt.push(kt(St.slice(1)));return Wt};return Lt(xt)}([this.n,this.n]);for(var t=function(xt){for(var Lt=[];xt-- >0;)Lt.push(0);return Lt}(this.n),a=function(xt){for(var Lt=[];xt-- >0;)Lt.push(0);return Lt}(this.m),f=!0,e=!0,v=Math.min(this.m-1,this.n),i=Math.max(0,Math.min(this.n-2,this.m)),r=0;r<Math.max(v,i);r++){if(r<v){this.s[r]=0;for(var h=r;h<this.m;h++)this.s[r]=u.hypot(this.s[r],o[h][r]);if(this.s[r]!==0){o[r][r]<0&&(this.s[r]=-this.s[r]);for(var c=r;c<this.m;c++)o[c][r]/=this.s[r];o[r][r]+=1}this.s[r]=-this.s[r]}for(var l=r+1;l<this.n;l++){if(function(xt,Lt){return xt&&Lt}(r<v,this.s[r]!==0)){for(var N=0,p=r;p<this.m;p++)N+=o[p][r]*o[p][l];N=-N/o[r][r];for(var y=r;y<this.m;y++)o[y][l]+=N*o[y][r]}t[l]=o[r][l]}if(function(xt,Lt){return xt&&Lt}(f,r<v))for(var C=r;C<this.m;C++)this.U[C][r]=o[C][r];if(r<i){t[r]=0;for(var F=r+1;F<this.n;F++)t[r]=u.hypot(t[r],t[F]);if(t[r]!==0){t[r+1]<0&&(t[r]=-t[r]);for(var M=r+1;M<this.n;M++)t[M]/=t[r];t[r+1]+=1}if(t[r]=-t[r],function(xt,Lt){return xt&&Lt}(r+1<this.m,t[r]!==0)){for(var P=r+1;P<this.m;P++)a[P]=0;for(var V=r+1;V<this.n;V++)for(var Y=r+1;Y<this.m;Y++)a[Y]+=t[V]*o[Y][V];for(var et=r+1;et<this.n;et++)for(var I=-t[et]/t[r+1],k=r+1;k<this.m;k++)o[k][et]+=I*a[k]}if(e)for(var s=r+1;s<this.n;s++)this.V[s][r]=t[s]}}var E=Math.min(this.n,this.m+1);if(v<this.n&&(this.s[v]=o[v][v]),this.m<E&&(this.s[E-1]=0),i+1<E&&(t[i]=o[i][E-1]),t[E-1]=0,f){for(var g=v;g<n;g++){for(var T=0;T<this.m;T++)this.U[T][g]=0;this.U[g][g]=1}for(var d=v-1;d>=0;d--)if(this.s[d]!==0){for(var D=d+1;D<n;D++){for(var O=0,b=d;b<this.m;b++)O+=this.U[b][d]*this.U[b][D];O=-O/this.U[d][d];for(var R=d;R<this.m;R++)this.U[R][D]+=O*this.U[R][d]}for(var x=d;x<this.m;x++)this.U[x][d]=-this.U[x][d];this.U[d][d]=1+this.U[d][d];for(var Z=0;Z<d-1;Z++)this.U[Z][d]=0}else{for(var _=0;_<this.m;_++)this.U[_][d]=0;this.U[d][d]=1}}if(e)for(var S=this.n-1;S>=0;S--){if(function(xt,Lt){return xt&&Lt}(S<i,t[S]!==0))for(var j=S+1;j<n;j++){for(var z=0,A=S+1;A<this.n;A++)z+=this.V[A][S]*this.V[A][j];z=-z/this.V[S+1][S];for(var U=S+1;U<this.n;U++)this.V[U][j]+=z*this.V[U][S]}for(var X=0;X<this.n;X++)this.V[X][S]=0;this.V[S][S]=1}for(var Q=E-1,ht=0,Ct=Math.pow(2,-52),Ft=Math.pow(2,-966);E>0;){var J=void 0,Xt=void 0;for(J=E-2;J>=-1&&J!==-1;J--)if(Math.abs(t[J])<=Ft+Ct*(Math.abs(this.s[J])+Math.abs(this.s[J+1]))){t[J]=0;break}if(J===E-2)Xt=4;else{var Ot=void 0;for(Ot=E-1;Ot>=J&&Ot!==J;Ot--){var ot=(Ot!==E?Math.abs(t[Ot]):0)+(Ot!==J+1?Math.abs(t[Ot-1]):0);if(Math.abs(this.s[Ot])<=Ft+Ct*ot){this.s[Ot]=0;break}}Ot===J?Xt=3:Ot===E-1?Xt=1:(Xt=2,J=Ot)}switch(J++,Xt){case 1:{var tt=t[E-2];t[E-2]=0;for(var yt=E-2;yt>=J;yt--){var mt=u.hypot(this.s[yt],tt),Mt=this.s[yt]/mt,Tt=tt/mt;if(this.s[yt]=mt,yt!==J&&(tt=-Tt*t[yt-1],t[yt-1]=Mt*t[yt-1]),e)for(var Nt=0;Nt<this.n;Nt++)mt=Mt*this.V[Nt][yt]+Tt*this.V[Nt][E-1],this.V[Nt][E-1]=-Tt*this.V[Nt][yt]+Mt*this.V[Nt][E-1],this.V[Nt][yt]=mt}}break;case 2:{var Dt=t[J-1];t[J-1]=0;for(var Rt=J;Rt<E;Rt++){var zt=u.hypot(this.s[Rt],Dt),Gt=this.s[Rt]/zt,Ht=Dt/zt;if(this.s[Rt]=zt,Dt=-Ht*t[Rt],t[Rt]=Gt*t[Rt],f)for(var Pt=0;Pt<this.m;Pt++)zt=Gt*this.U[Pt][Rt]+Ht*this.U[Pt][J-1],this.U[Pt][J-1]=-Ht*this.U[Pt][Rt]+Gt*this.U[Pt][J-1],this.U[Pt][Rt]=zt}}break;case 3:{var G=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[E-1]),Math.abs(this.s[E-2])),Math.abs(t[E-2])),Math.abs(this.s[J])),Math.abs(t[J])),H=this.s[E-1]/G,W=this.s[E-2]/G,B=t[E-2]/G,$=this.s[J]/G,K=t[J]/G,gt=((W+H)*(W-H)+B*B)/2,ct=H*B*(H*B),q=0;(function(xt,Lt){return xt||Lt})(gt!==0,ct!==0)&&(q=Math.sqrt(gt*gt+ct),gt<0&&(q=-q),q=ct/(gt+q));for(var lt=($+H)*($-H)+q,ut=$*K,rt=J;rt<E-1;rt++){var vt=u.hypot(lt,ut),At=lt/vt,st=ut/vt;if(rt!==J&&(t[rt-1]=vt),lt=At*this.s[rt]+st*t[rt],t[rt]=At*t[rt]-st*this.s[rt],ut=st*this.s[rt+1],this.s[rt+1]=At*this.s[rt+1],e)for(var it=0;it<this.n;it++)vt=At*this.V[it][rt]+st*this.V[it][rt+1],this.V[it][rt+1]=-st*this.V[it][rt]+At*this.V[it][rt+1],this.V[it][rt]=vt;if(vt=u.hypot(lt,ut),At=lt/vt,st=ut/vt,this.s[rt]=vt,lt=At*t[rt]+st*this.s[rt+1],this.s[rt+1]=-st*t[rt]+At*this.s[rt+1],ut=st*t[rt+1],t[rt+1]=At*t[rt+1],f&&rt<this.m-1)for(var dt=0;dt<this.m;dt++)vt=At*this.U[dt][rt]+st*this.U[dt][rt+1],this.U[dt][rt+1]=-st*this.U[dt][rt]+At*this.U[dt][rt+1],this.U[dt][rt]=vt}t[E-2]=lt,ht=ht+1}break;case 4:{if(this.s[J]<=0&&(this.s[J]=this.s[J]<0?-this.s[J]:0,e))for(var nt=0;nt<=Q;nt++)this.V[nt][J]=-this.V[nt][J];for(;J<Q&&!(this.s[J]>=this.s[J+1]);){var ft=this.s[J];if(this.s[J]=this.s[J+1],this.s[J+1]=ft,e&&J<this.n-1)for(var bt=0;bt<this.n;bt++)ft=this.V[bt][J+1],this.V[bt][J+1]=this.V[bt][J],this.V[bt][J]=ft;if(f&&J<this.m-1)for(var It=0;It<this.m;It++)ft=this.U[It][J+1],this.U[It][J+1]=this.U[It][J],this.U[It][J]=ft;J++}ht=0,E--}break}}var Bt={U:this.U,V:this.V,S:this.s};return Bt},u.hypot=function(o,n){var t=void 0;return Math.abs(o)>Math.abs(n)?(t=n/o,t=Math.abs(o)*Math.sqrt(1+t*t)):n!=0?(t=o/n,t=Math.abs(n)*Math.sqrt(1+t*t)):t=0,t},m.exports=u},function(m,w,L){"use strict";var u=function(){function t(a,f){for(var e=0;e<f.length;e++){var v=f[e];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(a,v.key,v)}}return function(a,f,e){return f&&t(a.prototype,f),e&&t(a,e),a}}();function o(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}var n=function(){function t(a,f){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;o(this,t),this.sequence1=a,this.sequence2=f,this.match_score=e,this.mismatch_penalty=v,this.gap_penalty=i,this.iMax=a.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var r=0;r<this.iMax;r++){this.grid[r]=new Array(this.jMax);for(var h=0;h<this.jMax;h++)this.grid[r][h]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[c][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return u(t,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var e=1;e<this.iMax;e++)this.grid[e][0]=this.grid[e-1][0]+this.gap_penalty,this.tracebackGrid[e][0]=[!1,!0,!1];for(var v=1;v<this.iMax;v++)for(var i=1;i<this.jMax;i++){var r=void 0;this.sequence1[v-1]===this.sequence2[i-1]?r=this.grid[v-1][i-1]+this.match_score:r=this.grid[v-1][i-1]+this.mismatch_penalty;var h=this.grid[v-1][i]+this.gap_penalty,c=this.grid[v][i-1]+this.gap_penalty,l=[r,h,c],N=this.arrayAllMaxIndexes(l);this.grid[v][i]=l[N[0]],this.tracebackGrid[v][i]=[N.includes(0),N.includes(1),N.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var e=f[0],v=this.tracebackGrid[e.pos[0]][e.pos[1]];v[0]&&f.push({pos:[e.pos[0]-1,e.pos[1]-1],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),v[1]&&f.push({pos:[e.pos[0]-1,e.pos[1]],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:"-"+e.seq2}),v[2]&&f.push({pos:[e.pos[0],e.pos[1]-1],seq1:"-"+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),e.pos[0]===0&&e.pos[1]===0&&this.alignments.push({sequence1:e.seq1,sequence2:e.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,e){for(var v=[],i=-1;(i=f.indexOf(e,i+1))!==-1;)v.push(i);return v}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),t}();m.exports=n},function(m,w,L){"use strict";var u=function(){};u.FDLayout=L(18),u.FDLayoutConstants=L(4),u.FDLayoutEdge=L(19),u.FDLayoutNode=L(20),u.DimensionD=L(21),u.HashMap=L(22),u.HashSet=L(23),u.IGeometry=L(8),u.IMath=L(9),u.Integer=L(10),u.Point=L(12),u.PointD=L(5),u.RandomSeed=L(16),u.RectangleD=L(13),u.Transform=L(17),u.UniqueIDGeneretor=L(14),u.Quicksort=L(25),u.LinkedList=L(11),u.LGraphObject=L(2),u.LGraph=L(6),u.LEdge=L(1),u.LGraphManager=L(7),u.LNode=L(3),u.Layout=L(15),u.LayoutConstants=L(0),u.NeedlemanWunsch=L(27),u.Matrix=L(24),u.SVD=L(26),m.exports=u},function(m,w,L){"use strict";function u(){this.listeners=[]}var o=u.prototype;o.addListener=function(n,t){this.listeners.push({event:n,callback:t})},o.removeListener=function(n,t){for(var a=this.listeners.length;a>=0;a--){var f=this.listeners[a];f.event===n&&f.callback===t&&this.listeners.splice(a,1)}},o.emit=function(n,t){for(var a=0;a<this.listeners.length;a++){var f=this.listeners[a];n===f.event&&f.callback(t)}},m.exports=u}])})});var Ae=me((oe,Me)=>{(function(w,L){typeof oe=="object"&&typeof Me=="object"?Me.exports=L(Ce()):typeof define=="function"&&define.amd?define(["layout-base"],L):typeof oe=="object"?oe.coseBase=L(Ce()):w.coseBase=L(w.layoutBase)})(oe,function(m){return(()=>{"use strict";var w={45:(n,t,a)=>{var f={};f.layoutBase=a(551),f.CoSEConstants=a(806),f.CoSEEdge=a(767),f.CoSEGraph=a(880),f.CoSEGraphManager=a(578),f.CoSELayout=a(765),f.CoSENode=a(991),f.ConstraintHandler=a(902),n.exports=f},806:(n,t,a)=>{var f=a(551).FDLayoutConstants;function e(){}for(var v in f)e[v]=f[v];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=f.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,e.ENFORCE_CONSTRAINTS=!0,e.APPLY_LAYOUT=!0,e.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,e.TREE_REDUCTION_ON_INCREMENTAL=!0,e.PURE_INCREMENTAL=e.DEFAULT_INCREMENTAL,n.exports=e},767:(n,t,a)=>{var f=a(551).FDLayoutEdge;function e(i,r,h){f.call(this,i,r,h)}e.prototype=Object.create(f.prototype);for(var v in f)e[v]=f[v];n.exports=e},880:(n,t,a)=>{var f=a(551).LGraph;function e(i,r,h){f.call(this,i,r,h)}e.prototype=Object.create(f.prototype);for(var v in f)e[v]=f[v];n.exports=e},578:(n,t,a)=>{var f=a(551).LGraphManager;function e(i){f.call(this,i)}e.prototype=Object.create(f.prototype);for(var v in f)e[v]=f[v];n.exports=e},765:(n,t,a)=>{var f=a(551).FDLayout,e=a(578),v=a(880),i=a(991),r=a(767),h=a(806),c=a(902),l=a(551).FDLayoutConstants,N=a(551).LayoutConstants,p=a(551).Point,y=a(551).PointD,C=a(551).DimensionD,F=a(551).Layout,M=a(551).Integer,P=a(551).IGeometry,V=a(551).LGraph,Y=a(551).Transform,et=a(551).LinkedList;function I(){f.call(this),this.toBeTiled={},this.constraints={}}I.prototype=Object.create(f.prototype);for(var k in f)I[k]=f[k];I.prototype.newGraphManager=function(){var s=new e(this);return this.graphManager=s,s},I.prototype.newGraph=function(s){return new v(null,this.graphManager,s)},I.prototype.newNode=function(s){return new i(this.graphManager,s)},I.prototype.newEdge=function(s){return new r(null,null,s)},I.prototype.initParameters=function(){f.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=l.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=l.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},I.prototype.initSpringEmbedder=function(){f.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/l.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},I.prototype.layout=function(){var s=N.DEFAULT_CREATE_BENDS_AS_NEEDED;return s&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},I.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(h.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(D){return E.has(D)});this.graphManager.setAllNodesToApplyGravitation(g)}}else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(T){return E.has(T)});this.graphManager.setAllNodesToApplyGravitation(g),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),h.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},I.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%l.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),E=this.nodesWithGravity.filter(function(d){return s.has(d)});this.graphManager.setAllNodesToApplyGravitation(E),this.graphManager.updateBounds(),this.updateGrid(),h.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),h.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var g=!this.isTreeGrowing&&!this.isGrowthFinished,T=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(g,T),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},I.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),E={},g=0;g<s.length;g++){var T=s[g].rect,d=s[g].id;E[d]={id:d,x:T.getCenterX(),y:T.getCenterY(),w:T.width,h:T.height}}return E},I.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(l.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},I.prototype.moveNodes=function(){for(var s=this.getAllNodes(),E,g=0;g<s.length;g++)E=s[g],E.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var g=0;g<s.length;g++)E=s[g],E.move()},I.prototype.initConstraintVariables=function(){var s=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var E=this.graphManager.getAllNodes(),g=0;g<E.length;g++){var T=E[g];this.idToNodeMap.set(T.id,T)}var d=function A(U){for(var X=U.getChild().getNodes(),Q,ht=0,Ct=0;Ct<X.length;Ct++)Q=X[Ct],Q.getChild()==null?s.fixedNodeSet.has(Q.id)&&(ht+=100):ht+=A(Q);return ht};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(X){s.fixedNodeSet.add(X.nodeId)});for(var E=this.graphManager.getAllNodes(),T,g=0;g<E.length;g++)if(T=E[g],T.getChild()!=null){var D=d(T);D>0&&(T.fixedNodeWeight=D)}}if(this.constraints.relativePlacementConstraint){var O=new Map,b=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(A){s.fixedNodesOnHorizontal.add(A),s.fixedNodesOnVertical.add(A)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var R=this.constraints.alignmentConstraint.vertical,g=0;g<R.length;g++)this.dummyToNodeForVerticalAlignment.set("dummy"+g,[]),R[g].forEach(function(U){O.set(U,"dummy"+g),s.dummyToNodeForVerticalAlignment.get("dummy"+g).push(U),s.fixedNodeSet.has(U)&&s.fixedNodesOnHorizontal.add("dummy"+g)});if(this.constraints.alignmentConstraint.horizontal)for(var x=this.constraints.alignmentConstraint.horizontal,g=0;g<x.length;g++)this.dummyToNodeForHorizontalAlignment.set("dummy"+g,[]),x[g].forEach(function(U){b.set(U,"dummy"+g),s.dummyToNodeForHorizontalAlignment.get("dummy"+g).push(U),s.fixedNodeSet.has(U)&&s.fixedNodesOnVertical.add("dummy"+g)})}if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(A){var U,X,Q;for(Q=A.length-1;Q>=2*A.length/3;Q--)U=Math.floor(Math.random()*(Q+1)),X=A[Q],A[Q]=A[U],A[U]=X;return A},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(A){if(A.left){var U=O.has(A.left)?O.get(A.left):A.left,X=O.has(A.right)?O.get(A.right):A.right;s.nodesInRelativeHorizontal.includes(U)||(s.nodesInRelativeHorizontal.push(U),s.nodeToRelativeConstraintMapHorizontal.set(U,[]),s.dummyToNodeForVerticalAlignment.has(U)?s.nodeToTempPositionMapHorizontal.set(U,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(U)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(U,s.idToNodeMap.get(U).getCenterX())),s.nodesInRelativeHorizontal.includes(X)||(s.nodesInRelativeHorizontal.push(X),s.nodeToRelativeConstraintMapHorizontal.set(X,[]),s.dummyToNodeForVerticalAlignment.has(X)?s.nodeToTempPositionMapHorizontal.set(X,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(X)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(X,s.idToNodeMap.get(X).getCenterX())),s.nodeToRelativeConstraintMapHorizontal.get(U).push({right:X,gap:A.gap}),s.nodeToRelativeConstraintMapHorizontal.get(X).push({left:U,gap:A.gap})}else{var Q=b.has(A.top)?b.get(A.top):A.top,ht=b.has(A.bottom)?b.get(A.bottom):A.bottom;s.nodesInRelativeVertical.includes(Q)||(s.nodesInRelativeVertical.push(Q),s.nodeToRelativeConstraintMapVertical.set(Q,[]),s.dummyToNodeForHorizontalAlignment.has(Q)?s.nodeToTempPositionMapVertical.set(Q,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(Q)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(Q,s.idToNodeMap.get(Q).getCenterY())),s.nodesInRelativeVertical.includes(ht)||(s.nodesInRelativeVertical.push(ht),s.nodeToRelativeConstraintMapVertical.set(ht,[]),s.dummyToNodeForHorizontalAlignment.has(ht)?s.nodeToTempPositionMapVertical.set(ht,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(ht)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(ht,s.idToNodeMap.get(ht).getCenterY())),s.nodeToRelativeConstraintMapVertical.get(Q).push({bottom:ht,gap:A.gap}),s.nodeToRelativeConstraintMapVertical.get(ht).push({top:Q,gap:A.gap})}});else{var Z=new Map,_=new Map;this.constraints.relativePlacementConstraint.forEach(function(A){if(A.left){var U=O.has(A.left)?O.get(A.left):A.left,X=O.has(A.right)?O.get(A.right):A.right;Z.has(U)?Z.get(U).push(X):Z.set(U,[X]),Z.has(X)?Z.get(X).push(U):Z.set(X,[U])}else{var Q=b.has(A.top)?b.get(A.top):A.top,ht=b.has(A.bottom)?b.get(A.bottom):A.bottom;_.has(Q)?_.get(Q).push(ht):_.set(Q,[ht]),_.has(ht)?_.get(ht).push(Q):_.set(ht,[Q])}});var S=function(U,X){var Q=[],ht=[],Ct=new et,Ft=new Set,J=0;return U.forEach(function(Xt,Ot){if(!Ft.has(Ot)){Q[J]=[],ht[J]=!1;var ot=Ot;for(Ct.push(ot),Ft.add(ot),Q[J].push(ot);Ct.length!=0;){ot=Ct.shift(),X.has(ot)&&(ht[J]=!0);var tt=U.get(ot);tt.forEach(function(yt){Ft.has(yt)||(Ct.push(yt),Ft.add(yt),Q[J].push(yt))})}J++}}),{components:Q,isFixed:ht}},j=S(Z,s.fixedNodesOnHorizontal);this.componentsOnHorizontal=j.components,this.fixedComponentsOnHorizontal=j.isFixed;var z=S(_,s.fixedNodesOnVertical);this.componentsOnVertical=z.components,this.fixedComponentsOnVertical=z.isFixed}}},I.prototype.updateDisplacements=function(){var s=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(z){var A=s.idToNodeMap.get(z.nodeId);A.displacementX=0,A.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var E=this.constraints.alignmentConstraint.vertical,g=0;g<E.length;g++){for(var T=0,d=0;d<E[g].length;d++){if(this.fixedNodeSet.has(E[g][d])){T=0;break}T+=this.idToNodeMap.get(E[g][d]).displacementX}for(var D=T/E[g].length,d=0;d<E[g].length;d++)this.idToNodeMap.get(E[g][d]).displacementX=D}if(this.constraints.alignmentConstraint.horizontal)for(var O=this.constraints.alignmentConstraint.horizontal,g=0;g<O.length;g++){for(var b=0,d=0;d<O[g].length;d++){if(this.fixedNodeSet.has(O[g][d])){b=0;break}b+=this.idToNodeMap.get(O[g][d]).displacementY}for(var R=b/O[g].length,d=0;d<O[g].length;d++)this.idToNodeMap.get(O[g][d]).displacementY=R}}if(this.constraints.relativePlacementConstraint)if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(z){if(!s.fixedNodesOnHorizontal.has(z)){var A=0;s.dummyToNodeForVerticalAlignment.has(z)?A=s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(z)[0]).displacementX:A=s.idToNodeMap.get(z).displacementX,s.nodeToRelativeConstraintMapHorizontal.get(z).forEach(function(U){if(U.right){var X=s.nodeToTempPositionMapHorizontal.get(U.right)-s.nodeToTempPositionMapHorizontal.get(z)-A;X<U.gap&&(A-=U.gap-X)}else{var X=s.nodeToTempPositionMapHorizontal.get(z)-s.nodeToTempPositionMapHorizontal.get(U.left)+A;X<U.gap&&(A+=U.gap-X)}}),s.nodeToTempPositionMapHorizontal.set(z,s.nodeToTempPositionMapHorizontal.get(z)+A),s.dummyToNodeForVerticalAlignment.has(z)?s.dummyToNodeForVerticalAlignment.get(z).forEach(function(U){s.idToNodeMap.get(U).displacementX=A}):s.idToNodeMap.get(z).displacementX=A}}),this.nodesInRelativeVertical.forEach(function(z){if(!s.fixedNodesOnHorizontal.has(z)){var A=0;s.dummyToNodeForHorizontalAlignment.has(z)?A=s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(z)[0]).displacementY:A=s.idToNodeMap.get(z).displacementY,s.nodeToRelativeConstraintMapVertical.get(z).forEach(function(U){if(U.bottom){var X=s.nodeToTempPositionMapVertical.get(U.bottom)-s.nodeToTempPositionMapVertical.get(z)-A;X<U.gap&&(A-=U.gap-X)}else{var X=s.nodeToTempPositionMapVertical.get(z)-s.nodeToTempPositionMapVertical.get(U.top)+A;X<U.gap&&(A+=U.gap-X)}}),s.nodeToTempPositionMapVertical.set(z,s.nodeToTempPositionMapVertical.get(z)+A),s.dummyToNodeForHorizontalAlignment.has(z)?s.dummyToNodeForHorizontalAlignment.get(z).forEach(function(U){s.idToNodeMap.get(U).displacementY=A}):s.idToNodeMap.get(z).displacementY=A}});else{for(var g=0;g<this.componentsOnHorizontal.length;g++){var x=this.componentsOnHorizontal[g];if(this.fixedComponentsOnHorizontal[g])for(var d=0;d<x.length;d++)this.dummyToNodeForVerticalAlignment.has(x[d])?this.dummyToNodeForVerticalAlignment.get(x[d]).forEach(function(U){s.idToNodeMap.get(U).displacementX=0}):this.idToNodeMap.get(x[d]).displacementX=0;else{for(var Z=0,_=0,d=0;d<x.length;d++)if(this.dummyToNodeForVerticalAlignment.has(x[d])){var S=this.dummyToNodeForVerticalAlignment.get(x[d]);Z+=S.length*this.idToNodeMap.get(S[0]).displacementX,_+=S.length}else Z+=this.idToNodeMap.get(x[d]).displacementX,_++;for(var j=Z/_,d=0;d<x.length;d++)this.dummyToNodeForVerticalAlignment.has(x[d])?this.dummyToNodeForVerticalAlignment.get(x[d]).forEach(function(U){s.idToNodeMap.get(U).displacementX=j}):this.idToNodeMap.get(x[d]).displacementX=j}}for(var g=0;g<this.componentsOnVertical.length;g++){var x=this.componentsOnVertical[g];if(this.fixedComponentsOnVertical[g])for(var d=0;d<x.length;d++)this.dummyToNodeForHorizontalAlignment.has(x[d])?this.dummyToNodeForHorizontalAlignment.get(x[d]).forEach(function(X){s.idToNodeMap.get(X).displacementY=0}):this.idToNodeMap.get(x[d]).displacementY=0;else{for(var Z=0,_=0,d=0;d<x.length;d++)if(this.dummyToNodeForHorizontalAlignment.has(x[d])){var S=this.dummyToNodeForHorizontalAlignment.get(x[d]);Z+=S.length*this.idToNodeMap.get(S[0]).displacementY,_+=S.length}else Z+=this.idToNodeMap.get(x[d]).displacementY,_++;for(var j=Z/_,d=0;d<x.length;d++)this.dummyToNodeForHorizontalAlignment.has(x[d])?this.dummyToNodeForHorizontalAlignment.get(x[d]).forEach(function(Ct){s.idToNodeMap.get(Ct).displacementY=j}):this.idToNodeMap.get(x[d]).displacementY=j}}}},I.prototype.calculateNodesToApplyGravitationTo=function(){var s=[],E,g=this.graphManager.getGraphs(),T=g.length,d;for(d=0;d<T;d++)E=g[d],E.updateConnected(),E.isConnected||(s=s.concat(E.getNodes()));return s},I.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var E=new Set,g;for(g=0;g<s.length;g++){var T=s[g];if(!E.has(T)){var d=T.getSource(),D=T.getTarget();if(d==D)T.getBendpoints().push(new y),T.getBendpoints().push(new y),this.createDummyNodesForBendpoints(T),E.add(T);else{var O=[];if(O=O.concat(d.getEdgeListToNode(D)),O=O.concat(D.getEdgeListToNode(d)),!E.has(O[0])){if(O.length>1){var b;for(b=0;b<O.length;b++){var R=O[b];R.getBendpoints().push(new y),this.createDummyNodesForBendpoints(R)}}O.forEach(function(x){E.add(x)})}}}if(E.size==s.length)break}},I.prototype.positionNodesRadially=function(s){for(var E=new p(0,0),g=Math.ceil(Math.sqrt(s.length)),T=0,d=0,D=0,O=new y(0,0),b=0;b<s.length;b++){b%g==0&&(D=0,d=T,b!=0&&(d+=h.DEFAULT_COMPONENT_SEPERATION),T=0);var R=s[b],x=F.findCenterOfTree(R);E.x=D,E.y=d,O=I.radialLayout(R,x,E),O.y>T&&(T=Math.floor(O.y)),D=Math.floor(O.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new y(N.WORLD_CENTER_X-O.x/2,N.WORLD_CENTER_Y-O.y/2))},I.radialLayout=function(s,E,g){var T=Math.max(this.maxDiagonalInTree(s),h.DEFAULT_RADIAL_SEPARATION);I.branchRadialLayout(E,null,0,359,0,T);var d=V.calculateBounds(s),D=new Y;D.setDeviceOrgX(d.getMinX()),D.setDeviceOrgY(d.getMinY()),D.setWorldOrgX(g.x),D.setWorldOrgY(g.y);for(var O=0;O<s.length;O++){var b=s[O];b.transform(D)}var R=new y(d.getMaxX(),d.getMaxY());return D.inverseTransformPoint(R)},I.branchRadialLayout=function(s,E,g,T,d,D){var O=(T-g+1)/2;O<0&&(O+=180);var b=(O+g)%360,R=b*P.TWO_PI/360,x=Math.cos(R),Z=d*Math.cos(R),_=d*Math.sin(R);s.setCenter(Z,_);var S=[];S=S.concat(s.getEdges());var j=S.length;E!=null&&j--;for(var z=0,A=S.length,U,X=s.getEdgesBetween(E);X.length>1;){var Q=X[0];X.splice(0,1);var ht=S.indexOf(Q);ht>=0&&S.splice(ht,1),A--,j--}E!=null?U=(S.indexOf(X[0])+1)%A:U=0;for(var Ct=Math.abs(T-g)/j,Ft=U;z!=j;Ft=++Ft%A){var J=S[Ft].getOtherEnd(s);if(J!=E){var Xt=(g+z*Ct)%360,Ot=(Xt+Ct)%360;I.branchRadialLayout(J,s,Xt,Ot,d+D,D),z++}}},I.maxDiagonalInTree=function(s){for(var E=M.MIN_VALUE,g=0;g<s.length;g++){var T=s[g],d=T.getDiagonal();d>E&&(E=d)}return E},I.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},I.prototype.groupZeroDegreeMembers=function(){var s=this,E={};this.memberGroups={},this.idToDummyNode={};for(var g=[],T=this.graphManager.getAllNodes(),d=0;d<T.length;d++){var D=T[d],O=D.getParent();this.getNodeDegreeWithChildren(D)===0&&(O.id==null||!this.getToBeTiled(O))&&g.push(D)}for(var d=0;d<g.length;d++){var D=g[d],b=D.getParent().id;typeof E[b]>"u"&&(E[b]=[]),E[b]=E[b].concat(D)}Object.keys(E).forEach(function(R){if(E[R].length>1){var x="DummyCompound_"+R;s.memberGroups[x]=E[R];var Z=E[R][0].getParent(),_=new i(s.graphManager);_.id=x,_.paddingLeft=Z.paddingLeft||0,_.paddingRight=Z.paddingRight||0,_.paddingBottom=Z.paddingBottom||0,_.paddingTop=Z.paddingTop||0,s.idToDummyNode[x]=_;var S=s.getGraphManager().add(s.newGraph(),_),j=Z.getChild();j.add(_);for(var z=0;z<E[R].length;z++){var A=E[R][z];j.remove(A),S.add(A)}}})},I.prototype.clearCompounds=function(){var s={},E={};this.performDFSOnCompounds();for(var g=0;g<this.compoundOrder.length;g++)E[this.compoundOrder[g].id]=this.compoundOrder[g],s[this.compoundOrder[g].id]=[].concat(this.compoundOrder[g].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[g].getChild()),this.compoundOrder[g].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,E)},I.prototype.clearZeroDegreeMembers=function(){var s=this,E=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(g){var T=s.idToDummyNode[g];if(E[g]=s.tileNodes(s.memberGroups[g],T.paddingLeft+T.paddingRight),T.rect.width=E[g].width,T.rect.height=E[g].height,T.setCenter(E[g].centerX,E[g].centerY),T.labelMarginLeft=0,T.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var d=T.rect.width,D=T.rect.height;T.labelWidth&&(T.labelPosHorizontal=="left"?(T.rect.x-=T.labelWidth,T.setWidth(d+T.labelWidth),T.labelMarginLeft=T.labelWidth):T.labelPosHorizontal=="center"&&T.labelWidth>d?(T.rect.x-=(T.labelWidth-d)/2,T.setWidth(T.labelWidth),T.labelMarginLeft=(T.labelWidth-d)/2):T.labelPosHorizontal=="right"&&T.setWidth(d+T.labelWidth)),T.labelHeight&&(T.labelPosVertical=="top"?(T.rect.y-=T.labelHeight,T.setHeight(D+T.labelHeight),T.labelMarginTop=T.labelHeight):T.labelPosVertical=="center"&&T.labelHeight>D?(T.rect.y-=(T.labelHeight-D)/2,T.setHeight(T.labelHeight),T.labelMarginTop=(T.labelHeight-D)/2):T.labelPosVertical=="bottom"&&T.setHeight(D+T.labelHeight))}})},I.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var E=this.compoundOrder[s],g=E.id,T=E.paddingLeft,d=E.paddingTop,D=E.labelMarginLeft,O=E.labelMarginTop;this.adjustLocations(this.tiledMemberPack[g],E.rect.x,E.rect.y,T,d,D,O)}},I.prototype.repopulateZeroDegreeMembers=function(){var s=this,E=this.tiledZeroDegreePack;Object.keys(E).forEach(function(g){var T=s.idToDummyNode[g],d=T.paddingLeft,D=T.paddingTop,O=T.labelMarginLeft,b=T.labelMarginTop;s.adjustLocations(E[g],T.rect.x,T.rect.y,d,D,O,b)})},I.prototype.getToBeTiled=function(s){var E=s.id;if(this.toBeTiled[E]!=null)return this.toBeTiled[E];var g=s.getChild();if(g==null)return this.toBeTiled[E]=!1,!1;for(var T=g.getNodes(),d=0;d<T.length;d++){var D=T[d];if(this.getNodeDegree(D)>0)return this.toBeTiled[E]=!1,!1;if(D.getChild()==null){this.toBeTiled[D.id]=!1;continue}if(!this.getToBeTiled(D))return this.toBeTiled[E]=!1,!1}return this.toBeTiled[E]=!0,!0},I.prototype.getNodeDegree=function(s){for(var E=s.id,g=s.getEdges(),T=0,d=0;d<g.length;d++){var D=g[d];D.getSource().id!==D.getTarget().id&&(T=T+1)}return T},I.prototype.getNodeDegreeWithChildren=function(s){var E=this.getNodeDegree(s);if(s.getChild()==null)return E;for(var g=s.getChild().getNodes(),T=0;T<g.length;T++){var d=g[T];E+=this.getNodeDegreeWithChildren(d)}return E},I.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},I.prototype.fillCompexOrderByDFS=function(s){for(var E=0;E<s.length;E++){var g=s[E];g.getChild()!=null&&this.fillCompexOrderByDFS(g.getChild().getNodes()),this.getToBeTiled(g)&&this.compoundOrder.push(g)}},I.prototype.adjustLocations=function(s,E,g,T,d,D,O){E+=T+D,g+=d+O;for(var b=E,R=0;R<s.rows.length;R++){var x=s.rows[R];E=b;for(var Z=0,_=0;_<x.length;_++){var S=x[_];S.rect.x=E,S.rect.y=g,E+=S.rect.width+s.horizontalPadding,S.rect.height>Z&&(Z=S.rect.height)}g+=Z+s.verticalPadding}},I.prototype.tileCompoundMembers=function(s,E){var g=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(T){var d=E[T];if(g.tiledMemberPack[T]=g.tileNodes(s[T],d.paddingLeft+d.paddingRight),d.rect.width=g.tiledMemberPack[T].width,d.rect.height=g.tiledMemberPack[T].height,d.setCenter(g.tiledMemberPack[T].centerX,g.tiledMemberPack[T].centerY),d.labelMarginLeft=0,d.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var D=d.rect.width,O=d.rect.height;d.labelWidth&&(d.labelPosHorizontal=="left"?(d.rect.x-=d.labelWidth,d.setWidth(D+d.labelWidth),d.labelMarginLeft=d.labelWidth):d.labelPosHorizontal=="center"&&d.labelWidth>D?(d.rect.x-=(d.labelWidth-D)/2,d.setWidth(d.labelWidth),d.labelMarginLeft=(d.labelWidth-D)/2):d.labelPosHorizontal=="right"&&d.setWidth(D+d.labelWidth)),d.labelHeight&&(d.labelPosVertical=="top"?(d.rect.y-=d.labelHeight,d.setHeight(O+d.labelHeight),d.labelMarginTop=d.labelHeight):d.labelPosVertical=="center"&&d.labelHeight>O?(d.rect.y-=(d.labelHeight-O)/2,d.setHeight(d.labelHeight),d.labelMarginTop=(d.labelHeight-O)/2):d.labelPosVertical=="bottom"&&d.setHeight(O+d.labelHeight))}})},I.prototype.tileNodes=function(s,E){var g=this.tileNodesByFavoringDim(s,E,!0),T=this.tileNodesByFavoringDim(s,E,!1),d=this.getOrgRatio(g),D=this.getOrgRatio(T),O;return D<d?O=T:O=g,O},I.prototype.getOrgRatio=function(s){var E=s.width,g=s.height,T=E/g;return T<1&&(T=1/T),T},I.prototype.calcIdealRowWidth=function(s,E){var g=h.TILING_PADDING_VERTICAL,T=h.TILING_PADDING_HORIZONTAL,d=s.length,D=0,O=0,b=0;s.forEach(function(z){D+=z.getWidth(),O+=z.getHeight(),z.getWidth()>b&&(b=z.getWidth())});var R=D/d,x=O/d,Z=Math.pow(g-T,2)+4*(R+T)*(x+g)*d,_=(T-g+Math.sqrt(Z))/(2*(R+T)),S;E?(S=Math.ceil(_),S==_&&S++):S=Math.floor(_);var j=S*(R+T)-T;return b>j&&(j=b),j+=T*2,j},I.prototype.tileNodesByFavoringDim=function(s,E,g){var T=h.TILING_PADDING_VERTICAL,d=h.TILING_PADDING_HORIZONTAL,D=h.TILING_COMPARE_BY,O={rows:[],rowWidth:[],rowHeight:[],width:0,height:E,verticalPadding:T,horizontalPadding:d,centerX:0,centerY:0};D&&(O.idealRowWidth=this.calcIdealRowWidth(s,g));var b=function(A){return A.rect.width*A.rect.height},R=function(A,U){return b(U)-b(A)};s.sort(function(z,A){var U=R;return O.idealRowWidth?(U=D,U(z.id,A.id)):U(z,A)});for(var x=0,Z=0,_=0;_<s.length;_++){var S=s[_];x+=S.getCenterX(),Z+=S.getCenterY()}O.centerX=x/s.length,O.centerY=Z/s.length;for(var _=0;_<s.length;_++){var S=s[_];if(O.rows.length==0)this.insertNodeToRow(O,S,0,E);else if(this.canAddHorizontal(O,S.rect.width,S.rect.height)){var j=O.rows.length-1;O.idealRowWidth||(j=this.getShortestRowIndex(O)),this.insertNodeToRow(O,S,j,E)}else this.insertNodeToRow(O,S,O.rows.length,E);this.shiftToLastRow(O)}return O},I.prototype.insertNodeToRow=function(s,E,g,T){var d=T;if(g==s.rows.length){var D=[];s.rows.push(D),s.rowWidth.push(d),s.rowHeight.push(0)}var O=s.rowWidth[g]+E.rect.width;s.rows[g].length>0&&(O+=s.horizontalPadding),s.rowWidth[g]=O,s.width<O&&(s.width=O);var b=E.rect.height;g>0&&(b+=s.verticalPadding);var R=0;b>s.rowHeight[g]&&(R=s.rowHeight[g],s.rowHeight[g]=b,R=s.rowHeight[g]-R),s.height+=R,s.rows[g].push(E)},I.prototype.getShortestRowIndex=function(s){for(var E=-1,g=Number.MAX_VALUE,T=0;T<s.rows.length;T++)s.rowWidth[T]<g&&(E=T,g=s.rowWidth[T]);return E},I.prototype.getLongestRowIndex=function(s){for(var E=-1,g=Number.MIN_VALUE,T=0;T<s.rows.length;T++)s.rowWidth[T]>g&&(E=T,g=s.rowWidth[T]);return E},I.prototype.canAddHorizontal=function(s,E,g){if(s.idealRowWidth){var T=s.rows.length-1,d=s.rowWidth[T];return d+E+s.horizontalPadding<=s.idealRowWidth}var D=this.getShortestRowIndex(s);if(D<0)return!0;var O=s.rowWidth[D];if(O+s.horizontalPadding+E<=s.width)return!0;var b=0;s.rowHeight[D]<g&&D>0&&(b=g+s.verticalPadding-s.rowHeight[D]);var R;s.width-O>=E+s.horizontalPadding?R=(s.height+b)/(O+E+s.horizontalPadding):R=(s.height+b)/s.width,b=g+s.verticalPadding;var x;return s.width<E?x=(s.height+b)/E:x=(s.height+b)/s.width,x<1&&(x=1/x),R<1&&(R=1/R),R<x},I.prototype.shiftToLastRow=function(s){var E=this.getLongestRowIndex(s),g=s.rowWidth.length-1,T=s.rows[E],d=T[T.length-1],D=d.width+s.horizontalPadding;if(s.width-s.rowWidth[g]>D&&E!=g){T.splice(-1,1),s.rows[g].push(d),s.rowWidth[E]=s.rowWidth[E]-D,s.rowWidth[g]=s.rowWidth[g]+D,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var O=Number.MIN_VALUE,b=0;b<T.length;b++)T[b].height>O&&(O=T[b].height);E>0&&(O+=s.verticalPadding);var R=s.rowHeight[E]+s.rowHeight[g];s.rowHeight[E]=O,s.rowHeight[g]<d.height+s.verticalPadding&&(s.rowHeight[g]=d.height+s.verticalPadding);var x=s.rowHeight[E]+s.rowHeight[g];s.height+=x-R,this.shiftToLastRow(s)}},I.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},I.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},I.prototype.reduceTrees=function(){for(var s=[],E=!0,g;E;){var T=this.graphManager.getAllNodes(),d=[];E=!1;for(var D=0;D<T.length;D++)if(g=T[D],g.getEdges().length==1&&!g.getEdges()[0].isInterGraph&&g.getChild()==null){if(h.PURE_INCREMENTAL){var O=g.getEdges()[0].getOtherEnd(g),b=new C(g.getCenterX()-O.getCenterX(),g.getCenterY()-O.getCenterY());d.push([g,g.getEdges()[0],g.getOwner(),b])}else d.push([g,g.getEdges()[0],g.getOwner()]);E=!0}if(E==!0){for(var R=[],x=0;x<d.length;x++)d[x][0].getEdges().length==1&&(R.push(d[x]),d[x][0].getOwner().remove(d[x][0]));s.push(R),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=s},I.prototype.growTree=function(s){for(var E=s.length,g=s[E-1],T,d=0;d<g.length;d++)T=g[d],this.findPlaceforPrunedNode(T),T[2].add(T[0]),T[2].add(T[1],T[1].source,T[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},I.prototype.findPlaceforPrunedNode=function(s){var E,g,T=s[0];if(T==s[1].source?g=s[1].target:g=s[1].source,h.PURE_INCREMENTAL)T.setCenter(g.getCenterX()+s[3].getWidth(),g.getCenterY()+s[3].getHeight());else{var d=g.startX,D=g.finishX,O=g.startY,b=g.finishY,R=0,x=0,Z=0,_=0,S=[R,Z,x,_];if(O>0)for(var j=d;j<=D;j++)S[0]+=this.grid[j][O-1].length+this.grid[j][O].length-1;if(D<this.grid.length-1)for(var j=O;j<=b;j++)S[1]+=this.grid[D+1][j].length+this.grid[D][j].length-1;if(b<this.grid[0].length-1)for(var j=d;j<=D;j++)S[2]+=this.grid[j][b+1].length+this.grid[j][b].length-1;if(d>0)for(var j=O;j<=b;j++)S[3]+=this.grid[d-1][j].length+this.grid[d][j].length-1;for(var z=M.MAX_VALUE,A,U,X=0;X<S.length;X++)S[X]<z?(z=S[X],A=1,U=X):S[X]==z&&A++;if(A==3&&z==0)S[0]==0&&S[1]==0&&S[2]==0?E=1:S[0]==0&&S[1]==0&&S[3]==0?E=0:S[0]==0&&S[2]==0&&S[3]==0?E=3:S[1]==0&&S[2]==0&&S[3]==0&&(E=2);else if(A==2&&z==0){var Q=Math.floor(Math.random()*2);S[0]==0&&S[1]==0?Q==0?E=0:E=1:S[0]==0&&S[2]==0?Q==0?E=0:E=2:S[0]==0&&S[3]==0?Q==0?E=0:E=3:S[1]==0&&S[2]==0?Q==0?E=1:E=2:S[1]==0&&S[3]==0?Q==0?E=1:E=3:Q==0?E=2:E=3}else if(A==4&&z==0){var Q=Math.floor(Math.random()*4);E=Q}else E=U;E==0?T.setCenter(g.getCenterX(),g.getCenterY()-g.getHeight()/2-l.DEFAULT_EDGE_LENGTH-T.getHeight()/2):E==1?T.setCenter(g.getCenterX()+g.getWidth()/2+l.DEFAULT_EDGE_LENGTH+T.getWidth()/2,g.getCenterY()):E==2?T.setCenter(g.getCenterX(),g.getCenterY()+g.getHeight()/2+l.DEFAULT_EDGE_LENGTH+T.getHeight()/2):T.setCenter(g.getCenterX()-g.getWidth()/2-l.DEFAULT_EDGE_LENGTH-T.getWidth()/2,g.getCenterY())}},n.exports=I},991:(n,t,a)=>{var f=a(551).FDLayoutNode,e=a(551).IMath;function v(r,h,c,l){f.call(this,r,h,c,l)}v.prototype=Object.create(f.prototype);for(var i in f)v[i]=f[i];v.prototype.calculateDisplacement=function(){var r=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=r.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=r.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=r.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=r.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>r.coolingFactor*r.maxNodeDisplacement&&(this.displacementX=r.coolingFactor*r.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>r.coolingFactor*r.maxNodeDisplacement&&(this.displacementY=r.coolingFactor*r.maxNodeDisplacement*e.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},v.prototype.propogateDisplacementToChildren=function(r,h){for(var c=this.getChild().getNodes(),l,N=0;N<c.length;N++)l=c[N],l.getChild()==null?(l.displacementX+=r,l.displacementY+=h):l.propogateDisplacementToChildren(r,h)},v.prototype.move=function(){var r=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),r.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},v.prototype.setPred1=function(r){this.pred1=r},v.prototype.getPred1=function(){return pred1},v.prototype.getPred2=function(){return pred2},v.prototype.setNext=function(r){this.next=r},v.prototype.getNext=function(){return next},v.prototype.setProcessed=function(r){this.processed=r},v.prototype.isProcessed=function(){return processed},n.exports=v},902:(n,t,a)=>{function f(c){if(Array.isArray(c)){for(var l=0,N=Array(c.length);l<c.length;l++)N[l]=c[l];return N}else return Array.from(c)}var e=a(806),v=a(551).LinkedList,i=a(551).Matrix,r=a(551).SVD;function h(){}h.handleConstraints=function(c){var l={};l.fixedNodeConstraint=c.constraints.fixedNodeConstraint,l.alignmentConstraint=c.constraints.alignmentConstraint,l.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var N=new Map,p=new Map,y=[],C=[],F=c.getAllNodes(),M=0,P=0;P<F.length;P++){var V=F[P];V.getChild()==null&&(p.set(V.id,M++),y.push(V.getCenterX()),C.push(V.getCenterY()),N.set(V.id,V))}l.relativePlacementConstraint&&l.relativePlacementConstraint.forEach(function(G){!G.gap&&G.gap!=0&&(G.left?G.gap=e.DEFAULT_EDGE_LENGTH+N.get(G.left).getWidth()/2+N.get(G.right).getWidth()/2:G.gap=e.DEFAULT_EDGE_LENGTH+N.get(G.top).getHeight()/2+N.get(G.bottom).getHeight()/2)});var Y=function(H,W){return{x:H.x-W.x,y:H.y-W.y}},et=function(H){var W=0,B=0;return H.forEach(function($){W+=y[p.get($)],B+=C[p.get($)]}),{x:W/H.size,y:B/H.size}},I=function(H,W,B,$,K){function gt(st,it){var dt=new Set(st),nt=!0,ft=!1,bt=void 0;try{for(var It=it[Symbol.iterator](),Bt;!(nt=(Bt=It.next()).done);nt=!0){var xt=Bt.value;dt.add(xt)}}catch(Lt){ft=!0,bt=Lt}finally{try{!nt&&It.return&&It.return()}finally{if(ft)throw bt}}return dt}var ct=new Map;H.forEach(function(st,it){ct.set(it,0)}),H.forEach(function(st,it){st.forEach(function(dt){ct.set(dt.id,ct.get(dt.id)+1)})});var q=new Map,lt=new Map,ut=new v;ct.forEach(function(st,it){st==0?(ut.push(it),B||(W=="horizontal"?q.set(it,p.has(it)?y[p.get(it)]:$.get(it)):q.set(it,p.has(it)?C[p.get(it)]:$.get(it)))):q.set(it,Number.NEGATIVE_INFINITY),B&&lt.set(it,new Set([it]))}),B&&K.forEach(function(st){var it=[];if(st.forEach(function(ft){B.has(ft)&&it.push(ft)}),it.length>0){var dt=0;it.forEach(function(ft){W=="horizontal"?(q.set(ft,p.has(ft)?y[p.get(ft)]:$.get(ft)),dt+=q.get(ft)):(q.set(ft,p.has(ft)?C[p.get(ft)]:$.get(ft)),dt+=q.get(ft))}),dt=dt/it.length,st.forEach(function(ft){B.has(ft)||q.set(ft,dt)})}else{var nt=0;st.forEach(function(ft){W=="horizontal"?nt+=p.has(ft)?y[p.get(ft)]:$.get(ft):nt+=p.has(ft)?C[p.get(ft)]:$.get(ft)}),nt=nt/st.length,st.forEach(function(ft){q.set(ft,nt)})}});for(var rt=function(){var it=ut.shift(),dt=H.get(it);dt.forEach(function(nt){if(q.get(nt.id)<q.get(it)+nt.gap)if(B&&B.has(nt.id)){var ft=void 0;if(W=="horizontal"?ft=p.has(nt.id)?y[p.get(nt.id)]:$.get(nt.id):ft=p.has(nt.id)?C[p.get(nt.id)]:$.get(nt.id),q.set(nt.id,ft),ft<q.get(it)+nt.gap){var bt=q.get(it)+nt.gap-ft;lt.get(it).forEach(function(It){q.set(It,q.get(It)-bt)})}}else q.set(nt.id,q.get(it)+nt.gap);ct.set(nt.id,ct.get(nt.id)-1),ct.get(nt.id)==0&&ut.push(nt.id),B&&lt.set(nt.id,gt(lt.get(it),lt.get(nt.id)))})};ut.length!=0;)rt();if(B){var vt=new Set;H.forEach(function(st,it){st.length==0&&vt.add(it)});var At=[];lt.forEach(function(st,it){if(vt.has(it)){var dt=!1,nt=!0,ft=!1,bt=void 0;try{for(var It=st[Symbol.iterator](),Bt;!(nt=(Bt=It.next()).done);nt=!0){var xt=Bt.value;B.has(xt)&&(dt=!0)}}catch(St){ft=!0,bt=St}finally{try{!nt&&It.return&&It.return()}finally{if(ft)throw bt}}if(!dt){var Lt=!1,kt=void 0;At.forEach(function(St,Wt){St.has([].concat(f(st))[0])&&(Lt=!0,kt=Wt)}),Lt?st.forEach(function(St){At[kt].add(St)}):At.push(new Set(st))}}}),At.forEach(function(st,it){var dt=Number.POSITIVE_INFINITY,nt=Number.POSITIVE_INFINITY,ft=Number.NEGATIVE_INFINITY,bt=Number.NEGATIVE_INFINITY,It=!0,Bt=!1,xt=void 0;try{for(var Lt=st[Symbol.iterator](),kt;!(It=(kt=Lt.next()).done);It=!0){var St=kt.value,Wt=void 0;W=="horizontal"?Wt=p.has(St)?y[p.get(St)]:$.get(St):Wt=p.has(St)?C[p.get(St)]:$.get(St);var $t=q.get(St);Wt<dt&&(dt=Wt),Wt>ft&&(ft=Wt),$t<nt&&(nt=$t),$t>bt&&(bt=$t)}}catch(ee){Bt=!0,xt=ee}finally{try{!It&&Lt.return&&Lt.return()}finally{if(Bt)throw xt}}var de=(dt+ft)/2-(nt+bt)/2,Qt=!0,jt=!1,_t=void 0;try{for(var Kt=st[Symbol.iterator](),le;!(Qt=(le=Kt.next()).done);Qt=!0){var te=le.value;q.set(te,q.get(te)+de)}}catch(ee){jt=!0,_t=ee}finally{try{!Qt&&Kt.return&&Kt.return()}finally{if(jt)throw _t}}})}return q},k=function(H){var W=0,B=0,$=0,K=0;if(H.forEach(function(lt){lt.left?y[p.get(lt.left)]-y[p.get(lt.right)]>=0?W++:B++:C[p.get(lt.top)]-C[p.get(lt.bottom)]>=0?$++:K++}),W>B&&$>K)for(var gt=0;gt<p.size;gt++)y[gt]=-1*y[gt],C[gt]=-1*C[gt];else if(W>B)for(var ct=0;ct<p.size;ct++)y[ct]=-1*y[ct];else if($>K)for(var q=0;q<p.size;q++)C[q]=-1*C[q]},s=function(H){var W=[],B=new v,$=new Set,K=0;return H.forEach(function(gt,ct){if(!$.has(ct)){W[K]=[];var q=ct;for(B.push(q),$.add(q),W[K].push(q);B.length!=0;){q=B.shift();var lt=H.get(q);lt.forEach(function(ut){$.has(ut.id)||(B.push(ut.id),$.add(ut.id),W[K].push(ut.id))})}K++}}),W},E=function(H){var W=new Map;return H.forEach(function(B,$){W.set($,[])}),H.forEach(function(B,$){B.forEach(function(K){W.get($).push(K),W.get(K.id).push({id:$,gap:K.gap,direction:K.direction})})}),W},g=function(H){var W=new Map;return H.forEach(function(B,$){W.set($,[])}),H.forEach(function(B,$){B.forEach(function(K){W.get(K.id).push({id:$,gap:K.gap,direction:K.direction})})}),W},T=[],d=[],D=!1,O=!1,b=new Set,R=new Map,x=new Map,Z=[];if(l.fixedNodeConstraint&&l.fixedNodeConstraint.forEach(function(G){b.add(G.nodeId)}),l.relativePlacementConstraint&&(l.relativePlacementConstraint.forEach(function(G){G.left?(R.has(G.left)?R.get(G.left).push({id:G.right,gap:G.gap,direction:"horizontal"}):R.set(G.left,[{id:G.right,gap:G.gap,direction:"horizontal"}]),R.has(G.right)||R.set(G.right,[])):(R.has(G.top)?R.get(G.top).push({id:G.bottom,gap:G.gap,direction:"vertical"}):R.set(G.top,[{id:G.bottom,gap:G.gap,direction:"vertical"}]),R.has(G.bottom)||R.set(G.bottom,[]))}),x=E(R),Z=s(x)),e.TRANSFORM_ON_CONSTRAINT_HANDLING){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>1)l.fixedNodeConstraint.forEach(function(G,H){T[H]=[G.position.x,G.position.y],d[H]=[y[p.get(G.nodeId)],C[p.get(G.nodeId)]]}),D=!0;else if(l.alignmentConstraint)(function(){var G=0;if(l.alignmentConstraint.vertical){for(var H=l.alignmentConstraint.vertical,W=function(q){var lt=new Set;H[q].forEach(function(vt){lt.add(vt)});var ut=new Set([].concat(f(lt)).filter(function(vt){return b.has(vt)})),rt=void 0;ut.size>0?rt=y[p.get(ut.values().next().value)]:rt=et(lt).x,H[q].forEach(function(vt){T[G]=[rt,C[p.get(vt)]],d[G]=[y[p.get(vt)],C[p.get(vt)]],G++})},B=0;B<H.length;B++)W(B);D=!0}if(l.alignmentConstraint.horizontal){for(var $=l.alignmentConstraint.horizontal,K=function(q){var lt=new Set;$[q].forEach(function(vt){lt.add(vt)});var ut=new Set([].concat(f(lt)).filter(function(vt){return b.has(vt)})),rt=void 0;ut.size>0?rt=y[p.get(ut.values().next().value)]:rt=et(lt).y,$[q].forEach(function(vt){T[G]=[y[p.get(vt)],rt],d[G]=[y[p.get(vt)],C[p.get(vt)]],G++})},gt=0;gt<$.length;gt++)K(gt);D=!0}l.relativePlacementConstraint&&(O=!0)})();else if(l.relativePlacementConstraint){for(var _=0,S=0,j=0;j<Z.length;j++)Z[j].length>_&&(_=Z[j].length,S=j);if(_<x.size/2)k(l.relativePlacementConstraint),D=!1,O=!1;else{var z=new Map,A=new Map,U=[];Z[S].forEach(function(G){R.get(G).forEach(function(H){H.direction=="horizontal"?(z.has(G)?z.get(G).push(H):z.set(G,[H]),z.has(H.id)||z.set(H.id,[]),U.push({left:G,right:H.id})):(A.has(G)?A.get(G).push(H):A.set(G,[H]),A.has(H.id)||A.set(H.id,[]),U.push({top:G,bottom:H.id}))})}),k(U),O=!1;var X=I(z,"horizontal"),Q=I(A,"vertical");Z[S].forEach(function(G,H){d[H]=[y[p.get(G)],C[p.get(G)]],T[H]=[],X.has(G)?T[H][0]=X.get(G):T[H][0]=y[p.get(G)],Q.has(G)?T[H][1]=Q.get(G):T[H][1]=C[p.get(G)]}),D=!0}}if(D){for(var ht=void 0,Ct=i.transpose(T),Ft=i.transpose(d),J=0;J<Ct.length;J++)Ct[J]=i.multGamma(Ct[J]),Ft[J]=i.multGamma(Ft[J]);var Xt=i.multMat(Ct,i.transpose(Ft)),Ot=r.svd(Xt);ht=i.multMat(Ot.V,i.transpose(Ot.U));for(var ot=0;ot<p.size;ot++){var tt=[y[ot],C[ot]],yt=[ht[0][0],ht[1][0]],mt=[ht[0][1],ht[1][1]];y[ot]=i.dotProduct(tt,yt),C[ot]=i.dotProduct(tt,mt)}O&&k(l.relativePlacementConstraint)}}if(e.ENFORCE_CONSTRAINTS){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>0){var Mt={x:0,y:0};l.fixedNodeConstraint.forEach(function(G,H){var W={x:y[p.get(G.nodeId)],y:C[p.get(G.nodeId)]},B=G.position,$=Y(B,W);Mt.x+=$.x,Mt.y+=$.y}),Mt.x/=l.fixedNodeConstraint.length,Mt.y/=l.fixedNodeConstraint.length,y.forEach(function(G,H){y[H]+=Mt.x}),C.forEach(function(G,H){C[H]+=Mt.y}),l.fixedNodeConstraint.forEach(function(G){y[p.get(G.nodeId)]=G.position.x,C[p.get(G.nodeId)]=G.position.y})}if(l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var Tt=l.alignmentConstraint.vertical,Nt=function(H){var W=new Set;Tt[H].forEach(function(K){W.add(K)});var B=new Set([].concat(f(W)).filter(function(K){return b.has(K)})),$=void 0;B.size>0?$=y[p.get(B.values().next().value)]:$=et(W).x,W.forEach(function(K){b.has(K)||(y[p.get(K)]=$)})},Dt=0;Dt<Tt.length;Dt++)Nt(Dt);if(l.alignmentConstraint.horizontal)for(var Rt=l.alignmentConstraint.horizontal,zt=function(H){var W=new Set;Rt[H].forEach(function(K){W.add(K)});var B=new Set([].concat(f(W)).filter(function(K){return b.has(K)})),$=void 0;B.size>0?$=C[p.get(B.values().next().value)]:$=et(W).y,W.forEach(function(K){b.has(K)||(C[p.get(K)]=$)})},Gt=0;Gt<Rt.length;Gt++)zt(Gt)}l.relativePlacementConstraint&&function(){var G=new Map,H=new Map,W=new Map,B=new Map,$=new Map,K=new Map,gt=new Set,ct=new Set;if(b.forEach(function(Yt){gt.add(Yt),ct.add(Yt)}),l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var q=l.alignmentConstraint.vertical,lt=function(Et){W.set("dummy"+Et,[]),q[Et].forEach(function(wt){G.set(wt,"dummy"+Et),W.get("dummy"+Et).push(wt),b.has(wt)&&gt.add("dummy"+Et)}),$.set("dummy"+Et,y[p.get(q[Et][0])])},ut=0;ut<q.length;ut++)lt(ut);if(l.alignmentConstraint.horizontal)for(var rt=l.alignmentConstraint.horizontal,vt=function(Et){B.set("dummy"+Et,[]),rt[Et].forEach(function(wt){H.set(wt,"dummy"+Et),B.get("dummy"+Et).push(wt),b.has(wt)&&ct.add("dummy"+Et)}),K.set("dummy"+Et,C[p.get(rt[Et][0])])},At=0;At<rt.length;At++)vt(At)}var st=new Map,it=new Map,dt=function(Et){R.get(Et).forEach(function(wt){var qt=void 0,Zt=void 0;wt.direction=="horizontal"?(qt=G.get(Et)?G.get(Et):Et,G.get(wt.id)?Zt={id:G.get(wt.id),gap:wt.gap,direction:wt.direction}:Zt=wt,st.has(qt)?st.get(qt).push(Zt):st.set(qt,[Zt]),st.has(Zt.id)||st.set(Zt.id,[])):(qt=H.get(Et)?H.get(Et):Et,H.get(wt.id)?Zt={id:H.get(wt.id),gap:wt.gap,direction:wt.direction}:Zt=wt,it.has(qt)?it.get(qt).push(Zt):it.set(qt,[Zt]),it.has(Zt.id)||it.set(Zt.id,[]))})},nt=!0,ft=!1,bt=void 0;try{for(var It=R.keys()[Symbol.iterator](),Bt;!(nt=(Bt=It.next()).done);nt=!0){var xt=Bt.value;dt(xt)}}catch(Yt){ft=!0,bt=Yt}finally{try{!nt&&It.return&&It.return()}finally{if(ft)throw bt}}var Lt=E(st),kt=E(it),St=s(Lt),Wt=s(kt),$t=g(st),de=g(it),Qt=[],jt=[];St.forEach(function(Yt,Et){Qt[Et]=[],Yt.forEach(function(wt){$t.get(wt).length==0&&Qt[Et].push(wt)})}),Wt.forEach(function(Yt,Et){jt[Et]=[],Yt.forEach(function(wt){de.get(wt).length==0&&jt[Et].push(wt)})});var _t=I(st,"horizontal",gt,$,Qt),Kt=I(it,"vertical",ct,K,jt),le=function(Et){W.get(Et)?W.get(Et).forEach(function(wt){y[p.get(wt)]=_t.get(Et)}):y[p.get(Et)]=_t.get(Et)},te=!0,ee=!1,De=void 0;try{for(var ve=_t.keys()[Symbol.iterator](),xe;!(te=(xe=ve.next()).done);te=!0){var pe=xe.value;le(pe)}}catch(Yt){ee=!0,De=Yt}finally{try{!te&&ve.return&&ve.return()}finally{if(ee)throw De}}var cr=function(Et){B.get(Et)?B.get(Et).forEach(function(wt){C[p.get(wt)]=Kt.get(Et)}):C[p.get(Et)]=Kt.get(Et)},ye=!0,Ie=!1,Re=void 0;try{for(var Ee=Kt.keys()[Symbol.iterator](),Se;!(ye=(Se=Ee.next()).done);ye=!0){var pe=Se.value;cr(pe)}}catch(Yt){Ie=!0,Re=Yt}finally{try{!ye&&Ee.return&&Ee.return()}finally{if(Ie)throw Re}}}()}for(var Ht=0;Ht<F.length;Ht++){var Pt=F[Ht];Pt.getChild()==null&&Pt.setCenter(y[p.get(Pt.id)],C[p.get(Pt.id)])}},n.exports=h},551:n=>{n.exports=m}},L={};function u(n){var t=L[n];if(t!==void 0)return t.exports;var a=L[n]={exports:{}};return w[n](a,a.exports,u),a.exports}var o=u(45);return o})()})});var Je=me((se,we)=>{(function(w,L){typeof se=="object"&&typeof we=="object"?we.exports=L(Ae()):typeof define=="function"&&define.amd?define(["cose-base"],L):typeof se=="object"?se.cytoscapeFcose=L(Ae()):w.cytoscapeFcose=L(w.coseBase)})(se,function(m){return(()=>{"use strict";var w={658:n=>{n.exports=Object.assign!=null?Object.assign.bind(Object):function(t){for(var a=arguments.length,f=Array(a>1?a-1:0),e=1;e<a;e++)f[e-1]=arguments[e];return f.forEach(function(v){Object.keys(v).forEach(function(i){return t[i]=v[i]})}),t}},548:(n,t,a)=>{var f=function(){function i(r,h){var c=[],l=!0,N=!1,p=void 0;try{for(var y=r[Symbol.iterator](),C;!(l=(C=y.next()).done)&&(c.push(C.value),!(h&&c.length===h));l=!0);}catch(F){N=!0,p=F}finally{try{!l&&y.return&&y.return()}finally{if(N)throw p}}return c}return function(r,h){if(Array.isArray(r))return r;if(Symbol.iterator in Object(r))return i(r,h);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),e=a(140).layoutBase.LinkedList,v={};v.getTopMostNodes=function(i){for(var r={},h=0;h<i.length;h++)r[i[h].id()]=!0;var c=i.filter(function(l,N){typeof l=="number"&&(l=N);for(var p=l.parent()[0];p!=null;){if(r[p.id()])return!1;p=p.parent()[0]}return!0});return c},v.connectComponents=function(i,r,h,c){var l=new e,N=new Set,p=[],y=void 0,C=void 0,F=void 0,M=!1,P=1,V=[],Y=[],et=function(){var k=i.collection();Y.push(k);var s=h[0],E=i.collection();E.merge(s).merge(s.descendants().intersection(r)),p.push(s),E.forEach(function(d){l.push(d),N.add(d),k.merge(d)});for(var g=function(){s=l.shift();var D=i.collection();s.neighborhood().nodes().forEach(function(x){r.intersection(s.edgesWith(x)).length>0&&D.merge(x)});for(var O=0;O<D.length;O++){var b=D[O];if(y=h.intersection(b.union(b.ancestors())),y!=null&&!N.has(y[0])){var R=y.union(y.descendants());R.forEach(function(x){l.push(x),N.add(x),k.merge(x),h.has(x)&&p.push(x)})}}};l.length!=0;)g();if(k.forEach(function(d){r.intersection(d.connectedEdges()).forEach(function(D){k.has(D.source())&&k.has(D.target())&&k.merge(D)})}),p.length==h.length&&(M=!0),!M||M&&P>1){C=p[0],F=C.connectedEdges().length,p.forEach(function(d){d.connectedEdges().length<F&&(F=d.connectedEdges().length,C=d)}),V.push(C.id());var T=i.collection();T.merge(p[0]),p.forEach(function(d){T.merge(d)}),p=[],h=h.difference(T),P++}};do et();while(!M);return c&&V.length>0&&c.set("dummy"+(c.size+1),V),Y},v.relocateComponent=function(i,r,h){if(!h.fixedNodeConstraint){var c=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,N=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY;if(h.quality=="draft"){var y=!0,C=!1,F=void 0;try{for(var M=r.nodeIndexes[Symbol.iterator](),P;!(y=(P=M.next()).done);y=!0){var V=P.value,Y=f(V,2),et=Y[0],I=Y[1],k=h.cy.getElementById(et);if(k){var s=k.boundingBox(),E=r.xCoords[I]-s.w/2,g=r.xCoords[I]+s.w/2,T=r.yCoords[I]-s.h/2,d=r.yCoords[I]+s.h/2;E<c&&(c=E),g>l&&(l=g),T<N&&(N=T),d>p&&(p=d)}}}catch(x){C=!0,F=x}finally{try{!y&&M.return&&M.return()}finally{if(C)throw F}}var D=i.x-(l+c)/2,O=i.y-(p+N)/2;r.xCoords=r.xCoords.map(function(x){return x+D}),r.yCoords=r.yCoords.map(function(x){return x+O})}else{Object.keys(r).forEach(function(x){var Z=r[x],_=Z.getRect().x,S=Z.getRect().x+Z.getRect().width,j=Z.getRect().y,z=Z.getRect().y+Z.getRect().height;_<c&&(c=_),S>l&&(l=S),j<N&&(N=j),z>p&&(p=z)});var b=i.x-(l+c)/2,R=i.y-(p+N)/2;Object.keys(r).forEach(function(x){var Z=r[x];Z.setCenter(Z.getCenterX()+b,Z.getCenterY()+R)})}}},v.calcBoundingBox=function(i,r,h,c){for(var l=Number.MAX_SAFE_INTEGER,N=Number.MIN_SAFE_INTEGER,p=Number.MAX_SAFE_INTEGER,y=Number.MIN_SAFE_INTEGER,C=void 0,F=void 0,M=void 0,P=void 0,V=i.descendants().not(":parent"),Y=V.length,et=0;et<Y;et++){var I=V[et];C=r[c.get(I.id())]-I.width()/2,F=r[c.get(I.id())]+I.width()/2,M=h[c.get(I.id())]-I.height()/2,P=h[c.get(I.id())]+I.height()/2,l>C&&(l=C),N<F&&(N=F),p>M&&(p=M),y<P&&(y=P)}var k={};return k.topLeftX=l,k.topLeftY=p,k.width=N-l,k.height=y-p,k},v.calcParentsWithoutChildren=function(i,r){var h=i.collection();return r.nodes(":parent").forEach(function(c){var l=!1;c.children().forEach(function(N){N.css("display")!="none"&&(l=!0)}),l||h.merge(c)}),h},n.exports=v},816:(n,t,a)=>{var f=a(548),e=a(140).CoSELayout,v=a(140).CoSENode,i=a(140).layoutBase.PointD,r=a(140).layoutBase.DimensionD,h=a(140).layoutBase.LayoutConstants,c=a(140).layoutBase.FDLayoutConstants,l=a(140).CoSEConstants,N=function(y,C){var F=y.cy,M=y.eles,P=M.nodes(),V=M.edges(),Y=void 0,et=void 0,I=void 0,k={};y.randomize&&(Y=C.nodeIndexes,et=C.xCoords,I=C.yCoords);var s=function(x){return typeof x=="function"},E=function(x,Z){return s(x)?x(Z):x},g=f.calcParentsWithoutChildren(F,M),T=function R(x,Z,_,S){for(var j=Z.length,z=0;z<j;z++){var A=Z[z],U=null;A.intersection(g).length==0&&(U=A.children());var X=void 0,Q=A.layoutDimensions({nodeDimensionsIncludeLabels:S.nodeDimensionsIncludeLabels});if(A.outerWidth()!=null&&A.outerHeight()!=null)if(S.randomize)if(!A.isParent())X=x.add(new v(_.graphManager,new i(et[Y.get(A.id())]-Q.w/2,I[Y.get(A.id())]-Q.h/2),new r(parseFloat(Q.w),parseFloat(Q.h))));else{var ht=f.calcBoundingBox(A,et,I,Y);A.intersection(g).length==0?X=x.add(new v(_.graphManager,new i(ht.topLeftX,ht.topLeftY),new r(ht.width,ht.height))):X=x.add(new v(_.graphManager,new i(ht.topLeftX,ht.topLeftY),new r(parseFloat(Q.w),parseFloat(Q.h))))}else X=x.add(new v(_.graphManager,new i(A.position("x")-Q.w/2,A.position("y")-Q.h/2),new r(parseFloat(Q.w),parseFloat(Q.h))));else X=x.add(new v(this.graphManager));if(X.id=A.data("id"),X.nodeRepulsion=E(S.nodeRepulsion,A),X.paddingLeft=parseInt(A.css("padding")),X.paddingTop=parseInt(A.css("padding")),X.paddingRight=parseInt(A.css("padding")),X.paddingBottom=parseInt(A.css("padding")),S.nodeDimensionsIncludeLabels&&(X.labelWidth=A.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,X.labelHeight=A.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,X.labelPosVertical=A.css("text-valign"),X.labelPosHorizontal=A.css("text-halign")),k[A.data("id")]=X,isNaN(X.rect.x)&&(X.rect.x=0),isNaN(X.rect.y)&&(X.rect.y=0),U!=null&&U.length>0){var Ct=void 0;Ct=_.getGraphManager().add(_.newGraph(),X),R(Ct,U,_,S)}}},d=function(x,Z,_){for(var S=0,j=0,z=0;z<_.length;z++){var A=_[z],U=k[A.data("source")],X=k[A.data("target")];if(U&&X&&U!==X&&U.getEdgesBetween(X).length==0){var Q=Z.add(x.newEdge(),U,X);Q.id=A.id(),Q.idealLength=E(y.idealEdgeLength,A),Q.edgeElasticity=E(y.edgeElasticity,A),S+=Q.idealLength,j++}}y.idealEdgeLength!=null&&(j>0?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=S/j:s(y.idealEdgeLength)?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=50:l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=y.idealEdgeLength,l.MIN_REPULSION_DIST=c.MIN_REPULSION_DIST=c.DEFAULT_EDGE_LENGTH/10,l.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH)},D=function(x,Z){Z.fixedNodeConstraint&&(x.constraints.fixedNodeConstraint=Z.fixedNodeConstraint),Z.alignmentConstraint&&(x.constraints.alignmentConstraint=Z.alignmentConstraint),Z.relativePlacementConstraint&&(x.constraints.relativePlacementConstraint=Z.relativePlacementConstraint)};y.nestingFactor!=null&&(l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=y.nestingFactor),y.gravity!=null&&(l.DEFAULT_GRAVITY_STRENGTH=c.DEFAULT_GRAVITY_STRENGTH=y.gravity),y.numIter!=null&&(l.MAX_ITERATIONS=c.MAX_ITERATIONS=y.numIter),y.gravityRange!=null&&(l.DEFAULT_GRAVITY_RANGE_FACTOR=c.DEFAULT_GRAVITY_RANGE_FACTOR=y.gravityRange),y.gravityCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=y.gravityCompound),y.gravityRangeCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=y.gravityRangeCompound),y.initialEnergyOnIncremental!=null&&(l.DEFAULT_COOLING_FACTOR_INCREMENTAL=c.DEFAULT_COOLING_FACTOR_INCREMENTAL=y.initialEnergyOnIncremental),y.tilingCompareBy!=null&&(l.TILING_COMPARE_BY=y.tilingCompareBy),y.quality=="proof"?h.QUALITY=2:h.QUALITY=0,l.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=h.NODE_DIMENSIONS_INCLUDE_LABELS=y.nodeDimensionsIncludeLabels,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!y.randomize,l.ANIMATE=c.ANIMATE=h.ANIMATE=y.animate,l.TILE=y.tile,l.TILING_PADDING_VERTICAL=typeof y.tilingPaddingVertical=="function"?y.tilingPaddingVertical.call():y.tilingPaddingVertical,l.TILING_PADDING_HORIZONTAL=typeof y.tilingPaddingHorizontal=="function"?y.tilingPaddingHorizontal.call():y.tilingPaddingHorizontal,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!0,l.PURE_INCREMENTAL=!y.randomize,h.DEFAULT_UNIFORM_LEAF_NODE_SIZES=y.uniformNodeDimensions,y.step=="transformed"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!1),y.step=="enforced"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!1),y.step=="cose"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!0),y.step=="all"&&(y.randomize?l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!0),y.fixedNodeConstraint||y.alignmentConstraint||y.relativePlacementConstraint?l.TREE_REDUCTION_ON_INCREMENTAL=!1:l.TREE_REDUCTION_ON_INCREMENTAL=!0;var O=new e,b=O.newGraphManager();return T(b.addRoot(),f.getTopMostNodes(P),O,y),d(O,b,V),D(O,y),O.runLayout(),k};n.exports={coseLayout:N}},212:(n,t,a)=>{var f=function(){function y(C,F){for(var M=0;M<F.length;M++){var P=F[M];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(C,P.key,P)}}return function(C,F,M){return F&&y(C.prototype,F),M&&y(C,M),C}}();function e(y,C){if(!(y instanceof C))throw new TypeError("Cannot call a class as a function")}var v=a(658),i=a(548),r=a(657),h=r.spectralLayout,c=a(816),l=c.coseLayout,N=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(C){return 4500},idealEdgeLength:function(C){return 50},edgeElasticity:function(C){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),p=function(){function y(C){e(this,y),this.options=v({},N,C)}return f(y,[{key:"run",value:function(){var F=this,M=this.options,P=M.cy,V=M.eles,Y=[],et=void 0,I=void 0,k=[],s=void 0,E=[];M.fixedNodeConstraint&&(!Array.isArray(M.fixedNodeConstraint)||M.fixedNodeConstraint.length==0)&&(M.fixedNodeConstraint=void 0),M.alignmentConstraint&&(M.alignmentConstraint.vertical&&(!Array.isArray(M.alignmentConstraint.vertical)||M.alignmentConstraint.vertical.length==0)&&(M.alignmentConstraint.vertical=void 0),M.alignmentConstraint.horizontal&&(!Array.isArray(M.alignmentConstraint.horizontal)||M.alignmentConstraint.horizontal.length==0)&&(M.alignmentConstraint.horizontal=void 0)),M.relativePlacementConstraint&&(!Array.isArray(M.relativePlacementConstraint)||M.relativePlacementConstraint.length==0)&&(M.relativePlacementConstraint=void 0);var g=M.fixedNodeConstraint||M.alignmentConstraint||M.relativePlacementConstraint;g&&(M.tile=!1,M.packComponents=!1);var T=void 0,d=!1;if(P.layoutUtilities&&M.packComponents&&(T=P.layoutUtilities("get"),T||(T=P.layoutUtilities()),d=!0),V.nodes().length>0)if(d){var b=i.getTopMostNodes(M.eles.nodes());if(s=i.connectComponents(P,M.eles,b),s.forEach(function(ot){var tt=ot.boundingBox();E.push({x:tt.x1+tt.w/2,y:tt.y1+tt.h/2})}),M.randomize&&s.forEach(function(ot){M.eles=ot,Y.push(h(M))}),M.quality=="default"||M.quality=="proof"){var R=P.collection();if(M.tile){var x=new Map,Z=[],_=[],S=0,j={nodeIndexes:x,xCoords:Z,yCoords:_},z=[];if(s.forEach(function(ot,tt){ot.edges().length==0&&(ot.nodes().forEach(function(yt,mt){R.merge(ot.nodes()[mt]),yt.isParent()||(j.nodeIndexes.set(ot.nodes()[mt].id(),S++),j.xCoords.push(ot.nodes()[0].position().x),j.yCoords.push(ot.nodes()[0].position().y))}),z.push(tt))}),R.length>1){var A=R.boundingBox();E.push({x:A.x1+A.w/2,y:A.y1+A.h/2}),s.push(R),Y.push(j);for(var U=z.length-1;U>=0;U--)s.splice(z[U],1),Y.splice(z[U],1),E.splice(z[U],1)}}s.forEach(function(ot,tt){M.eles=ot,k.push(l(M,Y[tt])),i.relocateComponent(E[tt],k[tt],M)})}else s.forEach(function(ot,tt){i.relocateComponent(E[tt],Y[tt],M)});var X=new Set;if(s.length>1){var Q=[],ht=V.filter(function(ot){return ot.css("display")=="none"});s.forEach(function(ot,tt){var yt=void 0;if(M.quality=="draft"&&(yt=Y[tt].nodeIndexes),ot.nodes().not(ht).length>0){var mt={};mt.edges=[],mt.nodes=[];var Mt=void 0;ot.nodes().not(ht).forEach(function(Tt){if(M.quality=="draft")if(!Tt.isParent())Mt=yt.get(Tt.id()),mt.nodes.push({x:Y[tt].xCoords[Mt]-Tt.boundingbox().w/2,y:Y[tt].yCoords[Mt]-Tt.boundingbox().h/2,width:Tt.boundingbox().w,height:Tt.boundingbox().h});else{var Nt=i.calcBoundingBox(Tt,Y[tt].xCoords,Y[tt].yCoords,yt);mt.nodes.push({x:Nt.topLeftX,y:Nt.topLeftY,width:Nt.width,height:Nt.height})}else k[tt][Tt.id()]&&mt.nodes.push({x:k[tt][Tt.id()].getLeft(),y:k[tt][Tt.id()].getTop(),width:k[tt][Tt.id()].getWidth(),height:k[tt][Tt.id()].getHeight()})}),ot.edges().forEach(function(Tt){var Nt=Tt.source(),Dt=Tt.target();if(Nt.css("display")!="none"&&Dt.css("display")!="none")if(M.quality=="draft"){var Rt=yt.get(Nt.id()),zt=yt.get(Dt.id()),Gt=[],Ht=[];if(Nt.isParent()){var Pt=i.calcBoundingBox(Nt,Y[tt].xCoords,Y[tt].yCoords,yt);Gt.push(Pt.topLeftX+Pt.width/2),Gt.push(Pt.topLeftY+Pt.height/2)}else Gt.push(Y[tt].xCoords[Rt]),Gt.push(Y[tt].yCoords[Rt]);if(Dt.isParent()){var G=i.calcBoundingBox(Dt,Y[tt].xCoords,Y[tt].yCoords,yt);Ht.push(G.topLeftX+G.width/2),Ht.push(G.topLeftY+G.height/2)}else Ht.push(Y[tt].xCoords[zt]),Ht.push(Y[tt].yCoords[zt]);mt.edges.push({startX:Gt[0],startY:Gt[1],endX:Ht[0],endY:Ht[1]})}else k[tt][Nt.id()]&&k[tt][Dt.id()]&&mt.edges.push({startX:k[tt][Nt.id()].getCenterX(),startY:k[tt][Nt.id()].getCenterY(),endX:k[tt][Dt.id()].getCenterX(),endY:k[tt][Dt.id()].getCenterY()})}),mt.nodes.length>0&&(Q.push(mt),X.add(tt))}});var Ct=T.packComponents(Q,M.randomize).shifts;if(M.quality=="draft")Y.forEach(function(ot,tt){var yt=ot.xCoords.map(function(Mt){return Mt+Ct[tt].dx}),mt=ot.yCoords.map(function(Mt){return Mt+Ct[tt].dy});ot.xCoords=yt,ot.yCoords=mt});else{var Ft=0;X.forEach(function(ot){Object.keys(k[ot]).forEach(function(tt){var yt=k[ot][tt];yt.setCenter(yt.getCenterX()+Ct[Ft].dx,yt.getCenterY()+Ct[Ft].dy)}),Ft++})}}}else{var D=M.eles.boundingBox();if(E.push({x:D.x1+D.w/2,y:D.y1+D.h/2}),M.randomize){var O=h(M);Y.push(O)}M.quality=="default"||M.quality=="proof"?(k.push(l(M,Y[0])),i.relocateComponent(E[0],k[0],M)):i.relocateComponent(E[0],Y[0],M)}var J=function(tt,yt){if(M.quality=="default"||M.quality=="proof"){typeof tt=="number"&&(tt=yt);var mt=void 0,Mt=void 0,Tt=tt.data("id");return k.forEach(function(Dt){Tt in Dt&&(mt={x:Dt[Tt].getRect().getCenterX(),y:Dt[Tt].getRect().getCenterY()},Mt=Dt[Tt])}),M.nodeDimensionsIncludeLabels&&(Mt.labelWidth&&(Mt.labelPosHorizontal=="left"?mt.x+=Mt.labelWidth/2:Mt.labelPosHorizontal=="right"&&(mt.x-=Mt.labelWidth/2)),Mt.labelHeight&&(Mt.labelPosVertical=="top"?mt.y+=Mt.labelHeight/2:Mt.labelPosVertical=="bottom"&&(mt.y-=Mt.labelHeight/2))),mt==null&&(mt={x:tt.position("x"),y:tt.position("y")}),{x:mt.x,y:mt.y}}else{var Nt=void 0;return Y.forEach(function(Dt){var Rt=Dt.nodeIndexes.get(tt.id());Rt!=null&&(Nt={x:Dt.xCoords[Rt],y:Dt.yCoords[Rt]})}),Nt==null&&(Nt={x:tt.position("x"),y:tt.position("y")}),{x:Nt.x,y:Nt.y}}};if(M.quality=="default"||M.quality=="proof"||M.randomize){var Xt=i.calcParentsWithoutChildren(P,V),Ot=V.filter(function(ot){return ot.css("display")=="none"});M.eles=V.not(Ot),V.nodes().not(":parent").not(Ot).layoutPositions(F,M,J),Xt.length>0&&Xt.forEach(function(ot){ot.position(J(ot))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),y}();n.exports=p},657:(n,t,a)=>{var f=a(548),e=a(140).layoutBase.Matrix,v=a(140).layoutBase.SVD,i=function(h){var c=h.cy,l=h.eles,N=l.nodes(),p=l.nodes(":parent"),y=new Map,C=new Map,F=new Map,M=[],P=[],V=[],Y=[],et=[],I=[],k=[],s=[],E=void 0,g=void 0,T=1e8,d=1e-9,D=h.piTol,O=h.samplingType,b=h.nodeSeparation,R=void 0,x=function(){for(var W=0,B=0,$=!1;B<R;){W=Math.floor(Math.random()*g),$=!1;for(var K=0;K<B;K++)if(Y[K]==W){$=!0;break}if(!$)Y[B]=W,B++;else continue}},Z=function(W,B,$){for(var K=[],gt=0,ct=0,q=0,lt=void 0,ut=[],rt=0,vt=1,At=0;At<g;At++)ut[At]=T;for(K[ct]=W,ut[W]=0;ct>=gt;){q=K[gt++];for(var st=M[q],it=0;it<st.length;it++)lt=C.get(st[it]),ut[lt]==T&&(ut[lt]=ut[q]+1,K[++ct]=lt);I[q][B]=ut[q]*b}if($){for(var dt=0;dt<g;dt++)I[dt][B]<et[dt]&&(et[dt]=I[dt][B]);for(var nt=0;nt<g;nt++)et[nt]>rt&&(rt=et[nt],vt=nt)}return vt},_=function(W){var B=void 0;if(W){B=Math.floor(Math.random()*g),E=B;for(var K=0;K<g;K++)et[K]=T;for(var gt=0;gt<R;gt++)Y[gt]=B,B=Z(B,gt,W)}else{x();for(var $=0;$<R;$++)Z(Y[$],$,W,!1)}for(var ct=0;ct<g;ct++)for(var q=0;q<R;q++)I[ct][q]*=I[ct][q];for(var lt=0;lt<R;lt++)k[lt]=[];for(var ut=0;ut<R;ut++)for(var rt=0;rt<R;rt++)k[ut][rt]=I[Y[rt]][ut]},S=function(){for(var W=v.svd(k),B=W.S,$=W.U,K=W.V,gt=B[0]*B[0]*B[0],ct=[],q=0;q<R;q++){ct[q]=[];for(var lt=0;lt<R;lt++)ct[q][lt]=0,q==lt&&(ct[q][lt]=B[q]/(B[q]*B[q]+gt/(B[q]*B[q])))}s=e.multMat(e.multMat(K,ct),e.transpose($))},j=function(){for(var W=void 0,B=void 0,$=[],K=[],gt=[],ct=[],q=0;q<g;q++)$[q]=Math.random(),K[q]=Math.random();$=e.normalize($),K=e.normalize(K);for(var lt=0,ut=d,rt=d,vt=void 0;;){lt++;for(var At=0;At<g;At++)gt[At]=$[At];if($=e.multGamma(e.multL(e.multGamma(gt),I,s)),W=e.dotProduct(gt,$),$=e.normalize($),ut=e.dotProduct(gt,$),vt=Math.abs(ut/rt),vt<=1+D&&vt>=1)break;rt=ut}for(var st=0;st<g;st++)gt[st]=$[st];for(lt=0,rt=d;;){lt++;for(var it=0;it<g;it++)ct[it]=K[it];if(ct=e.minusOp(ct,e.multCons(gt,e.dotProduct(gt,ct))),K=e.multGamma(e.multL(e.multGamma(ct),I,s)),B=e.dotProduct(ct,K),K=e.normalize(K),ut=e.dotProduct(ct,K),vt=Math.abs(ut/rt),vt<=1+D&&vt>=1)break;rt=ut}for(var dt=0;dt<g;dt++)ct[dt]=K[dt];P=e.multCons(gt,Math.sqrt(Math.abs(W))),V=e.multCons(ct,Math.sqrt(Math.abs(B)))};f.connectComponents(c,l,f.getTopMostNodes(N),y),p.forEach(function(H){f.connectComponents(c,l,f.getTopMostNodes(H.descendants().intersection(l)),y)});for(var z=0,A=0;A<N.length;A++)N[A].isParent()||C.set(N[A].id(),z++);var U=!0,X=!1,Q=void 0;try{for(var ht=y.keys()[Symbol.iterator](),Ct;!(U=(Ct=ht.next()).done);U=!0){var Ft=Ct.value;C.set(Ft,z++)}}catch(H){X=!0,Q=H}finally{try{!U&&ht.return&&ht.return()}finally{if(X)throw Q}}for(var J=0;J<C.size;J++)M[J]=[];p.forEach(function(H){for(var W=H.children().intersection(l);W.nodes(":childless").length==0;)W=W.nodes()[0].children().intersection(l);var B=0,$=W.nodes(":childless")[0].connectedEdges().length;W.nodes(":childless").forEach(function(K,gt){K.connectedEdges().length<$&&($=K.connectedEdges().length,B=gt)}),F.set(H.id(),W.nodes(":childless")[B].id())}),N.forEach(function(H){var W=void 0;H.isParent()?W=C.get(F.get(H.id())):W=C.get(H.id()),H.neighborhood().nodes().forEach(function(B){l.intersection(H.edgesWith(B)).length>0&&(B.isParent()?M[W].push(F.get(B.id())):M[W].push(B.id()))})});var Xt=function(W){var B=C.get(W),$=void 0;y.get(W).forEach(function(K){c.getElementById(K).isParent()?$=F.get(K):$=K,M[B].push($),M[C.get($)].push(W)})},Ot=!0,ot=!1,tt=void 0;try{for(var yt=y.keys()[Symbol.iterator](),mt;!(Ot=(mt=yt.next()).done);Ot=!0){var Mt=mt.value;Xt(Mt)}}catch(H){ot=!0,tt=H}finally{try{!Ot&&yt.return&&yt.return()}finally{if(ot)throw tt}}g=C.size;var Tt=void 0;if(g>2){R=g<h.sampleSize?g:h.sampleSize;for(var Nt=0;Nt<g;Nt++)I[Nt]=[];for(var Dt=0;Dt<R;Dt++)s[Dt]=[];return h.quality=="draft"||h.step=="all"?(_(O),S(),j(),Tt={nodeIndexes:C,xCoords:P,yCoords:V}):(C.forEach(function(H,W){P.push(c.getElementById(W).position("x")),V.push(c.getElementById(W).position("y"))}),Tt={nodeIndexes:C,xCoords:P,yCoords:V}),Tt}else{var Rt=C.keys(),zt=c.getElementById(Rt.next().value),Gt=zt.position(),Ht=zt.outerWidth();if(P.push(Gt.x),V.push(Gt.y),g==2){var Pt=c.getElementById(Rt.next().value),G=Pt.outerWidth();P.push(Gt.x+Ht/2+G/2+h.idealEdgeLength),V.push(Gt.y)}return Tt={nodeIndexes:C,xCoords:P,yCoords:V},Tt}};n.exports={spectralLayout:i}},579:(n,t,a)=>{var f=a(212),e=function(i){i&&i("layout","fcose",f)};typeof cytoscape<"u"&&e(cytoscape),n.exports=e},140:n=>{n.exports=m}},L={};function u(n){var t=L[n];if(t!==void 0)return t.exports;var a=L[n]={exports:{}};return w[n](a,a.exports,u),a.exports}var o=u(579);return o})()})});var rr=ur(Je(),1);var Qe={L:"left",R:"right",T:"top",B:"bottom"},Ke={L:at(m=>`${m},${m/2} 0,${m} 0,0`,"L"),R:at(m=>`0,${m/2} ${m},0 ${m},${m}`,"R"),T:at(m=>`0,0 ${m},0 ${m/2},${m}`,"T"),B:at(m=>`${m/2},0 ${m},${m} 0,${m}`,"B")},ue={L:at((m,w)=>m-w+2,"L"),R:at((m,w)=>m-2,"R"),T:at((m,w)=>m-w+2,"T"),B:at((m,w)=>m-2,"B")},gr=at(function(m){return Vt(m)?m==="L"?"R":"L":m==="T"?"B":"T"},"getOppositeArchitectureDirection"),je=at(function(m){let w=m;return w==="L"||w==="R"||w==="T"||w==="B"},"isArchitectureDirection"),Vt=at(function(m){let w=m;return w==="L"||w==="R"},"isArchitectureDirectionX"),Jt=at(function(m){let w=m;return w==="T"||w==="B"},"isArchitectureDirectionY"),_e=at(function(m,w){let L=Vt(m)&&Jt(w),u=Jt(m)&&Vt(w);return L||u},"isArchitectureDirectionXY"),dr=at(function(m){let w=m[0],L=m[1],u=Vt(w)&&Jt(L),o=Jt(w)&&Vt(L);return u||o},"isArchitecturePairXY"),vr=at(function(m){return m!=="LL"&&m!=="RR"&&m!=="TT"&&m!=="BB"},"isValidArchitectureDirectionPair"),Oe=at(function(m,w){let L=`${m}${w}`;return vr(L)?L:void 0},"getArchitectureDirectionPair"),pr=at(function([m,w],L){let u=L[0],o=L[1];return Vt(u)?Jt(o)?[m+(u==="L"?-1:1),w+(o==="T"?1:-1)]:[m+(u==="L"?-1:1),w]:Vt(o)?[m+(o==="L"?1:-1),w+(u==="T"?1:-1)]:[m,w+(u==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),yr=at(function(m){return m==="LT"||m==="TL"?[1,1]:m==="BL"||m==="LB"?[1,-1]:m==="BR"||m==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),Er=at(function(m){return m.type==="service"},"isArchitectureService"),mr=at(function(m){return m.type==="junction"},"isArchitectureJunction"),tr=at(m=>m.data(),"edgeData"),ie=at(m=>m.data(),"nodeData"),er=Fe.architecture,pt=new ke(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:er,dataStructures:void 0,elements:{}})),Tr=at(()=>{pt.reset(),Pe()},"clear"),Nr=at(function({id:m,icon:w,in:L,title:u,iconText:o}){if(pt.records.registeredIds[m]!==void 0)throw new Error(`The service id [${m}] is already in use by another ${pt.records.registeredIds[m]}`);if(L!==void 0){if(m===L)throw new Error(`The service [${m}] cannot be placed within itself`);if(pt.records.registeredIds[L]===void 0)throw new Error(`The service [${m}]'s parent does not exist. Please make sure the parent is created before this service`);if(pt.records.registeredIds[L]==="node")throw new Error(`The service [${m}]'s parent is not a group`)}pt.records.registeredIds[m]="node",pt.records.nodes[m]={id:m,type:"service",icon:w,iconText:o,title:u,edges:[],in:L}},"addService"),Lr=at(()=>Object.values(pt.records.nodes).filter(Er),"getServices"),Cr=at(function({id:m,in:w}){pt.records.registeredIds[m]="node",pt.records.nodes[m]={id:m,type:"junction",edges:[],in:w}},"addJunction"),Mr=at(()=>Object.values(pt.records.nodes).filter(mr),"getJunctions"),Ar=at(()=>Object.values(pt.records.nodes),"getNodes"),wr=at(m=>pt.records.nodes[m],"getNode"),Or=at(function({id:m,icon:w,in:L,title:u}){if(pt.records.registeredIds[m]!==void 0)throw new Error(`The group id [${m}] is already in use by another ${pt.records.registeredIds[m]}`);if(L!==void 0){if(m===L)throw new Error(`The group [${m}] cannot be placed within itself`);if(pt.records.registeredIds[L]===void 0)throw new Error(`The group [${m}]'s parent does not exist. Please make sure the parent is created before this group`);if(pt.records.registeredIds[L]==="node")throw new Error(`The group [${m}]'s parent is not a group`)}pt.records.registeredIds[m]="group",pt.records.groups[m]={id:m,icon:w,title:u,in:L}},"addGroup"),Dr=at(()=>Object.values(pt.records.groups),"getGroups"),xr=at(function({lhsId:m,rhsId:w,lhsDir:L,rhsDir:u,lhsInto:o,rhsInto:n,lhsGroup:t,rhsGroup:a,title:f}){if(!je(L))throw new Error(`Invalid direction given for left hand side of edge ${m}--${w}. Expected (L,R,T,B) got ${L}`);if(!je(u))throw new Error(`Invalid direction given for right hand side of edge ${m}--${w}. Expected (L,R,T,B) got ${u}`);if(pt.records.nodes[m]===void 0&&pt.records.groups[m]===void 0)throw new Error(`The left-hand id [${m}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(pt.records.nodes[w]===void 0&&pt.records.groups[m]===void 0)throw new Error(`The right-hand id [${w}] does not yet exist. Please create the service/group before declaring an edge to it.`);let e=pt.records.nodes[m].in,v=pt.records.nodes[w].in;if(t&&e&&v&&e==v)throw new Error(`The left-hand id [${m}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(a&&e&&v&&e==v)throw new Error(`The right-hand id [${w}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);let i={lhsId:m,lhsDir:L,lhsInto:o,lhsGroup:t,rhsId:w,rhsDir:u,rhsInto:n,rhsGroup:a,title:f};pt.records.edges.push(i),pt.records.nodes[m]&&pt.records.nodes[w]&&(pt.records.nodes[m].edges.push(pt.records.edges[pt.records.edges.length-1]),pt.records.nodes[w].edges.push(pt.records.edges[pt.records.edges.length-1]))},"addEdge"),Ir=at(()=>pt.records.edges,"getEdges"),Rr=at(()=>{if(pt.records.dataStructures===void 0){let m=Object.entries(pt.records.nodes).reduce((t,[a,f])=>(t[a]=f.edges.reduce((e,v)=>{if(v.lhsId===a){let i=Oe(v.lhsDir,v.rhsDir);i&&(e[i]=v.rhsId)}else{let i=Oe(v.rhsDir,v.lhsDir);i&&(e[i]=v.lhsId)}return e},{}),t),{}),w=Object.keys(m)[0],L={[w]:1},u=Object.keys(m).reduce((t,a)=>a===w?t:{...t,[a]:1},{}),o=at(t=>{let a={[t]:[0,0]},f=[t];for(;f.length>0;){let e=f.shift();if(e){L[e]=1,delete u[e];let v=m[e],[i,r]=a[e];Object.entries(v).forEach(([h,c])=>{L[c]||(a[c]=pr([i,r],h),f.push(c))})}}return a},"BFS"),n=[o(w)];for(;Object.keys(u).length>0;)n.push(o(Object.keys(u)[0]));pt.records.dataStructures={adjList:m,spatialMaps:n}}return pt.records.dataStructures},"getDataStructures"),Sr=at((m,w)=>{pt.records.elements[m]=w},"setElementForId"),Fr=at(m=>pt.records.elements[m],"getElementById"),ge={clear:Tr,setDiagramTitle:He,getDiagramTitle:We,setAccTitle:Ge,getAccTitle:Ue,setAccDescription:Ye,getAccDescription:Xe,addService:Nr,getServices:Lr,addJunction:Cr,getJunctions:Mr,getNodes:Ar,getNode:wr,addGroup:Or,getGroups:Dr,addEdge:xr,getEdges:Ir,setElementForId:Sr,getElementById:Fr,getDataStructures:Rr};function Ut(m){let w=ae().architecture;return w?.[m]?w[m]:er[m]}at(Ut,"getConfigField");var br=at((m,w)=>{Ze(m,w),m.groups.map(w.addGroup),m.services.map(L=>w.addService({...L,type:"service"})),m.junctions.map(L=>w.addJunction({...L,type:"junction"})),m.edges.map(w.addEdge)},"populateDb"),Pr={parse:at(async m=>{let w=await qe("architecture",m);Te.debug(w),br(w,ge)},"parse")},Gr=at(m=>`
  .edge {
    stroke-width: ${m.archEdgeWidth};
    stroke: ${m.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${m.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${m.archGroupBorderColor};
    stroke-width: ${m.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Ur=Gr,re=at(m=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${m}</g>`,"wrapIcon"),he={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:re('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:re('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:re('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:re('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:re('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:Ve,blank:{body:re("")}}},Yr=at(async function(m,w){let L=Ut("padding"),u=Ut("iconSize"),o=u/2,n=u/6,t=n/2;await Promise.all(w.edges().map(async a=>{let{source:f,sourceDir:e,sourceArrow:v,sourceGroup:i,target:r,targetDir:h,targetArrow:c,targetGroup:l,label:N}=tr(a),{x:p,y}=a[0].sourceEndpoint(),{x:C,y:F}=a[0].midpoint(),{x:M,y:P}=a[0].targetEndpoint(),V=L+4;if(i&&(Vt(e)?p+=e==="L"?-V:V:y+=e==="T"?-V:V+18),l&&(Vt(h)?M+=h==="L"?-V:V:P+=h==="T"?-V:V+18),!i&&ge.getNode(f)?.type==="junction"&&(Vt(e)?p+=e==="L"?o:-o:y+=e==="T"?o:-o),!l&&ge.getNode(r)?.type==="junction"&&(Vt(h)?M+=h==="L"?o:-o:P+=h==="T"?o:-o),a[0]._private.rscratch){let Y=m.insert("g");if(Y.insert("path").attr("d",`M ${p},${y} L ${C},${F} L${M},${P} `).attr("class","edge"),v){let et=Vt(e)?ue[e](p,n):p-t,I=Jt(e)?ue[e](y,n):y-t;Y.insert("polygon").attr("points",Ke[e](n)).attr("transform",`translate(${et},${I})`).attr("class","arrow")}if(c){let et=Vt(h)?ue[h](M,n):M-t,I=Jt(h)?ue[h](P,n):P-t;Y.insert("polygon").attr("points",Ke[h](n)).attr("transform",`translate(${et},${I})`).attr("class","arrow")}if(N){let et=_e(e,h)?"XY":Vt(e)?"X":"Y",I=0;et==="X"?I=Math.abs(p-M):et==="Y"?I=Math.abs(y-P)/1.5:I=Math.abs(p-M)/2;let k=Y.append("g");if(await ce(k,N,{useHtmlLabels:!1,width:I,classes:"architecture-service-label"},ae()),k.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),et==="X")k.attr("transform","translate("+C+", "+F+")");else if(et==="Y")k.attr("transform","translate("+C+", "+F+") rotate(-90)");else if(et==="XY"){let s=Oe(e,h);if(s&&dr(s)){let E=k.node().getBoundingClientRect(),[g,T]=yr(s);k.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*g*T*45})`);let d=k.node().getBoundingClientRect();k.attr("transform",`
                translate(${C}, ${F-E.height/2})
                translate(${g*d.width/2}, ${T*d.height/2})
                rotate(${-1*g*T*45}, 0, ${E.height/2})
              `)}}}}}))},"drawEdges"),Xr=at(async function(m,w){let u=Ut("padding")*.75,o=Ut("fontSize"),t=Ut("iconSize")/2;await Promise.all(w.nodes().map(async a=>{let f=ie(a);if(f.type==="group"){let{h:e,w:v,x1:i,y1:r}=a.boundingBox();m.append("rect").attr("x",i+t).attr("y",r+t).attr("width",v).attr("height",e).attr("class","node-bkg");let h=m.append("g"),c=i,l=r;if(f.icon){let N=h.append("g");N.html(`<g>${await fe(f.icon,{height:u,width:u,fallbackPrefix:he.prefix})}</g>`),N.attr("transform","translate("+(c+t+1)+", "+(l+t+1)+")"),c+=u,l+=o/2-1-2}if(f.label){let N=h.append("g");await ce(N,f.label,{useHtmlLabels:!1,width:v,classes:"architecture-service-label"},ae()),N.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),N.attr("transform","translate("+(c+t+4)+", "+(l+t+2)+")")}}}))},"drawGroups"),Hr=at(async function(m,w,L){for(let u of L){let o=w.append("g"),n=Ut("iconSize");if(u.title){let e=o.append("g");await ce(e,u.title,{useHtmlLabels:!1,width:n*1.5,classes:"architecture-service-label"},ae()),e.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),e.attr("transform","translate("+n/2+", "+n+")")}let t=o.append("g");if(u.icon)t.html(`<g>${await fe(u.icon,{height:n,width:n,fallbackPrefix:he.prefix})}</g>`);else if(u.iconText){t.html(`<g>${await fe("blank",{height:n,width:n,fallbackPrefix:he.prefix})}</g>`);let i=t.append("g").append("foreignObject").attr("width",n).attr("height",n).append("div").attr("class","node-icon-text").attr("style",`height: ${n}px;`).append("div").html(u.iconText),r=parseInt(window.getComputedStyle(i.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;i.attr("style",`-webkit-line-clamp: ${Math.floor((n-2)/r)};`)}else t.append("path").attr("class","node-bkg").attr("id","node-"+u.id).attr("d",`M0 ${n} v${-n} q0,-5 5,-5 h${n} q5,0 5,5 v${n} H0 Z`);o.attr("class","architecture-service");let{width:a,height:f}=o._groups[0][0].getBBox();u.width=a,u.height=f,m.setElementForId(u.id,o)}return 0},"drawServices"),Wr=at(function(m,w,L){L.forEach(u=>{let o=w.append("g"),n=Ut("iconSize");o.append("g").append("rect").attr("id","node-"+u.id).attr("fill-opacity","0").attr("width",n).attr("height",n),o.attr("class","architecture-junction");let{width:a,height:f}=o._groups[0][0].getBBox();o.width=a,o.height=f,m.setElementForId(u.id,o)})},"drawJunctions");ze([{name:he.prefix,icons:he}]);Ne.use(rr.default);function ir(m,w){m.forEach(L=>{w.add({group:"nodes",data:{type:"service",id:L.id,icon:L.icon,label:L.title,parent:L.in,width:Ut("iconSize"),height:Ut("iconSize")},classes:"node-service"})})}at(ir,"addServices");function ar(m,w){m.forEach(L=>{w.add({group:"nodes",data:{type:"junction",id:L.id,parent:L.in,width:Ut("iconSize"),height:Ut("iconSize")},classes:"node-junction"})})}at(ar,"addJunctions");function nr(m,w){w.nodes().map(L=>{let u=ie(L);if(u.type==="group")return;u.x=L.position().x,u.y=L.position().y,m.getElementById(u.id).attr("transform","translate("+(u.x||0)+","+(u.y||0)+")")})}at(nr,"positionNodes");function or(m,w){m.forEach(L=>{w.add({group:"nodes",data:{type:"group",id:L.id,icon:L.icon,label:L.title,parent:L.in},classes:"node-group"})})}at(or,"addGroups");function sr(m,w){m.forEach(L=>{let{lhsId:u,rhsId:o,lhsInto:n,lhsGroup:t,rhsInto:a,lhsDir:f,rhsDir:e,rhsGroup:v,title:i}=L,r=_e(L.lhsDir,L.rhsDir)?"segments":"straight",h={id:`${u}-${o}`,label:i,source:u,sourceDir:f,sourceArrow:n,sourceGroup:t,sourceEndpoint:f==="L"?"0 50%":f==="R"?"100% 50%":f==="T"?"50% 0":"50% 100%",target:o,targetDir:e,targetArrow:a,targetGroup:v,targetEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%"};w.add({group:"edges",data:h,classes:r})})}at(sr,"addEdges");function hr(m){let w=m.map(o=>{let n={},t={};return Object.entries(o).forEach(([a,[f,e]])=>{n[e]||(n[e]=[]),t[f]||(t[f]=[]),n[e].push(a),t[f].push(a)}),{horiz:Object.values(n).filter(a=>a.length>1),vert:Object.values(t).filter(a=>a.length>1)}}),[L,u]=w.reduce(([o,n],{horiz:t,vert:a})=>[[...o,...t],[...n,...a]],[[],[]]);return{horizontal:L,vertical:u}}at(hr,"getAlignments");function lr(m){let w=[],L=at(o=>`${o[0]},${o[1]}`,"posToStr"),u=at(o=>o.split(",").map(n=>parseInt(n)),"strToPos");return m.forEach(o=>{let n=Object.fromEntries(Object.entries(o).map(([e,v])=>[L(v),e])),t=[L([0,0])],a={},f={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;t.length>0;){let e=t.shift();if(e){a[e]=1;let v=n[e];if(v){let i=u(e);Object.entries(f).forEach(([r,h])=>{let c=L([i[0]+h[0],i[1]+h[1]]),l=n[c];l&&!a[c]&&(t.push(c),w.push({[Qe[r]]:l,[Qe[gr(r)]]:v,gap:1.5*Ut("iconSize")}))})}}}}),w}at(lr,"getRelativeConstraints");function fr(m,w,L,u,{spatialMaps:o}){return new Promise(n=>{let t=Be("body").append("div").attr("id","cy").attr("style","display:none"),a=Ne({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${Ut("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${Ut("padding")}px`}}]});t.remove(),or(L,a),ir(m,a),ar(w,a),sr(u,a);let f=hr(o),e=lr(o),v=a.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(i){let[r,h]=i.connectedNodes(),{parent:c}=ie(r),{parent:l}=ie(h);return c===l?1.5*Ut("iconSize"):.5*Ut("iconSize")},edgeElasticity(i){let[r,h]=i.connectedNodes(),{parent:c}=ie(r),{parent:l}=ie(h);return c===l?.45:.001},alignmentConstraint:f,relativePlacementConstraint:e});v.one("layoutstop",()=>{function i(r,h,c,l){let N,p,{x:y,y:C}=r,{x:F,y:M}=h;p=(l-C+(y-c)*(C-M)/(y-F))/Math.sqrt(1+Math.pow((C-M)/(y-F),2)),N=Math.sqrt(Math.pow(l-C,2)+Math.pow(c-y,2)-Math.pow(p,2));let P=Math.sqrt(Math.pow(F-y,2)+Math.pow(M-C,2));N=N/P;let V=(F-y)*(l-C)-(M-C)*(c-y);switch(!0){case V>=0:V=1;break;case V<0:V=-1;break}let Y=(F-y)*(c-y)+(M-C)*(l-C);switch(!0){case Y>=0:Y=1;break;case Y<0:Y=-1;break}return p=Math.abs(p)*V,N=N*Y,{distances:p,weights:N}}at(i,"getSegmentWeights"),a.startBatch();for(let r of Object.values(a.edges()))if(r.data?.()){let{x:h,y:c}=r.source().position(),{x:l,y:N}=r.target().position();if(h!==l&&c!==N){let p=r.sourceEndpoint(),y=r.targetEndpoint(),{sourceDir:C}=tr(r),[F,M]=Jt(C)?[p.x,y.y]:[y.x,p.y],{weights:P,distances:V}=i(p,y,F,M);r.style("segment-distances",V),r.style("segment-weights",P)}}a.endBatch(),v.run()}),v.run(),a.ready(i=>{Te.info("Ready",i),n(a)})})}at(fr,"layoutArchitecture");var Vr=at(async(m,w,L,u)=>{let o=u.db,n=o.getServices(),t=o.getJunctions(),a=o.getGroups(),f=o.getEdges(),e=o.getDataStructures(),v=$e(w),i=v.append("g");i.attr("class","architecture-edges");let r=v.append("g");r.attr("class","architecture-services");let h=v.append("g");h.attr("class","architecture-groups"),await Hr(o,r,n),Wr(o,r,t);let c=await fr(n,t,a,f,e);await Yr(i,c),await Xr(h,c),nr(o,c),be(void 0,v,Ut("padding"),Ut("useMaxWidth"))},"draw"),zr={draw:Vr},ti={parser:Pr,db:ge,renderer:zr,styles:Ur};export{ti as diagram};
//# sourceMappingURL=architectureDiagram-UYN6MBPD-WBU2OYNU.min.js.map
