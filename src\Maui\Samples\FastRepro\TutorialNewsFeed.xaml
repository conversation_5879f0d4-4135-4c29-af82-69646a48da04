﻿<?xml version="1.0" encoding="utf-8"?>

<draw:DrawnUiBasePage
    x:Class="Sandbox.TutorialNewsFeed"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:sandbox="clr-namespace:Sandbox"
    xmlns:views="clr-namespace:Sandbox.Views"
    xmlns:viewModels="clr-namespace:Sandbox.ViewModels"
    x:DataType="viewModels:NewsViewModel"
    x:Name="ThisPage">

    <Grid HorizontalOptions="Fill" VerticalOptions="Fill">

        <draw:Canvas
            BackgroundColor="Gainsboro"
            Gestures="Enabled"
            HorizontalOptions="Fill"
            RenderingMode="Accelerated"
            Tag="Main"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Tag="Wrapper"
                VerticalOptions="Fill">

                <draw:SkiaScroll
                    x:Name="NewsScroll"
                    Orientation="Vertical"
                    FrictionScrolled="0.2"
                    ChangeVelocityScrolled="0.9"
                    RefreshCommand="{Binding RefreshCommand}"
                    LoadMoreCommand="{Binding LoadMoreCommand}"
                    RefreshEnabled="True"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaScroll.Header>

                        <draw:SkiaLayer HeightRequest="40" UseCache="Image">
                            <draw:SkiaRichLabel 
                                Text="DrawnUI News Feed Tutorial" 
                                HorizontalOptions="Center" VerticalOptions="Center" />
                        </draw:SkiaLayer>

                    </draw:SkiaScroll.Header>


                    <draw:SkiaScroll.Footer>

                        <draw:SkiaLayer HeightRequest="50" />

                    </draw:SkiaScroll.Footer>

                    <!-- Dynamic height cells using SkiaLayout with ItemTemplate -->
                    <draw:SkiaLayout
                        x:Name="NewsStack"
                        Type="Column"
                        ReserveTemplates="0"
                        ItemsSource="{Binding NewsItems}"
                        RecyclingTemplate="Enabled"
                        MeasureItemsStrategy="MeasureVisible"
                        Spacing="0"
                        HorizontalOptions="Fill">

                        <draw:SkiaLayout.ItemTemplate>
                            <DataTemplate>
                                <views:NewsCell />
                            </DataTemplate>
                        </draw:SkiaLayout.ItemTemplate>

                    </draw:SkiaLayout>

                </draw:SkiaScroll>

                <!-- uncomment for debug info -->

                <!--<draw:SkiaLabel
                    UseCache="Operations"
                    Margin="8"
                    Padding="2"
                    AddMarginBottom="50"
                    BackgroundColor="#CC000000"
                    HorizontalOptions="Start"
                    InputTransparent="True"
                    Text="{Binding Source={x:Reference NewsStack}, Path=DebugString}"
                    TextColor="LawnGreen"
                    VerticalOptions="End"
                    ZIndex="100" />-->

                <!-- uncomment for FPS info -->

                <!--<draw:SkiaLabelFps
                    Margin="0,0,4,24"
                    BackgroundColor="DarkRed"
                    HorizontalOptions="End"
                    Rotation="-45"
                    TextColor="White"
                    VerticalOptions="End"
                    ZIndex="100"/>-->

            </draw:SkiaLayout>

        </draw:Canvas>
    </Grid>

</draw:DrawnUiBasePage>