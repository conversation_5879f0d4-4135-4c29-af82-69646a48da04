{"items": [{"name": "Home", "href": "index.html", "topicHref": "index.html"}, {"name": "Getting Started", "items": [{"name": "Installation and Setup", "href": "getting-started.html", "topicHref": "getting-started.html"}, {"name": "Porting Native to Drawn", "href": "porting-maui.html", "topicHref": "porting-maui.html"}]}, {"name": "Tutorials", "href": "tutorials.html", "topicHref": "tutorials.html", "items": [{"name": "Tutorials Overview", "href": "tutorials.html", "topicHref": "tutorials.html"}, {"name": "Your First Drawn App (XAML)", "href": "first-app.html", "topicHref": "first-app.html"}, {"name": "Your First Drawn App (C# Fluent)", "href": "first-app-code.html", "topicHref": "first-app-code.html"}, {"name": "Interactive Cards", "href": "interactive-cards.html", "topicHref": "interactive-cards.html"}, {"name": "A Custom Drawn Control", "href": "interactive-button.html", "topicHref": "interactive-button.html"}, {"name": "News Feed <PERSON>er", "href": "news-feed-tutorial.html", "topicHref": "news-feed-tutorial.html"}]}, {"name": "Controls", "href": "controls/index.html", "topicHref": "controls/index.html", "items": [{"name": "Controls Overview", "href": "controls/index.html", "topicHref": "controls/index.html"}, {"name": "Buttons", "href": "controls/buttons.html", "topicHref": "controls/buttons.html"}, {"name": "Switches and Toggles", "href": "controls/switches.html", "topicHref": "controls/switches.html"}, {"name": "Input Controls", "href": "controls/input.html", "topicHref": "controls/input.html"}, {"name": "Layout Controls", "href": "controls/layouts.html", "topicHref": "controls/layouts.html"}, {"name": "Scroll Views", "href": "controls/scroll.html", "topicHref": "controls/scroll.html"}, {"name": "Carousels", "href": "controls/carousels.html", "topicHref": "controls/carousels.html"}, {"name": "Drawers", "href": "controls/drawers.html", "topicHref": "controls/drawers.html"}, {"name": "Native Integration", "href": "controls/native-integration.html", "topicHref": "controls/native-integration.html"}, {"name": "<PERSON><PERSON><PERSON>", "href": "controls/shapes.html", "topicHref": "controls/shapes.html"}, {"name": "Text and Labels", "href": "controls/text.html", "topicHref": "controls/text.html"}, {"name": "Images", "href": "controls/images.html", "topicHref": "controls/images.html"}, {"name": "Sprites", "href": "controls/sprites.html", "topicHref": "controls/sprites.html"}, {"name": "Animations", "href": "controls/animations.html", "topicHref": "controls/animations.html"}, {"name": "Navigation Shell", "href": "controls/shell.html", "topicHref": "controls/shell.html"}]}, {"name": "Advanced", "href": "advanced/index.html", "topicHref": "advanced/index.html", "items": [{"name": "Layout System Architecture", "href": "advanced/layout-system.html", "topicHref": "advanced/layout-system.html"}, {"name": "Gestures & Touch Input", "href": "advanced/gestures.html", "topicHref": "advanced/gestures.html"}, {"name": "Platform-Specific Styling", "href": "advanced/platform-styling.html", "topicHref": "advanced/platform-styling.html"}, {"name": "Gradients", "href": "advanced/gradients.html", "topicHref": "advanced/gradients.html"}, {"name": "Fluent C# Extensions", "href": "fluent-extensions.html", "topicHref": "fluent-extensions.html"}, {"name": "SkiaScroll & Virtualization", "href": "advanced/skiascroll.html", "topicHref": "advanced/skiascroll.html"}, {"name": "Theme Bindings", "href": "advanced/theme-binding.html", "topicHref": "advanced/theme-binding.html"}, {"name": "Drawing Pipeline", "href": "drawing-pipeline.html", "topicHref": "drawing-pipeline.html"}, {"name": "Game UI & Interactive Games", "href": "advanced/game-ui.html", "topicHref": "advanced/game-ui.html"}]}, {"name": "Sample Apps", "href": "sample-apps.html", "topicHref": "sample-apps.html"}, {"name": "FAQ", "href": "faq.html", "topicHref": "faq.html"}], "author": "<PERSON> aka <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (@taublast)", "description": "DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.", "keywords": ".NET MAUI, SkiaSharp, cross-platform UI, hardware acceleration, mobile development, iOS, Android, Windows, MacCatalyst, custom controls, animations, gestures, pixel-perfect, rendering engine", "og:description": "Create stunning cross-platform apps with DrawnUI - a powerful rendering engine for .NET MAUI built on SkiaSharp. Hardware-accelerated performance with pixel-perfect controls.", "og:image": "https://taublast.github.io/drawnui/images/logo.png", "og:title": "DrawnUI for .NET MAUI", "og:type": "website", "og:url": "https://taublast.github.io/drawnui", "twitter:card": "summary_large_image", "twitter:description": "Hardware-accelerated rendering engine for .NET MAUI built on SkiaSharp", "twitter:title": "DrawnUI for .NET MAUI"}