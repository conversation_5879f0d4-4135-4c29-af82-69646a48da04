{"version": 3, "sources": ["../../node_modules/ts-dedent/src/index.ts", "../../node_modules/marked/src/defaults.ts", "../../node_modules/marked/src/helpers.ts", "../../node_modules/marked/src/Tokenizer.ts", "../../node_modules/marked/src/rules.ts", "../../node_modules/marked/src/Lexer.ts", "../../node_modules/marked/src/Renderer.ts", "../../node_modules/marked/src/TextRenderer.ts", "../../node_modules/marked/src/Parser.ts", "../../node_modules/marked/src/Hooks.ts", "../../node_modules/marked/src/Instance.ts", "../../node_modules/marked/src/marked.ts", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-6JOS74DS.mjs"], "sourcesContent": ["export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n", "/**\n * Gets the original marked default options.\n */\nexport function _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nexport let _defaults = _getDefaults();\nexport function changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n", "/**\n * Helpers\n */\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nexport function escape(html, encode) {\n    if (encode) {\n        if (escapeTest.test(html)) {\n            return html.replace(escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (escapeTestNoEncode.test(html)) {\n            return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nconst unescapeTest = /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig;\nexport function unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nconst caret = /(^|[^\\[])\\^/g;\nexport function edit(regex, opt) {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    opt = opt || '';\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nexport function cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(/%25/g, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nexport const noopTest = { exec: () => null };\nexport function splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(/\\|/g, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(/ \\|/);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells[cells.length - 1].trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nexport function findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport { rtrim, splitCells, escape, findClosingBracket, } from './helpers.ts';\nfunction outputLink(cap, link, raw, lexer) {\n    const href = link.href;\n    const title = link.title ? escape(link.title) : null;\n    const text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text: escape(text),\n    };\n}\nfunction indentCodeCompensation(raw, text) {\n    const matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(/^\\s+/);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(/^ {1,4}/gm, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '');\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (/#$/.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || / $/.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (/^ {0,3}>/.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(/\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g, '\\n    $1')\n                    .replace(/^ {0,3}>[ \\t]?/gm, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens[tokens.length - 1];\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens[tokens.length - 1].raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(/[^ ]/); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && /^ *$/.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n                    const hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n                    const fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n                    const headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(src)) {\n                            break;\n                        }\n                        if (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLine.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.search(/[^ ]/) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLine.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (/\\n *\\n *$/.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = /^\\[[ xX]\\] /.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            list.items[list.items.length - 1].raw = list.items[list.items.length - 1].raw.trimEnd();\n            list.items[list.items.length - 1].text = list.items[list.items.length - 1].text.trimEnd();\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n            const href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!/[:|]/.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(/^\\||\\| *$/g, '').split('|');\n        const rows = cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (/^ *-+: *$/.test(align)) {\n                item.align.push('right');\n            }\n            else if (/^ *:-+: *$/.test(align)) {\n                item.align.push('center');\n            }\n            else if (/^ *:-+ *$/.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: escape(cap[1]),\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && /^</.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(/>$/.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (/^</.test(href)) {\n                if (this.options.pedantic && !(/>$/.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(/\\n/g, ' ');\n            const hasNonSpaceChars = /[^ ]/.test(text);\n            const hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            text = escape(text, true);\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape(cap[1]);\n                href = 'mailto:' + text;\n            }\n            else {\n                text = escape(cap[1]);\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape(cap[0]);\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = escape(cap[0]);\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            let text;\n            if (this.lexer.state.inRawBlock) {\n                text = cap[0];\n            }\n            else {\n                text = escape(cap[0]);\n            }\n            return {\n                type: 'text',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n}\n", "import { edit, noopTest, } from './helpers.ts';\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?: *(?:\\n|$))+/;\nconst blockCode = /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheading = edit(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, / {4}/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', ' {4}[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = '\\\\p{P}\\\\p{S}';\nconst punctuation = edit(/^((?![*_])[\\spunctuation])/, 'u')\n    .replace(/punctuation/g, _punctuation).getRegex();\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\([^\\(\\)]*?\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelim = edit(/^(?:\\*+(?:((?!\\*)[punct])|[^\\s*]))|^_+(?:((?!_)[punct])|([^\\s_]))/, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAst = edit('^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)[punct](\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?!\\\\*)(?=[punct\\\\s]|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)[punct\\\\s](\\\\*+)(?=[^punct\\\\s])' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=[punct])' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)[punct](\\\\*+)(?!\\\\*)(?=[punct])' // (5) #***# can be either Left or Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?=[^punct\\\\s])', 'gu') // (6) a***a can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)[punct](_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|[^punct\\\\s](_+)(?!_)(?=[punct\\\\s]|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)[punct\\\\s](_+)(?=[^punct\\\\s])' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=[punct])' // (4) ___# can only be Left Delimiter\n    + '|(?!_)[punct](_+)(?!_)(?=[punct])', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\([punct])/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    escape: edit(escape).replace('])', '~|])').getRegex(),\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nexport const block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nexport const inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { block, inline } from './rules.ts';\n/**\n * Block Lexer\n */\nexport class _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src\n            .replace(/\\r\\n|\\r/g, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n        }\n        else {\n            src = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n                return leading + '    '.repeat(tabs.length);\n            });\n        }\n        let token;\n        let lastToken;\n        let cutSrc;\n        while (src) {\n            if (this.options.extensions\n                && this.options.extensions.block\n                && this.options.extensions.block.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.length === 1 && tokens.length > 0) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    tokens[tokens.length - 1].raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                lastToken = tokens[tokens.length - 1];\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = (cutSrc.length !== src.length);\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        let token, lastToken, cutSrc;\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match;\n        let keepPrevChar, prevChar;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            // extensions\n            if (this.options.extensions\n                && this.options.extensions.inline\n                && this.options.extensions.inline.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { cleanUrl, escape, } from './helpers.ts';\n/**\n * Renderer\n */\nexport class _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(/^\\S*/)?.[0];\n        const code = text.replace(/\\n$/, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${text}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + title + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${title}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens ? this.parser.parseInline(token.tokens) : token.text;\n    }\n}\n", "/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer) {\n        renderer = renderer || this.renderer;\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nexport class _Hooks {\n    options;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape, unescape, } from './helpers.ts';\nexport class Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.#parseMarkdown(_Lexer.lex, _Parser.parse);\n    parseInline = this.#parseMarkdown(_Lexer.lexInline, _Parser.parseInline);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    let rendererFunc = pack.renderer[rendererProp];\n                    if (!pack.useNewRenderer) {\n                        // TODO: Remove this in next major version\n                        rendererFunc = this.#convertRendererFunction(rendererFunc, rendererProp, renderer);\n                    }\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (prop === 'options') {\n                        // ignore options property\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    // TODO: Remove this in next major release\n    #convertRendererFunction(func, prop, renderer) {\n        switch (prop) {\n            case 'heading':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, renderer.parser.parseInline(token.tokens), token.depth, unescape(renderer.parser.parseInline(token.tokens, renderer.parser.textRenderer)));\n                };\n            case 'code':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.text, token.lang, !!token.escaped);\n                };\n            case 'table':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    let header = '';\n                    // header\n                    let cell = '';\n                    for (let j = 0; j < token.header.length; j++) {\n                        cell += this.tablecell({\n                            text: token.header[j].text,\n                            tokens: token.header[j].tokens,\n                            header: true,\n                            align: token.align[j],\n                        });\n                    }\n                    header += this.tablerow({ text: cell });\n                    let body = '';\n                    for (let j = 0; j < token.rows.length; j++) {\n                        const row = token.rows[j];\n                        cell = '';\n                        for (let k = 0; k < row.length; k++) {\n                            cell += this.tablecell({\n                                text: row[k].text,\n                                tokens: row[k].tokens,\n                                header: false,\n                                align: token.align[k],\n                            });\n                        }\n                        body += this.tablerow({ text: cell });\n                    }\n                    return func.call(this, header, body);\n                };\n            case 'blockquote':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    const body = this.parser.parse(token.tokens);\n                    return func.call(this, body);\n                };\n            case 'list':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    const ordered = token.ordered;\n                    const start = token.start;\n                    const loose = token.loose;\n                    let body = '';\n                    for (let j = 0; j < token.items.length; j++) {\n                        const item = token.items[j];\n                        const checked = item.checked;\n                        const task = item.task;\n                        let itemBody = '';\n                        if (item.task) {\n                            const checkbox = this.checkbox({ checked: !!checked });\n                            if (loose) {\n                                if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n                                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                                        item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n                                    }\n                                }\n                                else {\n                                    item.tokens.unshift({\n                                        type: 'text',\n                                        text: checkbox + ' ',\n                                    });\n                                }\n                            }\n                            else {\n                                itemBody += checkbox + ' ';\n                            }\n                        }\n                        itemBody += this.parser.parse(item.tokens, loose);\n                        body += this.listitem({\n                            type: 'list_item',\n                            raw: itemBody,\n                            text: itemBody,\n                            task,\n                            checked: !!checked,\n                            loose,\n                            tokens: item.tokens,\n                        });\n                    }\n                    return func.call(this, body, ordered, start);\n                };\n            case 'html':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.text, token.block);\n                };\n            case 'paragraph':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, this.parser.parseInline(token.tokens));\n                };\n            case 'escape':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.text);\n                };\n            case 'link':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.href, token.title, this.parser.parseInline(token.tokens));\n                };\n            case 'image':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.href, token.title, token.text);\n                };\n            case 'strong':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, this.parser.parseInline(token.tokens));\n                };\n            case 'em':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, this.parser.parseInline(token.tokens));\n                };\n            case 'codespan':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.text);\n                };\n            case 'del':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, this.parser.parseInline(token.tokens));\n                };\n            case 'text':\n                return function (token) {\n                    if (!token.type || token.type !== prop) {\n                        // @ts-ignore\n                        // eslint-disable-next-line prefer-rest-params\n                        return func.apply(this, arguments);\n                    }\n                    return func.call(this, token.text);\n                };\n            default:\n            // do nothing\n        }\n        return func;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    #parseMarkdown(lexer, parser) {\n        return (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            // Show warning if an extension set async to true but the parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                if (!opt.silent) {\n                    console.warn('marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored.');\n                }\n                opt.async = true;\n            }\n            const throwError = this.#onError(!!opt.silent, !!opt.async);\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n            }\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n    }\n    #onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport { _getDefaults, changeDefaults, _defaults, } from './defaults.ts';\nconst markedInstance = new Marked();\nexport function marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\n", "import {\n  decodeEntities\n} from \"./chunk-7DKRZKHE.mjs\";\nimport {\n  __name,\n  common_default,\n  getConfig2 as getConfig,\n  hasKatex,\n  log,\n  renderKatex\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/rendering-util/createText.ts\nimport { select } from \"d3\";\n\n// src/rendering-util/handle-markdown-text.ts\nimport { marked } from \"marked\";\nimport { dedent } from \"ts-dedent\";\nfunction preprocessMarkdown(markdown, { markdownAutoWrap }) {\n  const withoutBR = markdown.replace(/<br\\/>/g, \"\\n\");\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, \"\\n\");\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, \"&nbsp;\");\n  }\n  return withoutExtraSpaces;\n}\n__name(preprocessMarkdown, \"preprocessMarkdown\");\nfunction markdownToLines(markdown, config = {}) {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines = [[]];\n  let currentLine = 0;\n  function processNode(node, parentType = \"normal\") {\n    if (node.type === \"text\") {\n      const textLines = node.text.split(\"\\n\");\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(\" \").forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === \"strong\" || node.type === \"em\") {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode, node.type);\n      });\n    } else if (node.type === \"html\") {\n      lines[currentLine].push({ content: node.text, type: \"normal\" });\n    }\n  }\n  __name(processNode, \"processNode\");\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === \"paragraph\") {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode);\n      });\n    } else if (treeNode.type === \"html\") {\n      lines[currentLine].push({ content: treeNode.text, type: \"normal\" });\n    }\n  });\n  return lines;\n}\n__name(markdownToLines, \"markdownToLines\");\nfunction markdownToHTML(markdown, { markdownAutoWrap } = {}) {\n  const nodes = marked.lexer(markdown);\n  function output(node) {\n    if (node.type === \"text\") {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, \"<br/>\").replace(/ /g, \"&nbsp;\");\n      }\n      return node.text.replace(/\\n */g, \"<br/>\");\n    } else if (node.type === \"strong\") {\n      return `<strong>${node.tokens?.map(output).join(\"\")}</strong>`;\n    } else if (node.type === \"em\") {\n      return `<em>${node.tokens?.map(output).join(\"\")}</em>`;\n    } else if (node.type === \"paragraph\") {\n      return `<p>${node.tokens?.map(output).join(\"\")}</p>`;\n    } else if (node.type === \"space\") {\n      return \"\";\n    } else if (node.type === \"html\") {\n      return `${node.text}`;\n    } else if (node.type === \"escape\") {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n  __name(output, \"output\");\n  return nodes.map(output).join(\"\");\n}\n__name(markdownToHTML, \"markdownToHTML\");\n\n// src/rendering-util/splitText.ts\nfunction splitTextToChars(text) {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n__name(splitTextToChars, \"splitTextToChars\");\nfunction splitWordToFitWidth(checkFit, word) {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n__name(splitWordToFitWidth, \"splitWordToFitWidth\");\nfunction splitWordToFitWidthRecursion(checkFit, usedChars, remainingChars, type) {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(\"\"), type },\n      { content: \"\", type }\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(\"\"), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(\"\"), type },\n    { content: remainingChars.join(\"\"), type }\n  ];\n}\n__name(splitWordToFitWidthRecursion, \"splitWordToFitWidthRecursion\");\nfunction splitLineToFitWidth(line, checkFit) {\n  if (line.some(({ content }) => content.includes(\"\\n\"))) {\n    throw new Error(\"splitLineToFitWidth does not support newlines in the line\");\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n__name(splitLineToFitWidth, \"splitLineToFitWidth\");\nfunction splitLineToFitWidthRecursion(words, checkFit, lines = [], newLine = []) {\n  if (words.length === 0) {\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = \"\";\n  if (words[0].content === \" \") {\n    joiner = \" \";\n    words.shift();\n  }\n  const nextWord = words.shift() ?? { content: \" \", type: \"normal\" };\n  const lineWithNextWord = [...newLine];\n  if (joiner !== \"\") {\n    lineWithNextWord.push({ content: joiner, type: \"normal\" });\n  }\n  lineWithNextWord.push(nextWord);\n  if (checkFit(lineWithNextWord)) {\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n  if (newLine.length > 0) {\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n__name(splitLineToFitWidthRecursion, \"splitLineToFitWidthRecursion\");\n\n// src/rendering-util/createText.ts\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append(\"foreignObject\");\n  fo.attr(\"width\", `${10 * width}px`);\n  fo.attr(\"height\", `${10 * width}px`);\n  const div = fo.append(\"xhtml:div\");\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common_default.lineBreakRegex, \"\\n\"), getConfig());\n  }\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  const span = div.append(\"span\");\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr(\"class\", `${labelClass} ${classes}`);\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"table-cell\");\n  div.style(\"white-space\", \"nowrap\");\n  div.style(\"line-height\", \"1.5\");\n  div.style(\"max-width\", width + \"px\");\n  div.style(\"text-align\", \"center\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  if (addBackground) {\n    div.attr(\"class\", \"labelBkg\");\n  }\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style(\"display\", \"table\");\n    div.style(\"white-space\", \"break-spaces\");\n    div.style(\"width\", width + \"px\");\n    bbox = div.node().getBoundingClientRect();\n  }\n  return fo.node();\n}\n__name(addHtmlSpan, \"addHtmlSpan\");\nfunction createTspan(textElement, lineIndex, lineHeight) {\n  return textElement.append(\"tspan\").attr(\"class\", \"text-outer-tspan\").attr(\"x\", 0).attr(\"y\", lineIndex * lineHeight - 0.1 + \"em\").attr(\"dy\", lineHeight + \"em\");\n}\n__name(createTspan, \"createTspan\");\nfunction computeWidthOfText(parentNode, lineHeight, line) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n__name(computeWidthOfText, \"computeWidthOfText\");\nfunction computeDimensionOfText(parentNode, lineHeight, text) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: \"normal\" }]);\n  const textDimension = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n__name(computeDimensionOfText, \"computeDimensionOfText\");\nfunction createFormattedText(width, g, structuredText, addBackground = false) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append(\"g\");\n  const bkg = labelGroup.insert(\"rect\").attr(\"class\", \"background\").attr(\"style\", \"stroke: none\");\n  const textElement = labelGroup.append(\"text\").attr(\"y\", \"-10.1\");\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    const checkWidth = /* @__PURE__ */ __name((line2) => computeWidthOfText(labelGroup, lineHeight, line2) <= width, \"checkWidth\");\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg.attr(\"x\", bbox.x - padding).attr(\"y\", bbox.y - padding).attr(\"width\", bbox.width + 2 * padding).attr(\"height\", bbox.height + 2 * padding);\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n__name(createFormattedText, \"createFormattedText\");\nfunction updateTextContentAndStyles(tspan, wrappedLine) {\n  tspan.text(\"\");\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan.append(\"tspan\").attr(\"font-style\", word.type === \"em\" ? \"italic\" : \"normal\").attr(\"class\", \"text-inner-tspan\").attr(\"font-weight\", word.type === \"strong\" ? \"bold\" : \"normal\");\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      innerTspan.text(\" \" + word.content);\n    }\n  });\n}\n__name(updateTextContentAndStyles, \"updateTextContentAndStyles\");\nfunction replaceIconSubstring(text) {\n  return text.replace(\n    /fa[bklrs]?:fa-[\\w-]+/g,\n    // cspell: disable-line\n    (s) => `<i class='${s.replace(\":\", \" \")}'></i>`\n  );\n}\n__name(replaceIconSubstring, \"replaceIconSubstring\");\nvar createText = /* @__PURE__ */ __name(async (el, text = \"\", {\n  style = \"\",\n  isTitle = false,\n  classes = \"\",\n  useHtmlLabels = true,\n  isNode = true,\n  width = 200,\n  addSvgBackground = false\n} = {}, config) => {\n  log.debug(\n    \"XYZ createText\",\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    \"addSvgBackground: \",\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = replaceIconSubstring(decodeEntities(htmlText));\n    const inputForKatex = text.replace(/\\\\\\\\/g, \"\\\\\");\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace(\"fill:\", \"color:\")\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, \"<br/>\");\n    const structuredText = markdownToLines(sanitizeBR.replace(\"<br>\", \"<br/>\"), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace(\"stroke:\", \"lineColor:\");\n      }\n      const nodeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).attr(\"style\", nodeLabelTextStyle);\n    } else {\n      const edgeLabelRectStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/background:/g, \"fill:\");\n      select(svgLabel).select(\"rect\").attr(\"style\", edgeLabelRectStyle.replace(/background:/g, \"fill:\"));\n      const edgeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).select(\"text\").attr(\"style\", edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n}, \"createText\");\n\nexport {\n  computeDimensionOfText,\n  replaceIconSubstring,\n  createText\n};\n"], "mappings": "wIAAM,SAAUA,GACdC,EAAoC,SACpCC,EAAA,CAAA,EAAAC,EAAA,EAAAA,EAAA,UAAA,OAAAA,IAAAD,EAAAC,EAAA,CAAA,EAAA,UAAAA,CAAA,EAEA,IAAIC,EAAU,MAAM,KAAK,OAAOH,GAAU,SAAW,CAACA,CAAK,EAAIA,CAAK,EAGpEG,EAAQA,EAAQ,OAAS,CAAC,EAAIA,EAAQA,EAAQ,OAAS,CAAC,EAAE,QACxD,iBACA,EAAE,EAIJ,IAAMC,EAAgBD,EAAQ,OAAO,SAACE,EAAKC,EAAG,CAC5C,IAAMC,EAAUD,EAAI,MAAM,qBAAqB,EAC/C,OAAIC,EACKF,EAAI,OACTE,EAAQ,IAAI,SAACC,EAAK,CAAA,IAAAC,EAAAC,EAAK,OAAAA,GAAAD,EAAAD,EAAM,MAAM,QAAQ,KAAC,MAAAC,IAAA,OAAA,OAAAA,EAAE,UAAM,MAAAC,IAAA,OAAAA,EAAI,CAAC,CAAA,CAAC,EAGvDL,CACT,EAAa,CAAA,CAAE,EAGf,GAAID,EAAc,OAAQ,CACxB,IAAMO,EAAU,IAAI,OAAO;OAAW,KAAK,IAAG,MAAR,KAAYP,CAAa,EAAA,IAAM,GAAG,EAExED,EAAUA,EAAQ,IAAI,SAACG,EAAG,CAAK,OAAAA,EAAI,QAAQK,EAAS;CAAI,CAAzB,CAA0B,EAI3DR,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAE,QAAQ,SAAU,EAAE,EAG5C,IAAIS,EAAST,EAAQ,CAAC,EAEtB,OAAAF,EAAO,QAAQ,SAACY,EAAOC,EAAC,CAEtB,IAAMC,EAAeH,EAAO,MAAM,eAAe,EAC3CI,EAAcD,EAAeA,EAAa,CAAC,EAAI,GACjDE,EAAgBJ,EAEhB,OAAOA,GAAU,UAAYA,EAAM,SAAS;CAAI,IAClDI,EAAgB,OAAOJ,CAAK,EACzB,MAAM;CAAI,EACV,IAAI,SAACP,EAAKQ,EAAC,CACV,OAAOA,IAAM,EAAIR,EAAM,GAAGU,EAAcV,CAC1C,CAAC,EACA,KAAK;CAAI,GAGdM,GAAUK,EAAgBd,EAAQW,EAAI,CAAC,CACzC,CAAC,EAEMF,CACT,CCpDO,SAASM,GAAe,CAC3B,MAAO,CACH,MAAO,GACP,OAAQ,GACR,WAAY,KACZ,IAAK,GACL,MAAO,KACP,SAAU,GACV,SAAU,KACV,OAAQ,GACR,UAAW,KACX,WAAY,IACpB,CACA,CACU,IAACC,EAAYD,EAAY,EAC5B,SAASE,GAAeC,EAAa,CACxCF,EAAYE,CAChB,CCjBA,IAAMC,GAAa,UACbC,GAAgB,IAAI,OAAOD,GAAW,OAAQ,GAAG,EACjDE,GAAqB,oDACrBC,GAAwB,IAAI,OAAOD,GAAmB,OAAQ,GAAG,EACjEE,GAAqB,CACvB,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACT,EACMC,GAAwBC,GAAOF,GAAmBE,CAAE,EACnD,SAASC,EAAOC,EAAMC,EAAQ,CACjC,GAAIA,GACA,GAAIT,GAAW,KAAKQ,CAAI,EACpB,OAAOA,EAAK,QAAQP,GAAeI,EAAoB,UAIvDH,GAAmB,KAAKM,CAAI,EAC5B,OAAOA,EAAK,QAAQL,GAAuBE,EAAoB,EAGvE,OAAOG,CACX,CACA,IAAME,GAAe,6CACd,SAASC,GAASH,EAAM,CAE3B,OAAOA,EAAK,QAAQE,GAAc,CAACE,EAAGC,KAClCA,EAAIA,EAAE,YAAW,EACbA,IAAM,QACC,IACPA,EAAE,OAAO,CAAC,IAAM,IACTA,EAAE,OAAO,CAAC,IAAM,IACjB,OAAO,aAAa,SAASA,EAAE,UAAU,CAAC,EAAG,EAAE,CAAC,EAChD,OAAO,aAAa,CAACA,EAAE,UAAU,CAAC,CAAC,EAEtC,GACV,CACL,CACA,IAAMC,GAAQ,eACP,SAASC,EAAKC,EAAOC,EAAK,CAC7B,IAAIC,EAAS,OAAOF,GAAU,SAAWA,EAAQA,EAAM,OACvDC,EAAMA,GAAO,GACb,IAAME,EAAM,CACR,QAAS,CAACC,EAAMC,IAAQ,CACpB,IAAIC,EAAY,OAAOD,GAAQ,SAAWA,EAAMA,EAAI,OACpD,OAAAC,EAAYA,EAAU,QAAQR,GAAO,IAAI,EACzCI,EAASA,EAAO,QAAQE,EAAME,CAAS,EAChCH,CACnB,EACQ,SAAU,IACC,IAAI,OAAOD,EAAQD,CAAG,CAEzC,EACI,OAAOE,CACX,CACO,SAASI,GAASC,EAAM,CAC3B,GAAI,CACAA,EAAO,UAAUA,CAAI,EAAE,QAAQ,OAAQ,GAAG,CAClD,MACU,CACF,OAAO,IACf,CACI,OAAOA,CACX,CACO,IAAMC,EAAW,CAAE,KAAM,IAAM,IAAI,EACnC,SAASC,GAAWC,EAAUC,EAAO,CAGxC,IAAMC,EAAMF,EAAS,QAAQ,MAAO,CAACG,EAAOC,EAAQC,IAAQ,CACxD,IAAIC,EAAU,GACVC,EAAOH,EACX,KAAO,EAAEG,GAAQ,GAAKF,EAAIE,CAAI,IAAM,MAChCD,EAAU,CAACA,EACf,OAAIA,EAGO,IAIA,IAEnB,CAAK,EAAGE,EAAQN,EAAI,MAAM,KAAK,EACvBO,EAAI,EAQR,GANKD,EAAM,CAAC,EAAE,KAAI,GACdA,EAAM,MAAK,EAEXA,EAAM,OAAS,GAAK,CAACA,EAAMA,EAAM,OAAS,CAAC,EAAE,KAAI,GACjDA,EAAM,IAAG,EAETP,EACA,GAAIO,EAAM,OAASP,EACfO,EAAM,OAAOP,CAAK,MAGlB,MAAOO,EAAM,OAASP,GAClBO,EAAM,KAAK,EAAE,EAGzB,KAAOC,EAAID,EAAM,OAAQC,IAErBD,EAAMC,CAAC,EAAID,EAAMC,CAAC,EAAE,KAAI,EAAG,QAAQ,QAAS,GAAG,EAEnD,OAAOD,CACX,CASO,SAASE,EAAML,EAAKM,EAAGC,EAAQ,CAClC,IAAMC,EAAIR,EAAI,OACd,GAAIQ,IAAM,EACN,MAAO,GAGX,IAAIC,EAAU,EAEd,KAAOA,EAAUD,GAAG,CAChB,IAAME,EAAWV,EAAI,OAAOQ,EAAIC,EAAU,CAAC,EAC3C,GAAIC,IAAaJ,GAAK,CAACC,EACnBE,YAEKC,IAAaJ,GAAKC,EACvBE,QAGA,MAEZ,CACI,OAAOT,EAAI,MAAM,EAAGQ,EAAIC,CAAO,CACnC,CACO,SAASE,GAAmBX,EAAKY,EAAG,CACvC,GAAIZ,EAAI,QAAQY,EAAE,CAAC,CAAC,IAAM,GACtB,MAAO,GAEX,IAAIC,EAAQ,EACZ,QAAST,EAAI,EAAGA,EAAIJ,EAAI,OAAQI,IAC5B,GAAIJ,EAAII,CAAC,IAAM,KACXA,YAEKJ,EAAII,CAAC,IAAMQ,EAAE,CAAC,EACnBC,YAEKb,EAAII,CAAC,IAAMQ,EAAE,CAAC,IACnBC,IACIA,EAAQ,GACR,OAAOT,EAInB,MAAO,EACX,CC/JA,SAASU,GAAWC,EAAKC,EAAMC,EAAKC,EAAO,CACvC,IAAM1B,EAAOwB,EAAK,KACZG,EAAQH,EAAK,MAAQzC,EAAOyC,EAAK,KAAK,EAAI,KAC1CI,EAAOL,EAAI,CAAC,EAAE,QAAQ,cAAe,IAAI,EAC/C,GAAIA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAK,CAC1BG,EAAM,MAAM,OAAS,GACrB,IAAMG,EAAQ,CACV,KAAM,OACN,IAAAJ,EACA,KAAAzB,EACA,MAAA2B,EACA,KAAAC,EACA,OAAQF,EAAM,aAAaE,CAAI,CAC3C,EACQ,OAAAF,EAAM,MAAM,OAAS,GACdG,CACf,CACI,MAAO,CACH,KAAM,QACN,IAAAJ,EACA,KAAAzB,EACA,MAAA2B,EACA,KAAM5C,EAAO6C,CAAI,CACzB,CACA,CACA,SAASE,GAAuBL,EAAKG,EAAM,CACvC,IAAMG,EAAoBN,EAAI,MAAM,eAAe,EACnD,GAAIM,IAAsB,KACtB,OAAOH,EAEX,IAAMI,EAAeD,EAAkB,CAAC,EACxC,OAAOH,EACF,MAAM;CAAI,EACV,IAAIK,GAAQ,CACb,IAAMC,EAAoBD,EAAK,MAAM,MAAM,EAC3C,GAAIC,IAAsB,KACtB,OAAOD,EAEX,GAAM,CAACE,CAAY,EAAID,EACvB,OAAIC,EAAa,QAAUH,EAAa,OAC7BC,EAAK,MAAMD,EAAa,MAAM,EAElCC,CACf,CAAK,EACI,KAAK;CAAI,CAClB,CAIO,IAAMG,EAAN,KAAiB,CACpB,QACA,MACA,MACA,YAAYC,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAClC,CACI,MAAMiE,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,GAAOA,EAAI,CAAC,EAAE,OAAS,EACvB,MAAO,CACH,KAAM,QACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,QAAQ,YAAa,EAAE,EAC3C,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,eAAgB,WAChB,KAAO,KAAK,QAAQ,SAEdK,EADAf,EAAMe,EAAM;CAAI,CAEtC,CACA,CACA,CACI,OAAOU,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,MAAM,OAAO,KAAKe,CAAG,EAC5C,GAAIf,EAAK,CACL,IAAME,EAAMF,EAAI,CAAC,EACXK,EAAOE,GAAuBL,EAAKF,EAAI,CAAC,GAAK,EAAE,EACrD,MAAO,CACH,KAAM,OACN,IAAAE,EACA,KAAMF,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,KAAI,EAAG,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACpF,KAAAK,CAChB,CACA,CACA,CACI,QAAQU,EAAK,CACT,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,KAAI,EAEtB,GAAI,KAAK,KAAKK,CAAI,EAAG,CACjB,IAAMW,EAAU1B,EAAMe,EAAM,GAAG,GAC3B,KAAK,QAAQ,UAGR,CAACW,GAAW,KAAK,KAAKA,CAAO,KAElCX,EAAOW,EAAQ,KAAI,EAEvC,CACY,MAAO,CACH,KAAM,UACN,IAAKhB,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OACd,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,MAAM,GAAG,KAAKe,CAAG,EACxC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKV,EAAMU,EAAI,CAAC,EAAG;CAAI,CACvC,CAEA,CACI,WAAWe,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,MAAM,WAAW,KAAKe,CAAG,EAChD,GAAIf,EAAK,CACL,IAAIiB,EAAQ3B,EAAMU,EAAI,CAAC,EAAG;CAAI,EAAE,MAAM;CAAI,EACtCE,EAAM,GACNG,EAAO,GACLa,EAAS,CAAA,EACf,KAAOD,EAAM,OAAS,GAAG,CACrB,IAAIE,EAAe,GACbC,EAAe,CAAA,EACjB/B,EACJ,IAAKA,EAAI,EAAGA,EAAI4B,EAAM,OAAQ5B,IAE1B,GAAI,WAAW,KAAK4B,EAAM5B,CAAC,CAAC,EACxB+B,EAAa,KAAKH,EAAM5B,CAAC,CAAC,EAC1B8B,EAAe,WAEV,CAACA,EACNC,EAAa,KAAKH,EAAM5B,CAAC,CAAC,MAG1B,OAGR4B,EAAQA,EAAM,MAAM5B,CAAC,EACrB,IAAMgC,EAAaD,EAAa,KAAK;CAAI,EACnCE,EAAcD,EAEf,QAAQ,iCAAkC;OAAU,EACpD,QAAQ,mBAAoB,EAAE,EACnCnB,EAAMA,EAAM,GAAGA,CAAG;EAAKmB,CAAU,GAAKA,EACtChB,EAAOA,EAAO,GAAGA,CAAI;EAAKiB,CAAW,GAAKA,EAG1C,IAAMC,EAAM,KAAK,MAAM,MAAM,IAK7B,GAJA,KAAK,MAAM,MAAM,IAAM,GACvB,KAAK,MAAM,YAAYD,EAAaJ,EAAQ,EAAI,EAChD,KAAK,MAAM,MAAM,IAAMK,EAEnBN,EAAM,SAAW,EACjB,MAEJ,IAAMO,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAC1C,GAAIM,GAAW,OAAS,OAEpB,MAEC,GAAIA,GAAW,OAAS,aAAc,CAEvC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,WAAWD,CAAO,EACxCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5BzB,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASuB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACpEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,KAAK,MAAM,EAAIE,EAAS,KACxE,KACpB,SACyBH,GAAW,OAAS,OAAQ,CAEjC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,KAAKD,CAAO,EAClCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5BzB,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASsB,EAAU,IAAI,MAAM,EAAIG,EAAS,IACrEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACvEV,EAAQS,EAAQ,UAAUR,EAAOA,EAAO,OAAS,CAAC,EAAE,IAAI,MAAM,EAAE,MAAM;CAAI,EAC1E,QACpB,CACA,CACY,MAAO,CACH,KAAM,aACN,IAAAhB,EACA,OAAAgB,EACA,KAAAb,CAChB,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAIf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EACxC,GAAIf,EAAK,CACL,IAAI4B,EAAO5B,EAAI,CAAC,EAAE,KAAI,EAChB6B,EAAYD,EAAK,OAAS,EAC1BE,EAAO,CACT,KAAM,OACN,IAAK,GACL,QAASD,EACT,MAAOA,EAAY,CAACD,EAAK,MAAM,EAAG,EAAE,EAAI,GACxC,MAAO,GACP,MAAO,CAAA,CACvB,EACYA,EAAOC,EAAY,aAAaD,EAAK,MAAM,EAAE,CAAC,GAAK,KAAKA,CAAI,GACxD,KAAK,QAAQ,WACbA,EAAOC,EAAYD,EAAO,SAG9B,IAAMG,EAAY,IAAI,OAAO,WAAWH,CAAI,8BAA+B,EACvEI,EAAoB,GAExB,KAAOjB,GAAK,CACR,IAAIkB,EAAW,GACX/B,EAAM,GACNgC,EAAe,GAInB,GAHI,EAAElC,EAAM+B,EAAU,KAAKhB,CAAG,IAG1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC5B,MAEJb,EAAMF,EAAI,CAAC,EACXe,EAAMA,EAAI,UAAUb,EAAI,MAAM,EAC9B,IAAIiC,EAAOnC,EAAI,CAAC,EAAE,MAAM;EAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,OAASoC,GAAM,IAAI,OAAO,EAAIA,EAAE,MAAM,CAAC,EAC/EC,EAAWtB,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAC/BuB,EAAY,CAACH,EAAK,KAAI,EACtBI,EAAS,EAmBb,GAlBI,KAAK,QAAQ,UACbA,EAAS,EACTL,EAAeC,EAAK,UAAS,GAExBG,EACLC,EAASvC,EAAI,CAAC,EAAE,OAAS,GAGzBuC,EAASvC,EAAI,CAAC,EAAE,OAAO,MAAM,EAC7BuC,EAASA,EAAS,EAAI,EAAIA,EAC1BL,EAAeC,EAAK,MAAMI,CAAM,EAChCA,GAAUvC,EAAI,CAAC,EAAE,QAEjBsC,GAAa,OAAO,KAAKD,CAAQ,IACjCnC,GAAOmC,EAAW;EAClBtB,EAAMA,EAAI,UAAUsB,EAAS,OAAS,CAAC,EACvCJ,EAAW,IAEX,CAACA,EAAU,CACX,IAAMO,EAAkB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGD,EAAS,CAAC,CAAC,oDAAqD,EACjHE,GAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGF,EAAS,CAAC,CAAC,oDAAoD,EACxGG,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGH,EAAS,CAAC,CAAC,iBAAiB,EAC9EI,GAAoB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGJ,EAAS,CAAC,CAAC,IAAI,EAExE,KAAOxB,GAAK,CACR,IAAM6B,EAAU7B,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAmBpC,GAlBAsB,EAAWO,EAEP,KAAK,QAAQ,WACbP,EAAWA,EAAS,QAAQ,0BAA2B,IAAI,GAG3DK,GAAiB,KAAKL,CAAQ,GAI9BM,GAAkB,KAAKN,CAAQ,GAI/BG,EAAgB,KAAKH,CAAQ,GAI7BI,GAAQ,KAAK1B,CAAG,EAChB,MAEJ,GAAIsB,EAAS,OAAO,MAAM,GAAKE,GAAU,CAACF,EAAS,KAAI,EACnDH,GAAgB;EAAOG,EAAS,MAAME,CAAM,MAE3C,CAeD,GAbID,GAIAH,EAAK,OAAO,MAAM,GAAK,GAGvBO,GAAiB,KAAKP,CAAI,GAG1BQ,GAAkB,KAAKR,CAAI,GAG3BM,GAAQ,KAAKN,CAAI,EACjB,MAEJD,GAAgB;EAAOG,CACnD,CAC4B,CAACC,GAAa,CAACD,EAAS,KAAI,IAC5BC,EAAY,IAEhBpC,GAAO0C,EAAU;EACjB7B,EAAMA,EAAI,UAAU6B,EAAQ,OAAS,CAAC,EACtCT,EAAOE,EAAS,MAAME,CAAM,CACpD,CACA,CACqBT,EAAK,QAEFE,EACAF,EAAK,MAAQ,GAER,YAAY,KAAK5B,CAAG,IACzB8B,EAAoB,KAG5B,IAAIa,EAAS,KACTC,EAEA,KAAK,QAAQ,MACbD,EAAS,cAAc,KAAKX,CAAY,EACpCW,IACAC,EAAYD,EAAO,CAAC,IAAM,OAC1BX,EAAeA,EAAa,QAAQ,eAAgB,EAAE,IAG9DJ,EAAK,MAAM,KAAK,CACZ,KAAM,YACN,IAAA5B,EACA,KAAM,CAAC,CAAC2C,EACR,QAASC,EACT,MAAO,GACP,KAAMZ,EACN,OAAQ,CAAA,CAC5B,CAAiB,EACDJ,EAAK,KAAO5B,CAC5B,CAEY4B,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAE,IAAMA,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAE,IAAI,QAAO,EACrFA,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAE,KAAOA,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAE,KAAK,QAAO,EACvFA,EAAK,IAAMA,EAAK,IAAI,QAAO,EAE3B,QAASzC,EAAI,EAAGA,EAAIyC,EAAK,MAAM,OAAQzC,IAGnC,GAFA,KAAK,MAAM,MAAM,IAAM,GACvByC,EAAK,MAAMzC,CAAC,EAAE,OAAS,KAAK,MAAM,YAAYyC,EAAK,MAAMzC,CAAC,EAAE,KAAM,CAAA,CAAE,EAChE,CAACyC,EAAK,MAAO,CAEb,IAAMiB,EAAUjB,EAAK,MAAMzC,CAAC,EAAE,OAAO,OAAO+C,GAAKA,EAAE,OAAS,OAAO,EAC7DY,EAAwBD,EAAQ,OAAS,GAAKA,EAAQ,KAAKX,GAAK,SAAS,KAAKA,EAAE,GAAG,CAAC,EAC1FN,EAAK,MAAQkB,CACjC,CAGY,GAAIlB,EAAK,MACL,QAASzC,EAAI,EAAGA,EAAIyC,EAAK,MAAM,OAAQzC,IACnCyC,EAAK,MAAMzC,CAAC,EAAE,MAAQ,GAG9B,OAAOyC,CACnB,CACA,CACI,KAAKf,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAQA,MAPc,CACV,KAAM,OACN,MAAO,GACP,IAAKA,EAAI,CAAC,EACV,IAAKA,EAAI,CAAC,IAAM,OAASA,EAAI,CAAC,IAAM,UAAYA,EAAI,CAAC,IAAM,QAC3D,KAAMA,EAAI,CAAC,CAC3B,CAGA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,MAAM,IAAI,KAAKe,CAAG,EACzC,GAAIf,EAAK,CACL,IAAMiD,EAAMjD,EAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,OAAQ,GAAG,EAC9CvB,EAAOuB,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,QAAQ,WAAY,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAI,GACnGI,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGA,EAAI,CAAC,EAAE,OAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACrH,MAAO,CACH,KAAM,MACN,IAAAiD,EACA,IAAKjD,EAAI,CAAC,EACV,KAAAvB,EACA,MAAA2B,CAChB,CACA,CACA,CACI,MAAMW,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,MAAM,KAAKe,CAAG,EAI3C,GAHI,CAACf,GAGD,CAAC,OAAO,KAAKA,EAAI,CAAC,CAAC,EAEnB,OAEJ,IAAMkD,EAAUvE,GAAWqB,EAAI,CAAC,CAAC,EAC3BmD,EAASnD,EAAI,CAAC,EAAE,QAAQ,aAAc,EAAE,EAAE,MAAM,GAAG,EACnDoD,EAAOpD,EAAI,CAAC,GAAKA,EAAI,CAAC,EAAE,KAAI,EAAKA,EAAI,CAAC,EAAE,QAAQ,YAAa,EAAE,EAAE,MAAM;CAAI,EAAI,CAAA,EAC/EqD,EAAO,CACT,KAAM,QACN,IAAKrD,EAAI,CAAC,EACV,OAAQ,CAAA,EACR,MAAO,CAAA,EACP,KAAM,CAAA,CAClB,EACQ,GAAIkD,EAAQ,SAAWC,EAAO,OAI9B,SAAWG,KAASH,EACZ,YAAY,KAAKG,CAAK,EACtBD,EAAK,MAAM,KAAK,OAAO,EAElB,aAAa,KAAKC,CAAK,EAC5BD,EAAK,MAAM,KAAK,QAAQ,EAEnB,YAAY,KAAKC,CAAK,EAC3BD,EAAK,MAAM,KAAK,MAAM,EAGtBA,EAAK,MAAM,KAAK,IAAI,EAG5B,QAAShE,EAAI,EAAGA,EAAI6D,EAAQ,OAAQ7D,IAChCgE,EAAK,OAAO,KAAK,CACb,KAAMH,EAAQ7D,CAAC,EACf,OAAQ,KAAK,MAAM,OAAO6D,EAAQ7D,CAAC,CAAC,EACpC,OAAQ,GACR,MAAOgE,EAAK,MAAMhE,CAAC,CACnC,CAAa,EAEL,QAAWP,KAAOsE,EACdC,EAAK,KAAK,KAAK1E,GAAWG,EAAKuE,EAAK,OAAO,MAAM,EAAE,IAAI,CAACE,EAAMlE,KACnD,CACH,KAAMkE,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAI,EAC9B,OAAQ,GACR,MAAOF,EAAK,MAAMhE,CAAC,CACvC,EACa,CAAC,EAEN,OAAOgE,EACf,CACI,SAAStC,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,MAAM,SAAS,KAAKe,CAAG,EAC9C,GAAIf,EACA,MAAO,CACH,KAAM,UACN,IAAKA,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,EAAI,EACtC,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,UAAUe,EAAK,CACX,IAAMf,EAAM,KAAK,MAAM,MAAM,UAAU,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,OAAS,CAAC,IAAM;EAC5CA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAClBA,EAAI,CAAC,EACX,MAAO,CACH,KAAM,YACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,OAAOe,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,EAC7C,GAAIf,EACA,MAAO,CACH,KAAM,SACN,IAAKA,EAAI,CAAC,EACV,KAAMxC,EAAOwC,EAAI,CAAC,CAAC,CACnC,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAI,CAAC,KAAK,MAAM,MAAM,QAAU,QAAQ,KAAKA,EAAI,CAAC,CAAC,EAC/C,KAAK,MAAM,MAAM,OAAS,GAErB,KAAK,MAAM,MAAM,QAAU,UAAU,KAAKA,EAAI,CAAC,CAAC,IACrD,KAAK,MAAM,MAAM,OAAS,IAE1B,CAAC,KAAK,MAAM,MAAM,YAAc,iCAAiC,KAAKA,EAAI,CAAC,CAAC,EAC5E,KAAK,MAAM,MAAM,WAAa,GAEzB,KAAK,MAAM,MAAM,YAAc,mCAAmC,KAAKA,EAAI,CAAC,CAAC,IAClF,KAAK,MAAM,MAAM,WAAa,IAE3B,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,OAAQ,KAAK,MAAM,MAAM,OACzB,WAAY,KAAK,MAAM,MAAM,WAC7B,MAAO,GACP,KAAMA,EAAI,CAAC,CAC3B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAMwD,EAAaxD,EAAI,CAAC,EAAE,KAAI,EAC9B,GAAI,CAAC,KAAK,QAAQ,UAAY,KAAK,KAAKwD,CAAU,EAAG,CAEjD,GAAI,CAAE,KAAK,KAAKA,CAAU,EACtB,OAGJ,IAAMC,EAAanE,EAAMkE,EAAW,MAAM,EAAG,EAAE,EAAG,IAAI,EACtD,IAAKA,EAAW,OAASC,EAAW,QAAU,IAAM,EAChD,MAEpB,KACiB,CAED,IAAMC,EAAiB9D,GAAmBI,EAAI,CAAC,EAAG,IAAI,EACtD,GAAI0D,EAAiB,GAAI,CAErB,IAAMC,GADQ3D,EAAI,CAAC,EAAE,QAAQ,GAAG,IAAM,EAAI,EAAI,GACtBA,EAAI,CAAC,EAAE,OAAS0D,EACxC1D,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAG0D,CAAc,EAC3C1D,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAG2D,CAAO,EAAE,KAAI,EAC1C3D,EAAI,CAAC,EAAI,EAC7B,CACA,CACY,IAAIvB,EAAOuB,EAAI,CAAC,EACZI,EAAQ,GACZ,GAAI,KAAK,QAAQ,SAAU,CAEvB,IAAMH,EAAO,gCAAgC,KAAKxB,CAAI,EAClDwB,IACAxB,EAAOwB,EAAK,CAAC,EACbG,EAAQH,EAAK,CAAC,EAElC,MAEgBG,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAAI,GAE3C,OAAAvB,EAAOA,EAAK,KAAI,EACZ,KAAK,KAAKA,CAAI,IACV,KAAK,QAAQ,UAAY,CAAE,KAAK,KAAK+E,CAAU,EAE/C/E,EAAOA,EAAK,MAAM,CAAC,EAGnBA,EAAOA,EAAK,MAAM,EAAG,EAAE,GAGxBsB,GAAWC,EAAK,CACnB,KAAMvB,GAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAChE,MAAO2B,GAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,CACnF,EAAeJ,EAAI,CAAC,EAAG,KAAK,KAAK,CACjC,CACA,CACI,QAAQe,EAAK6C,EAAO,CAChB,IAAI5D,EACJ,IAAKA,EAAM,KAAK,MAAM,OAAO,QAAQ,KAAKe,CAAG,KACrCf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,GAAI,CAC/C,IAAM8C,GAAc7D,EAAI,CAAC,GAAKA,EAAI,CAAC,GAAG,QAAQ,OAAQ,GAAG,EACnDC,EAAO2D,EAAMC,EAAW,YAAW,CAAE,EAC3C,GAAI,CAAC5D,EAAM,CACP,IAAMI,EAAOL,EAAI,CAAC,EAAE,OAAO,CAAC,EAC5B,MAAO,CACH,KAAM,OACN,IAAKK,EACL,KAAAA,CACpB,CACA,CACY,OAAON,GAAWC,EAAKC,EAAMD,EAAI,CAAC,EAAG,KAAK,KAAK,CAC3D,CACA,CACI,SAASe,EAAK+C,EAAWC,EAAW,GAAI,CACpC,IAAIhF,EAAQ,KAAK,MAAM,OAAO,eAAe,KAAKgC,CAAG,EAIrD,GAHI,CAAChC,GAGDA,EAAM,CAAC,GAAKgF,EAAS,MAAM,eAAe,EAC1C,OAEJ,GAAI,EADahF,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,KACxB,CAACgF,GAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,EAAG,CAExE,IAAMC,EAAU,CAAC,GAAGjF,EAAM,CAAC,CAAC,EAAE,OAAS,EACnCkF,EAAQC,EAASC,EAAaH,EAASI,EAAgB,EACrDC,EAAStF,EAAM,CAAC,EAAE,CAAC,IAAM,IAAM,KAAK,MAAM,OAAO,kBAAoB,KAAK,MAAM,OAAO,kBAI7F,IAHAsF,EAAO,UAAY,EAEnBP,EAAYA,EAAU,MAAM,GAAK/C,EAAI,OAASiD,CAAO,GAC7CjF,EAAQsF,EAAO,KAAKP,CAAS,IAAM,MAAM,CAE7C,GADAG,EAASlF,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,EACxE,CAACkF,EACD,SAEJ,GADAC,EAAU,CAAC,GAAGD,CAAM,EAAE,OAClBlF,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAG,CACtBoF,GAAcD,EACd,QACpB,UACyBnF,EAAM,CAAC,GAAKA,EAAM,CAAC,IACpBiF,EAAU,GAAK,GAAGA,EAAUE,GAAW,GAAI,CAC3CE,GAAiBF,EACjB,QACxB,CAGgB,GADAC,GAAcD,EACVC,EAAa,EACb,SAEJD,EAAU,KAAK,IAAIA,EAASA,EAAUC,EAAaC,CAAa,EAEhE,IAAME,EAAiB,CAAC,GAAGvF,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAClCmB,EAAMa,EAAI,MAAM,EAAGiD,EAAUjF,EAAM,MAAQuF,EAAiBJ,CAAO,EAEzE,GAAI,KAAK,IAAIF,EAASE,CAAO,EAAI,EAAG,CAChC,IAAM7D,EAAOH,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,KACN,IAAAA,EACA,KAAAG,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CAC5D,CACA,CAEgB,IAAMA,EAAOH,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,SACN,IAAAA,EACA,KAAAG,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACxD,CACA,CACA,CACA,CACI,SAASU,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,QAAQ,MAAO,GAAG,EAC9BuE,EAAmB,OAAO,KAAKlE,CAAI,EACnCmE,EAA0B,KAAK,KAAKnE,CAAI,GAAK,KAAK,KAAKA,CAAI,EACjE,OAAIkE,GAAoBC,IACpBnE,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAAS,CAAC,GAE5CA,EAAO7C,EAAO6C,EAAM,EAAI,EACjB,CACH,KAAM,WACN,IAAKL,EAAI,CAAC,EACV,KAAAK,CAChB,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,OAAO,GAAG,KAAKe,CAAG,EACzC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,MACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,CACtD,CAEA,CACI,SAASe,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,SAAS,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAIK,EAAM5B,EACV,OAAIuB,EAAI,CAAC,IAAM,KACXK,EAAO7C,EAAOwC,EAAI,CAAC,CAAC,EACpBvB,EAAO,UAAY4B,IAGnBA,EAAO7C,EAAOwC,EAAI,CAAC,CAAC,EACpBvB,EAAO4B,GAEJ,CACH,KAAM,OACN,IAAKL,EAAI,CAAC,EACV,KAAAK,EACA,KAAA5B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK4B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,IAAIU,EAAK,CACL,IAAIf,EACJ,GAAIA,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAAG,CACvC,IAAIV,EAAM5B,EACV,GAAIuB,EAAI,CAAC,IAAM,IACXK,EAAO7C,EAAOwC,EAAI,CAAC,CAAC,EACpBvB,EAAO,UAAY4B,MAElB,CAED,IAAIoE,EACJ,GACIA,EAAczE,EAAI,CAAC,EACnBA,EAAI,CAAC,EAAI,KAAK,MAAM,OAAO,WAAW,KAAKA,EAAI,CAAC,CAAC,IAAI,CAAC,GAAK,SACtDyE,IAAgBzE,EAAI,CAAC,GAC9BK,EAAO7C,EAAOwC,EAAI,CAAC,CAAC,EAChBA,EAAI,CAAC,IAAM,OACXvB,EAAO,UAAYuB,EAAI,CAAC,EAGxBvB,EAAOuB,EAAI,CAAC,CAEhC,CACY,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,KAAA5B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK4B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,WAAWU,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EACJ,OAAI,KAAK,MAAM,MAAM,WACjBA,EAAOL,EAAI,CAAC,EAGZK,EAAO7C,EAAOwC,EAAI,CAAC,CAAC,EAEjB,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,CAChB,CACA,CACA,CACA,ECzwBMqE,GAAU,mBACVC,GAAY,uCACZC,GAAS,8GACTC,EAAK,qEACLC,GAAU,uCACVC,GAAS,wBACTC,GAAWhH,EAAK,oJAAoJ,EACrK,QAAQ,QAAS+G,EAAM,EACvB,QAAQ,aAAc,MAAM,EAC5B,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,SAAQ,EACPE,EAAa,uFACbC,GAAY,UACZC,EAAc,8BACdC,GAAMpH,EAAK,iGAAiG,EAC7G,QAAQ,QAASmH,CAAW,EAC5B,QAAQ,QAAS,8DAA8D,EAC/E,SAAQ,EACPrD,GAAO9D,EAAK,sCAAsC,EACnD,QAAQ,QAAS+G,EAAM,EACvB,SAAQ,EACPM,EAAO,gWAMPC,EAAW,gCACX7H,GAAOO,EAAK,mdASP,GAAG,EACT,QAAQ,UAAWsH,CAAQ,EAC3B,QAAQ,MAAOD,CAAI,EACnB,QAAQ,YAAa,0EAA0E,EAC/F,SAAQ,EACPE,GAAYvH,EAAKiH,CAAU,EAC5B,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOQ,CAAI,EACnB,SAAQ,EACPG,GAAaxH,EAAK,yCAAyC,EAC5D,QAAQ,YAAauH,EAAS,EAC9B,SAAQ,EAIPE,EAAc,CAChB,WAAAD,GACA,KAAMb,GACN,IAAAS,GACA,OAAAR,GACA,QAAAE,GACA,GAAAD,EACA,KAAApH,GACA,SAAAuH,GACA,KAAAlD,GACA,QAAA4C,GACA,UAAAa,GACA,MAAO7G,EACP,KAAMwG,EACV,EAIMQ,GAAW1H,EAAK,6JAEsE,EACvF,QAAQ,KAAM6G,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,aAAc,SAAS,EAC/B,QAAQ,OAAQ,YAAY,EAC5B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOQ,CAAI,EACnB,SAAQ,EACPM,GAAW,CACb,GAAGF,EACH,MAAOC,GACP,UAAW1H,EAAKiH,CAAU,EACrB,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,QAASa,EAAQ,EACzB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOL,CAAI,EACnB,SAAQ,CACjB,EAIMO,GAAgB,CAClB,GAAGH,EACH,KAAMzH,EAAK,wIAEiE,EACvE,QAAQ,UAAWsH,CAAQ,EAC3B,QAAQ,OAAQ,mKAGgB,EAChC,SAAQ,EACb,IAAK,oEACL,QAAS,yBACT,OAAQ5G,EACR,SAAU,mCACV,UAAWV,EAAKiH,CAAU,EACrB,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW;EAAiB,EACpC,QAAQ,WAAYG,EAAQ,EAC5B,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,UAAW,EAAE,EACrB,QAAQ,QAAS,EAAE,EACnB,QAAQ,QAAS,EAAE,EACnB,QAAQ,OAAQ,EAAE,EAClB,SAAQ,CACjB,EAIMxH,GAAS,8CACTqI,GAAa,sCACbC,GAAK,wBACLC,GAAa,8EAEbC,EAAe,eACfC,GAAcjI,EAAK,6BAA8B,GAAG,EACrD,QAAQ,eAAgBgI,CAAY,EAAE,SAAQ,EAE7CE,GAAY,gDACZC,GAAiBnI,EAAK,oEAAqE,GAAG,EAC/F,QAAQ,SAAUgI,CAAY,EAC9B,SAAQ,EACPI,GAAoBpI,EAAK,wQAOY,IAAI,EAC1C,QAAQ,SAAUgI,CAAY,EAC9B,SAAQ,EAEPK,GAAoBrI,EAAK,uNAMY,IAAI,EAC1C,QAAQ,SAAUgI,CAAY,EAC9B,SAAQ,EACPM,GAAiBtI,EAAK,cAAe,IAAI,EAC1C,QAAQ,SAAUgI,CAAY,EAC9B,SAAQ,EACPO,GAAWvI,EAAK,qCAAqC,EACtD,QAAQ,SAAU,8BAA8B,EAChD,QAAQ,QAAS,8IAA8I,EAC/J,SAAQ,EACPwI,GAAiBxI,EAAKsH,CAAQ,EAAE,QAAQ,YAAa,KAAK,EAAE,SAAQ,EACpErC,GAAMjF,EAAK,0JAKuB,EACnC,QAAQ,UAAWwI,EAAc,EACjC,QAAQ,YAAa,6EAA6E,EAClG,SAAQ,EACPC,EAAe,sDACfxG,GAAOjC,EAAK,+CAA+C,EAC5D,QAAQ,QAASyI,CAAY,EAC7B,QAAQ,OAAQ,sCAAsC,EACtD,QAAQ,QAAS,6DAA6D,EAC9E,SAAQ,EACPC,GAAU1I,EAAK,yBAAyB,EACzC,QAAQ,QAASyI,CAAY,EAC7B,QAAQ,MAAOtB,CAAW,EAC1B,SAAQ,EACPwB,GAAS3I,EAAK,uBAAuB,EACtC,QAAQ,MAAOmH,CAAW,EAC1B,SAAQ,EACPyB,GAAgB5I,EAAK,wBAAyB,GAAG,EAClD,QAAQ,UAAW0I,EAAO,EAC1B,QAAQ,SAAUC,EAAM,EACxB,SAAQ,EAIPE,EAAe,CACjB,WAAYnI,EACZ,eAAA4H,GACA,SAAAC,GACA,UAAAL,GACA,GAAAJ,GACA,KAAMD,GACN,IAAKnH,EACL,eAAAyH,GACA,kBAAAC,GACA,kBAAAC,GACA,OAAA7I,GACA,KAAAyC,GACA,OAAA0G,GACA,YAAAV,GACA,QAAAS,GACA,cAAAE,GACA,IAAA3D,GACA,KAAM8C,GACN,IAAKrH,CACT,EAIMoI,GAAiB,CACnB,GAAGD,EACH,KAAM7I,EAAK,yBAAyB,EAC/B,QAAQ,QAASyI,CAAY,EAC7B,SAAQ,EACb,QAASzI,EAAK,+BAA+B,EACxC,QAAQ,QAASyI,CAAY,EAC7B,SAAQ,CACjB,EAIMM,EAAY,CACd,GAAGF,EACH,OAAQ7I,EAAKR,EAAM,EAAE,QAAQ,KAAM,MAAM,EAAE,SAAQ,EACnD,IAAKQ,EAAK,mEAAoE,GAAG,EAC5E,QAAQ,QAAS,2EAA2E,EAC5F,SAAQ,EACb,WAAY,6EACZ,IAAK,+CACL,KAAM,4NACV,EAIMgJ,GAAe,CACjB,GAAGD,EACH,GAAI/I,EAAK8H,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAAE,SAAQ,EAC1C,KAAM9H,EAAK+I,EAAU,IAAI,EACpB,QAAQ,OAAQ,eAAe,EAC/B,QAAQ,UAAW,GAAG,EACtB,SAAQ,CACjB,EAIaE,EAAQ,CACjB,OAAQxB,EACR,IAAKE,GACL,SAAUC,EACd,EACasB,EAAS,CAClB,OAAQL,EACR,IAAKE,EACL,OAAQC,GACR,SAAUF,EACd,ECtRaK,EAAN,MAAMC,CAAO,CAChB,OACA,QACA,MACA,UACA,YACA,YAAYtG,EAAS,CAEjB,KAAK,OAAS,CAAA,EACd,KAAK,OAAO,MAAQ,OAAO,OAAO,IAAI,EACtC,KAAK,QAAUA,GAAWhE,EAC1B,KAAK,QAAQ,UAAY,KAAK,QAAQ,WAAa,IAAI+D,EACvD,KAAK,UAAY,KAAK,QAAQ,UAC9B,KAAK,UAAU,QAAU,KAAK,QAC9B,KAAK,UAAU,MAAQ,KACvB,KAAK,YAAc,CAAA,EACnB,KAAK,MAAQ,CACT,OAAQ,GACR,WAAY,GACZ,IAAK,EACjB,EACQ,IAAMwG,EAAQ,CACV,MAAOJ,EAAM,OACb,OAAQC,EAAO,MAC3B,EACY,KAAK,QAAQ,UACbG,EAAM,MAAQJ,EAAM,SACpBI,EAAM,OAASH,EAAO,UAEjB,KAAK,QAAQ,MAClBG,EAAM,MAAQJ,EAAM,IAChB,KAAK,QAAQ,OACbI,EAAM,OAASH,EAAO,OAGtBG,EAAM,OAASH,EAAO,KAG9B,KAAK,UAAU,MAAQG,CAC/B,CAII,WAAW,OAAQ,CACf,MAAO,CACH,MAAAJ,EACA,OAAAC,CACZ,CACA,CAII,OAAO,IAAInG,EAAKD,EAAS,CAErB,OADc,IAAIsG,EAAOtG,CAAO,EACnB,IAAIC,CAAG,CAC5B,CAII,OAAO,UAAUA,EAAKD,EAAS,CAE3B,OADc,IAAIsG,EAAOtG,CAAO,EACnB,aAAaC,CAAG,CACrC,CAII,IAAIA,EAAK,CACLA,EAAMA,EACD,QAAQ,WAAY;CAAI,EAC7B,KAAK,YAAYA,EAAK,KAAK,MAAM,EACjC,QAAS1B,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAAK,CAC9C,IAAMiI,EAAO,KAAK,YAAYjI,CAAC,EAC/B,KAAK,aAAaiI,EAAK,IAAKA,EAAK,MAAM,CACnD,CACQ,YAAK,YAAc,CAAA,EACZ,KAAK,MACpB,CACI,YAAYvG,EAAKG,EAAS,CAAA,EAAIqG,EAAuB,GAAO,CACpD,KAAK,QAAQ,SACbxG,EAAMA,EAAI,QAAQ,MAAO,MAAM,EAAE,QAAQ,SAAU,EAAE,EAGrDA,EAAMA,EAAI,QAAQ,eAAgB,CAAClD,EAAG2J,EAASC,IACpCD,EAAU,OAAO,OAAOC,EAAK,MAAM,CAC7C,EAEL,IAAInH,EACAkB,EACAkG,EACJ,KAAO3G,GACH,GAAI,OAAK,QAAQ,YACV,KAAK,QAAQ,WAAW,OACxB,KAAK,QAAQ,WAAW,MAAM,KAAM4G,IAC/BrH,EAAQqH,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI5G,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,GAIL,IAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,SAAW,GAAKY,EAAO,OAAS,EAG1CA,EAAOA,EAAO,OAAS,CAAC,EAAE,KAAO;EAGjCA,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCkB,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAEhCM,IAAcA,EAAU,OAAS,aAAeA,EAAU,OAAS,SACnEA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAMkB,EAAU,MAG9DN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,WAAWS,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCkB,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCM,IAAcA,EAAU,OAAS,aAAeA,EAAU,OAAS,SACnEA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,IAC/B,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAMkB,EAAU,MAExD,KAAK,OAAO,MAAMlB,EAAM,GAAG,IACjC,KAAK,OAAO,MAAMA,EAAM,GAAG,EAAI,CAC3B,KAAMA,EAAM,KACZ,MAAOA,EAAM,KACrC,GAEgB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAIY,GADAoH,EAAS3G,EACL,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAY,CAC/D,IAAI6G,EAAa,IACXC,EAAU9G,EAAI,MAAM,CAAC,EACvB+G,EACJ,KAAK,QAAQ,WAAW,WAAW,QAASC,GAAkB,CAC1DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCF,EAAS3G,EAAI,UAAU,EAAG6G,EAAa,CAAC,EAE5D,CACY,GAAI,KAAK,MAAM,MAAQtH,EAAQ,KAAK,UAAU,UAAUoH,CAAM,GAAI,CAC9DlG,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCqG,GAAwB/F,GAAW,OAAS,aAC5CA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAMkB,EAAU,MAG9DN,EAAO,KAAKZ,CAAK,EAErBiH,EAAwBG,EAAO,SAAW3G,EAAI,OAC9CA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCkB,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCM,GAAaA,EAAU,OAAS,QAChCA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAMkB,EAAU,MAG9DN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMiH,EAAS,0BAA4BjH,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMiH,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,EAEQ,YAAK,MAAM,IAAM,GACV9G,CACf,CACI,OAAOH,EAAKG,EAAS,CAAA,EAAI,CACrB,YAAK,YAAY,KAAK,CAAE,IAAAH,EAAK,OAAAG,CAAM,CAAE,EAC9BA,CACf,CAII,aAAaH,EAAKG,EAAS,CAAA,EAAI,CAC3B,IAAIZ,EAAOkB,EAAWkG,EAElB5D,EAAY/C,EACZhC,EACAkJ,EAAclE,EAElB,GAAI,KAAK,OAAO,MAAO,CACnB,IAAMH,EAAQ,OAAO,KAAK,KAAK,OAAO,KAAK,EAC3C,GAAIA,EAAM,OAAS,EACf,MAAQ7E,EAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK+E,CAAS,IAAM,MACtEF,EAAM,SAAS7E,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,EAAI,EAAG,EAAE,CAAC,IAChE+E,EAAYA,EAAU,MAAM,EAAG/E,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAM+E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS,EAIvL,CAEQ,MAAQ/E,EAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK+E,CAAS,IAAM,MACtEA,EAAYA,EAAU,MAAM,EAAG/E,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAM+E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS,EAG/J,MAAQ/E,EAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK+E,CAAS,IAAM,MAC3EA,EAAYA,EAAU,MAAM,EAAG/E,EAAM,KAAK,EAAI,KAAO+E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS,EAE7H,KAAO/C,GAMH,GALKkH,IACDlE,EAAW,IAEfkE,EAAe,GAEX,OAAK,QAAQ,YACV,KAAK,QAAQ,WAAW,QACxB,KAAK,QAAQ,WAAW,OAAO,KAAMN,IAChCrH,EAAQqH,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI5G,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,GAIL,IAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCkB,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCM,GAAalB,EAAM,OAAS,QAAUkB,EAAU,OAAS,QACzDA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,EAAK,KAAK,OAAO,KAAK,EAAG,CACxDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCkB,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCM,GAAalB,EAAM,OAAS,QAAUkB,EAAU,OAAS,QACzDA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,EAAK+C,EAAWC,CAAQ,EAAG,CAC3DhD,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAI,CAAC,KAAK,MAAM,SAAWA,EAAQ,KAAK,UAAU,IAAIS,CAAG,GAAI,CACzDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAIY,GADAoH,EAAS3G,EACL,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,YAAa,CAChE,IAAI6G,EAAa,IACXC,EAAU9G,EAAI,MAAM,CAAC,EACvB+G,EACJ,KAAK,QAAQ,WAAW,YAAY,QAASC,GAAkB,CAC3DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCF,EAAS3G,EAAI,UAAU,EAAG6G,EAAa,CAAC,EAE5D,CACY,GAAItH,EAAQ,KAAK,UAAU,WAAWoH,CAAM,EAAG,CAC3C3G,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,MAAM,EAAE,IAAM,MACxByD,EAAWzD,EAAM,IAAI,MAAM,EAAE,GAEjC2H,EAAe,GACfzG,EAAYN,EAAOA,EAAO,OAAS,CAAC,EAChCM,GAAaA,EAAU,OAAS,QAChCA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMiH,EAAS,0BAA4BjH,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMiH,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,EAEQ,OAAO9G,CACf,CACA,EC3aagH,EAAN,KAAgB,CACnB,QACA,OACA,YAAYpH,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAClC,CACI,MAAMwD,EAAO,CACT,MAAO,EACf,CACI,KAAK,CAAE,KAAAD,EAAM,KAAA8H,EAAM,QAAAjJ,CAAO,EAAI,CAC1B,IAAMkJ,GAAcD,GAAQ,IAAI,MAAM,MAAM,IAAI,CAAC,EAC3CE,EAAOhI,EAAK,QAAQ,MAAO,EAAE,EAAI;EACvC,OAAK+H,EAKE,8BACD5K,EAAO4K,CAAU,EACjB,MACClJ,EAAUmJ,EAAO7K,EAAO6K,EAAM,EAAI,GACnC;EARK,eACAnJ,EAAUmJ,EAAO7K,EAAO6K,EAAM,EAAI,GACnC;CAOlB,CACI,WAAW,CAAE,OAAAnH,CAAM,EAAI,CAEnB,MAAO;EADM,KAAK,OAAO,MAAMA,CAAM,CACT;CACpC,CACI,KAAK,CAAE,KAAAb,CAAI,EAAI,CACX,OAAOA,CACf,CACI,QAAQ,CAAE,OAAAa,EAAQ,MAAAoH,CAAK,EAAI,CACvB,MAAO,KAAKA,CAAK,IAAI,KAAK,OAAO,YAAYpH,CAAM,CAAC,MAAMoH,CAAK;CACvE,CACI,GAAGhI,EAAO,CACN,MAAO;CACf,CACI,KAAKA,EAAO,CACR,IAAMiI,EAAUjI,EAAM,QAChBkI,EAAQlI,EAAM,MAChBmI,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIpI,EAAM,MAAM,OAAQoI,IAAK,CACzC,IAAMrF,EAAO/C,EAAM,MAAMoI,CAAC,EAC1BD,GAAQ,KAAK,SAASpF,CAAI,CACtC,CACQ,IAAMsF,EAAOJ,EAAU,KAAO,KACxBK,EAAaL,GAAWC,IAAU,EAAM,WAAaA,EAAQ,IAAO,GAC1E,MAAO,IAAMG,EAAOC,EAAY;EAAQH,EAAO,KAAOE,EAAO;CACrE,CACI,SAAStF,EAAM,CACX,IAAIwF,EAAW,GACf,GAAIxF,EAAK,KAAM,CACX,IAAMyF,EAAW,KAAK,SAAS,CAAE,QAAS,CAAC,CAACzF,EAAK,OAAO,CAAE,EACtDA,EAAK,MACDA,EAAK,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAS,aAClDA,EAAK,OAAO,CAAC,EAAE,KAAOyF,EAAW,IAAMzF,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SAC/FA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAOyF,EAAW,IAAMzF,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAI9EA,EAAK,OAAO,QAAQ,CAChB,KAAM,OACN,IAAKyF,EAAW,IAChB,KAAMA,EAAW,GACzC,CAAqB,EAILD,GAAYC,EAAW,GAEvC,CACQ,OAAAD,GAAY,KAAK,OAAO,MAAMxF,EAAK,OAAQ,CAAC,CAACA,EAAK,KAAK,EAChD,OAAOwF,CAAQ;CAC9B,CACI,SAAS,CAAE,QAAAE,CAAO,EAAI,CAClB,MAAO,WACAA,EAAU,cAAgB,IAC3B,8BACd,CACI,UAAU,CAAE,OAAA7H,CAAM,EAAI,CAClB,MAAO,MAAM,KAAK,OAAO,YAAYA,CAAM,CAAC;CACpD,CACI,MAAMZ,EAAO,CACT,IAAI0I,EAAS,GAETzF,EAAO,GACX,QAASmF,EAAI,EAAGA,EAAIpI,EAAM,OAAO,OAAQoI,IACrCnF,GAAQ,KAAK,UAAUjD,EAAM,OAAOoI,CAAC,CAAC,EAE1CM,GAAU,KAAK,SAAS,CAAE,KAAMzF,CAAI,CAAE,EACtC,IAAIkF,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIpI,EAAM,KAAK,OAAQoI,IAAK,CACxC,IAAM5J,EAAMwB,EAAM,KAAKoI,CAAC,EACxBnF,EAAO,GACP,QAAS0F,EAAI,EAAGA,EAAInK,EAAI,OAAQmK,IAC5B1F,GAAQ,KAAK,UAAUzE,EAAImK,CAAC,CAAC,EAEjCR,GAAQ,KAAK,SAAS,CAAE,KAAMlF,CAAI,CAAE,CAChD,CACQ,OAAIkF,IACAA,EAAO,UAAUA,CAAI,YAClB;;EAEDO,EACA;EACAP,EACA;CACd,CACI,SAAS,CAAE,KAAApI,CAAI,EAAI,CACf,MAAO;EAASA,CAAI;CAC5B,CACI,UAAUC,EAAO,CACb,IAAM4I,EAAU,KAAK,OAAO,YAAY5I,EAAM,MAAM,EAC9CqI,EAAOrI,EAAM,OAAS,KAAO,KAInC,OAHYA,EAAM,MACZ,IAAIqI,CAAI,WAAWrI,EAAM,KAAK,KAC9B,IAAIqI,CAAI,KACDO,EAAU,KAAKP,CAAI;CACxC,CAII,OAAO,CAAE,OAAAzH,CAAM,EAAI,CACf,MAAO,WAAW,KAAK,OAAO,YAAYA,CAAM,CAAC,WACzD,CACI,GAAG,CAAE,OAAAA,CAAM,EAAI,CACX,MAAO,OAAO,KAAK,OAAO,YAAYA,CAAM,CAAC,OACrD,CACI,SAAS,CAAE,KAAAb,CAAI,EAAI,CACf,MAAO,SAASA,CAAI,SAC5B,CACI,GAAGC,EAAO,CACN,MAAO,MACf,CACI,IAAI,CAAE,OAAAY,CAAM,EAAI,CACZ,MAAO,QAAQ,KAAK,OAAO,YAAYA,CAAM,CAAC,QACtD,CACI,KAAK,CAAE,KAAAzC,EAAM,MAAA2B,EAAO,OAAAc,CAAM,EAAI,CAC1B,IAAMb,EAAO,KAAK,OAAO,YAAYa,CAAM,EACrCiI,EAAY3K,GAASC,CAAI,EAC/B,GAAI0K,IAAc,KACd,OAAO9I,EAEX5B,EAAO0K,EACP,IAAIC,EAAM,YAAc3K,EAAO,IAC/B,OAAI2B,IACAgJ,GAAO,WAAahJ,EAAQ,KAEhCgJ,GAAO,IAAM/I,EAAO,OACb+I,CACf,CACI,MAAM,CAAE,KAAA3K,EAAM,MAAA2B,EAAO,KAAAC,CAAI,EAAI,CACzB,IAAM8I,EAAY3K,GAASC,CAAI,EAC/B,GAAI0K,IAAc,KACd,OAAO9I,EAEX5B,EAAO0K,EACP,IAAIC,EAAM,aAAa3K,CAAI,UAAU4B,CAAI,IACzC,OAAID,IACAgJ,GAAO,WAAWhJ,CAAK,KAE3BgJ,GAAO,IACAA,CACf,CACI,KAAK9I,EAAO,CACR,MAAO,WAAYA,GAASA,EAAM,OAAS,KAAK,OAAO,YAAYA,EAAM,MAAM,EAAIA,EAAM,IACjG,CACA,ECxKa+I,EAAN,KAAoB,CAEvB,OAAO,CAAE,KAAAhJ,CAAI,EAAI,CACb,OAAOA,CACf,CACI,GAAG,CAAE,KAAAA,CAAI,EAAI,CACT,OAAOA,CACf,CACI,SAAS,CAAE,KAAAA,CAAI,EAAI,CACf,OAAOA,CACf,CACI,IAAI,CAAE,KAAAA,CAAI,EAAI,CACV,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,MAAO,GAAKA,CACpB,CACI,MAAM,CAAE,KAAAA,CAAI,EAAI,CACZ,MAAO,GAAKA,CACpB,CACI,IAAK,CACD,MAAO,EACf,CACA,EC3BaiJ,EAAN,MAAMC,CAAQ,CACjB,QACA,SACA,aACA,YAAYzI,EAAS,CACjB,KAAK,QAAUA,GAAWhE,EAC1B,KAAK,QAAQ,SAAW,KAAK,QAAQ,UAAY,IAAIoL,EACrD,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,SAAS,QAAU,KAAK,QAC7B,KAAK,SAAS,OAAS,KACvB,KAAK,aAAe,IAAImB,CAChC,CAII,OAAO,MAAMnI,EAAQJ,EAAS,CAE1B,OADe,IAAIyI,EAAQzI,CAAO,EACpB,MAAMI,CAAM,CAClC,CAII,OAAO,YAAYA,EAAQJ,EAAS,CAEhC,OADe,IAAIyI,EAAQzI,CAAO,EACpB,YAAYI,CAAM,CACxC,CAII,MAAMA,EAAQK,EAAM,GAAM,CACtB,IAAI6H,EAAM,GACV,QAAS/J,EAAI,EAAGA,EAAI6B,EAAO,OAAQ7B,IAAK,CACpC,IAAMmK,EAAWtI,EAAO7B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAa,KAAK,QAAQ,WAAW,UAAUmK,EAAS,IAAI,EAAG,CAClH,IAAMC,EAAeD,EACfE,EAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAY,EACpG,GAAIC,IAAQ,IAAS,CAAC,CAAC,QAAS,KAAM,UAAW,OAAQ,QAAS,aAAc,OAAQ,OAAQ,YAAa,MAAM,EAAE,SAASD,EAAa,IAAI,EAAG,CAC9IL,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMpJ,EAAQkJ,EACd,OAAQlJ,EAAM,KAAI,CACd,IAAK,QAAS,CACV8I,GAAO,KAAK,SAAS,MAAM9I,CAAK,EAChC,QACpB,CACgB,IAAK,KAAM,CACP8I,GAAO,KAAK,SAAS,GAAG9I,CAAK,EAC7B,QACpB,CACgB,IAAK,UAAW,CACZ8I,GAAO,KAAK,SAAS,QAAQ9I,CAAK,EAClC,QACpB,CACgB,IAAK,OAAQ,CACT8I,GAAO,KAAK,SAAS,KAAK9I,CAAK,EAC/B,QACpB,CACgB,IAAK,QAAS,CACV8I,GAAO,KAAK,SAAS,MAAM9I,CAAK,EAChC,QACpB,CACgB,IAAK,aAAc,CACf8I,GAAO,KAAK,SAAS,WAAW9I,CAAK,EACrC,QACpB,CACgB,IAAK,OAAQ,CACT8I,GAAO,KAAK,SAAS,KAAK9I,CAAK,EAC/B,QACpB,CACgB,IAAK,OAAQ,CACT8I,GAAO,KAAK,SAAS,KAAK9I,CAAK,EAC/B,QACpB,CACgB,IAAK,YAAa,CACd8I,GAAO,KAAK,SAAS,UAAU9I,CAAK,EACpC,QACpB,CACgB,IAAK,OAAQ,CACT,IAAIqJ,EAAYrJ,EACZmI,EAAO,KAAK,SAAS,KAAKkB,CAAS,EACvC,KAAOtK,EAAI,EAAI6B,EAAO,QAAUA,EAAO7B,EAAI,CAAC,EAAE,OAAS,QACnDsK,EAAYzI,EAAO,EAAE7B,CAAC,EACtBoJ,GAAQ;EAAO,KAAK,SAAS,KAAKkB,CAAS,EAE3CpI,EACA6H,GAAO,KAAK,SAAS,UAAU,CAC3B,KAAM,YACN,IAAKX,EACL,KAAMA,EACN,OAAQ,CAAC,CAAE,KAAM,OAAQ,IAAKA,EAAM,KAAMA,CAAI,CAAE,CAC5E,CAAyB,EAGDW,GAAOX,EAEX,QACpB,CACgB,QAAS,CACL,IAAMT,EAAS,eAAiB1H,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM0H,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CAII,YAAYlI,EAAQ0I,EAAU,CAC1BA,EAAWA,GAAY,KAAK,SAC5B,IAAIR,EAAM,GACV,QAAS/J,EAAI,EAAGA,EAAI6B,EAAO,OAAQ7B,IAAK,CACpC,IAAMmK,EAAWtI,EAAO7B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAa,KAAK,QAAQ,WAAW,UAAUmK,EAAS,IAAI,EAAG,CAClH,IAAME,EAAM,KAAK,QAAQ,WAAW,UAAUF,EAAS,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAQ,EAC5F,GAAIE,IAAQ,IAAS,CAAC,CAAC,SAAU,OAAQ,OAAQ,QAAS,SAAU,KAAM,WAAY,KAAM,MAAO,MAAM,EAAE,SAASF,EAAS,IAAI,EAAG,CAChIJ,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMpJ,EAAQkJ,EACd,OAAQlJ,EAAM,KAAI,CACd,IAAK,SAAU,CACX8I,GAAOQ,EAAS,KAAKtJ,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACT8I,GAAOQ,EAAS,KAAKtJ,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACT8I,GAAOQ,EAAS,KAAKtJ,CAAK,EAC1B,KACpB,CACgB,IAAK,QAAS,CACV8I,GAAOQ,EAAS,MAAMtJ,CAAK,EAC3B,KACpB,CACgB,IAAK,SAAU,CACX8I,GAAOQ,EAAS,OAAOtJ,CAAK,EAC5B,KACpB,CACgB,IAAK,KAAM,CACP8I,GAAOQ,EAAS,GAAGtJ,CAAK,EACxB,KACpB,CACgB,IAAK,WAAY,CACb8I,GAAOQ,EAAS,SAAStJ,CAAK,EAC9B,KACpB,CACgB,IAAK,KAAM,CACP8I,GAAOQ,EAAS,GAAGtJ,CAAK,EACxB,KACpB,CACgB,IAAK,MAAO,CACR8I,GAAOQ,EAAS,IAAItJ,CAAK,EACzB,KACpB,CACgB,IAAK,OAAQ,CACT8I,GAAOQ,EAAS,KAAKtJ,CAAK,EAC1B,KACpB,CACgB,QAAS,CACL,IAAM0H,EAAS,eAAiB1H,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM0H,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CACA,EC/LaS,EAAN,KAAa,CAChB,QACA,YAAY/I,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAClC,CACI,OAAO,iBAAmB,IAAI,IAAI,CAC9B,aACA,cACA,kBACR,CAAK,EAID,WAAWgN,EAAU,CACjB,OAAOA,CACf,CAII,YAAYrM,EAAM,CACd,OAAOA,CACf,CAII,iBAAiByD,EAAQ,CACrB,OAAOA,CACf,CACA,ECrBa6I,EAAN,KAAa,CAChB,SAAWlN,EAAY,EACvB,QAAU,KAAK,WACf,MAAQ,KAAKmN,GAAe7C,EAAO,IAAKmC,EAAQ,KAAK,EACrD,YAAc,KAAKU,GAAe7C,EAAO,UAAWmC,EAAQ,WAAW,EACvE,OAASA,EACT,SAAWpB,EACX,aAAemB,EACf,MAAQlC,EACR,UAAYtG,EACZ,MAAQgJ,EACR,eAAeI,EAAM,CACjB,KAAK,IAAI,GAAGA,CAAI,CACxB,CAII,WAAW/I,EAAQgJ,EAAU,CACzB,IAAIC,EAAS,CAAA,EACb,QAAW7J,KAASY,EAEhB,OADAiJ,EAASA,EAAO,OAAOD,EAAS,KAAK,KAAM5J,CAAK,CAAC,EACzCA,EAAM,KAAI,CACd,IAAK,QAAS,CACV,IAAM8J,EAAa9J,EACnB,QAAWiD,KAAQ6G,EAAW,OAC1BD,EAASA,EAAO,OAAO,KAAK,WAAW5G,EAAK,OAAQ2G,CAAQ,CAAC,EAEjE,QAAWpL,KAAOsL,EAAW,KACzB,QAAW7G,KAAQzE,EACfqL,EAASA,EAAO,OAAO,KAAK,WAAW5G,EAAK,OAAQ2G,CAAQ,CAAC,EAGrE,KACpB,CACgB,IAAK,OAAQ,CACT,IAAMG,EAAY/J,EAClB6J,EAASA,EAAO,OAAO,KAAK,WAAWE,EAAU,MAAOH,CAAQ,CAAC,EACjE,KACpB,CACgB,QAAS,CACL,IAAMT,EAAenJ,EACjB,KAAK,SAAS,YAAY,cAAcmJ,EAAa,IAAI,EACzD,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAASa,GAAgB,CAC7E,IAAMpJ,EAASuI,EAAaa,CAAW,EAAE,KAAK,GAAQ,EACtDH,EAASA,EAAO,OAAO,KAAK,WAAWjJ,EAAQgJ,CAAQ,CAAC,CACpF,CAAyB,EAEIT,EAAa,SAClBU,EAASA,EAAO,OAAO,KAAK,WAAWV,EAAa,OAAQS,CAAQ,CAAC,EAE7F,CACA,CAEQ,OAAOC,CACf,CACI,OAAOF,EAAM,CACT,IAAMM,EAAa,KAAK,SAAS,YAAc,CAAE,UAAW,CAAA,EAAI,YAAa,CAAA,CAAE,EAC/E,OAAAN,EAAK,QAASO,GAAS,CAEnB,IAAMC,EAAO,CAAE,GAAGD,CAAI,EA8DtB,GA5DAC,EAAK,MAAQ,KAAK,SAAS,OAASA,EAAK,OAAS,GAE9CD,EAAK,aACLA,EAAK,WAAW,QAASE,GAAQ,CAC7B,GAAI,CAACA,EAAI,KACL,MAAM,IAAI,MAAM,yBAAyB,EAE7C,GAAI,aAAcA,EAAK,CACnB,IAAMC,EAAeJ,EAAW,UAAUG,EAAI,IAAI,EAC9CC,EAEAJ,EAAW,UAAUG,EAAI,IAAI,EAAI,YAAaT,EAAM,CAChD,IAAIP,EAAMgB,EAAI,SAAS,MAAM,KAAMT,CAAI,EACvC,OAAIP,IAAQ,KACRA,EAAMiB,EAAa,MAAM,KAAMV,CAAI,GAEhCP,CACvC,EAG4Ba,EAAW,UAAUG,EAAI,IAAI,EAAIA,EAAI,QAEjE,CACoB,GAAI,cAAeA,EAAK,CACpB,GAAI,CAACA,EAAI,OAAUA,EAAI,QAAU,SAAWA,EAAI,QAAU,SACtD,MAAM,IAAI,MAAM,6CAA6C,EAEjE,IAAME,EAAWL,EAAWG,EAAI,KAAK,EACjCE,EACAA,EAAS,QAAQF,EAAI,SAAS,EAG9BH,EAAWG,EAAI,KAAK,EAAI,CAACA,EAAI,SAAS,EAEtCA,EAAI,QACAA,EAAI,QAAU,QACVH,EAAW,WACXA,EAAW,WAAW,KAAKG,EAAI,KAAK,EAGpCH,EAAW,WAAa,CAACG,EAAI,KAAK,EAGjCA,EAAI,QAAU,WACfH,EAAW,YACXA,EAAW,YAAY,KAAKG,EAAI,KAAK,EAGrCH,EAAW,YAAc,CAACG,EAAI,KAAK,GAIvE,CACwB,gBAAiBA,GAAOA,EAAI,cAC5BH,EAAW,YAAYG,EAAI,IAAI,EAAIA,EAAI,YAE/D,CAAiB,EACDD,EAAK,WAAaF,GAGlBC,EAAK,SAAU,CACf,IAAMZ,EAAW,KAAK,SAAS,UAAY,IAAI1B,EAAU,KAAK,QAAQ,EACtE,QAAW2C,KAAQL,EAAK,SAAU,CAC9B,GAAI,EAAEK,KAAQjB,GACV,MAAM,IAAI,MAAM,aAAaiB,CAAI,kBAAkB,EAEvD,GAAI,CAAC,UAAW,QAAQ,EAAE,SAASA,CAAI,EAEnC,SAEJ,IAAMC,EAAeD,EACjBE,EAAeP,EAAK,SAASM,CAAY,EACxCN,EAAK,iBAENO,EAAe,KAAKC,GAAyBD,EAAcD,EAAclB,CAAQ,GAErF,IAAMe,EAAef,EAASkB,CAAY,EAE1ClB,EAASkB,CAAY,EAAI,IAAIb,IAAS,CAClC,IAAIP,EAAMqB,EAAa,MAAMnB,EAAUK,CAAI,EAC3C,OAAIP,IAAQ,KACRA,EAAMiB,EAAa,MAAMf,EAAUK,CAAI,GAEpCP,GAAO,EACtC,CACA,CACgBe,EAAK,SAAWb,CAChC,CACY,GAAIY,EAAK,UAAW,CAChB,IAAMS,EAAY,KAAK,SAAS,WAAa,IAAIpK,EAAW,KAAK,QAAQ,EACzE,QAAWgK,KAAQL,EAAK,UAAW,CAC/B,GAAI,EAAEK,KAAQI,GACV,MAAM,IAAI,MAAM,cAAcJ,CAAI,kBAAkB,EAExD,GAAI,CAAC,UAAW,QAAS,OAAO,EAAE,SAASA,CAAI,EAE3C,SAEJ,IAAMK,EAAgBL,EAChBM,EAAgBX,EAAK,UAAUU,CAAa,EAC5CE,EAAgBH,EAAUC,CAAa,EAG7CD,EAAUC,CAAa,EAAI,IAAIjB,IAAS,CACpC,IAAIP,EAAMyB,EAAc,MAAMF,EAAWhB,CAAI,EAC7C,OAAIP,IAAQ,KACRA,EAAM0B,EAAc,MAAMH,EAAWhB,CAAI,GAEtCP,CAC/B,CACA,CACgBe,EAAK,UAAYQ,CACjC,CAEY,GAAIT,EAAK,MAAO,CACZ,IAAMa,EAAQ,KAAK,SAAS,OAAS,IAAIxB,EACzC,QAAWgB,KAAQL,EAAK,MAAO,CAC3B,GAAI,EAAEK,KAAQQ,GACV,MAAM,IAAI,MAAM,SAASR,CAAI,kBAAkB,EAEnD,GAAIA,IAAS,UAET,SAEJ,IAAMS,EAAYT,EACZU,EAAYf,EAAK,MAAMc,CAAS,EAChCE,EAAWH,EAAMC,CAAS,EAC5BzB,EAAO,iBAAiB,IAAIgB,CAAI,EAEhCQ,EAAMC,CAAS,EAAKG,GAAQ,CACxB,GAAI,KAAK,SAAS,MACd,OAAO,QAAQ,QAAQF,EAAU,KAAKF,EAAOI,CAAG,CAAC,EAAE,KAAK/B,GAC7C8B,EAAS,KAAKH,EAAO3B,CAAG,CAClC,EAEL,IAAMA,EAAM6B,EAAU,KAAKF,EAAOI,CAAG,EACrC,OAAOD,EAAS,KAAKH,EAAO3B,CAAG,CAC3D,EAIwB2B,EAAMC,CAAS,EAAI,IAAIrB,IAAS,CAC5B,IAAIP,EAAM6B,EAAU,MAAMF,EAAOpB,CAAI,EACrC,OAAIP,IAAQ,KACRA,EAAM8B,EAAS,MAAMH,EAAOpB,CAAI,GAE7BP,CACnC,CAEA,CACgBe,EAAK,MAAQY,CAC7B,CAEY,GAAIb,EAAK,WAAY,CACjB,IAAMkB,EAAa,KAAK,SAAS,WAC3BC,EAAiBnB,EAAK,WAC5BC,EAAK,WAAa,SAAUnK,EAAO,CAC/B,IAAI6J,EAAS,CAAA,EACb,OAAAA,EAAO,KAAKwB,EAAe,KAAK,KAAMrL,CAAK,CAAC,EACxCoL,IACAvB,EAASA,EAAO,OAAOuB,EAAW,KAAK,KAAMpL,CAAK,CAAC,GAEhD6J,CAC3B,CACA,CACY,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGM,CAAI,CACvD,CAAS,EACM,IACf,CAEIO,GAAyBY,EAAMf,EAAMjB,EAAU,CAC3C,OAAQiB,EAAI,CACR,IAAK,UACD,OAAO,SAAUvK,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMhC,EAAS,OAAO,YAAYtJ,EAAM,MAAM,EAAGA,EAAM,MAAO1C,GAASgM,EAAS,OAAO,YAAYtJ,EAAM,OAAQsJ,EAAS,OAAO,YAAY,CAAC,CAAC,CACpL,EACY,IAAK,OACD,OAAO,SAAUtJ,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,KAAMA,EAAM,KAAM,CAAC,CAACA,EAAM,OAAO,CAClF,EACY,IAAK,QACD,OAAO,SAAUA,EAAO,CACpB,GAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAG9B,OAAOe,EAAK,MAAM,KAAM,SAAS,EAErC,IAAI5C,EAAS,GAETzF,EAAO,GACX,QAASmF,EAAI,EAAGA,EAAIpI,EAAM,OAAO,OAAQoI,IACrCnF,GAAQ,KAAK,UAAU,CACnB,KAAMjD,EAAM,OAAOoI,CAAC,EAAE,KACtB,OAAQpI,EAAM,OAAOoI,CAAC,EAAE,OACxB,OAAQ,GACR,MAAOpI,EAAM,MAAMoI,CAAC,CAChD,CAAyB,EAELM,GAAU,KAAK,SAAS,CAAE,KAAMzF,CAAI,CAAE,EACtC,IAAIkF,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIpI,EAAM,KAAK,OAAQoI,IAAK,CACxC,IAAM5J,EAAMwB,EAAM,KAAKoI,CAAC,EACxBnF,EAAO,GACP,QAAS0F,EAAI,EAAGA,EAAInK,EAAI,OAAQmK,IAC5B1F,GAAQ,KAAK,UAAU,CACnB,KAAMzE,EAAImK,CAAC,EAAE,KACb,OAAQnK,EAAImK,CAAC,EAAE,OACf,OAAQ,GACR,MAAO3I,EAAM,MAAM2I,CAAC,CACpD,CAA6B,EAELR,GAAQ,KAAK,SAAS,CAAE,KAAMlF,CAAI,CAAE,CAC5D,CACoB,OAAOqI,EAAK,KAAK,KAAM5C,EAAQP,CAAI,CACvD,EACY,IAAK,aACD,OAAO,SAAUnI,EAAO,CACpB,GAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAG9B,OAAOe,EAAK,MAAM,KAAM,SAAS,EAErC,IAAMnD,EAAO,KAAK,OAAO,MAAMnI,EAAM,MAAM,EAC3C,OAAOsL,EAAK,KAAK,KAAMnD,CAAI,CAC/C,EACY,IAAK,OACD,OAAO,SAAUnI,EAAO,CACpB,GAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAG9B,OAAOe,EAAK,MAAM,KAAM,SAAS,EAErC,IAAMrD,EAAUjI,EAAM,QAChBkI,EAAQlI,EAAM,MACduL,EAAQvL,EAAM,MAChBmI,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIpI,EAAM,MAAM,OAAQoI,IAAK,CACzC,IAAMrF,EAAO/C,EAAM,MAAMoI,CAAC,EACpBK,EAAU1F,EAAK,QACfyI,EAAOzI,EAAK,KACdwF,EAAW,GACf,GAAIxF,EAAK,KAAM,CACX,IAAMyF,EAAW,KAAK,SAAS,CAAE,QAAS,CAAC,CAACC,CAAO,CAAE,EACjD8C,EACIxI,EAAK,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAS,aAClDA,EAAK,OAAO,CAAC,EAAE,KAAOyF,EAAW,IAAMzF,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SAC/FA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAOyF,EAAW,IAAMzF,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAI9EA,EAAK,OAAO,QAAQ,CAChB,KAAM,OACN,KAAMyF,EAAW,GACzD,CAAqC,EAILD,GAAYC,EAAW,GAEvD,CACwBD,GAAY,KAAK,OAAO,MAAMxF,EAAK,OAAQwI,CAAK,EAChDpD,GAAQ,KAAK,SAAS,CAClB,KAAM,YACN,IAAKI,EACL,KAAMA,EACN,KAAAiD,EACA,QAAS,CAAC,CAAC/C,EACX,MAAA8C,EACA,OAAQxI,EAAK,MACzC,CAAyB,CACzB,CACoB,OAAOuI,EAAK,KAAK,KAAMnD,EAAMF,EAASC,CAAK,CAC/D,EACY,IAAK,OACD,OAAO,SAAUlI,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,KAAMA,EAAM,KAAK,CAClE,EACY,IAAK,YACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAM,KAAK,OAAO,YAAYtL,EAAM,MAAM,CAAC,CAChF,EACY,IAAK,SACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,IAAI,CACrD,EACY,IAAK,OACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,KAAMA,EAAM,MAAO,KAAK,OAAO,YAAYA,EAAM,MAAM,CAAC,CACzG,EACY,IAAK,QACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,KAAMA,EAAM,MAAOA,EAAM,IAAI,CAC9E,EACY,IAAK,SACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAM,KAAK,OAAO,YAAYtL,EAAM,MAAM,CAAC,CAChF,EACY,IAAK,KACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAM,KAAK,OAAO,YAAYtL,EAAM,MAAM,CAAC,CAChF,EACY,IAAK,WACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,IAAI,CACrD,EACY,IAAK,MACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAM,KAAK,OAAO,YAAYtL,EAAM,MAAM,CAAC,CAChF,EACY,IAAK,OACD,OAAO,SAAUA,EAAO,CACpB,MAAI,CAACA,EAAM,MAAQA,EAAM,OAASuK,EAGvBe,EAAK,MAAM,KAAM,SAAS,EAE9BA,EAAK,KAAK,KAAMtL,EAAM,IAAI,CACrD,CAGA,CACQ,OAAOsL,CACf,CACI,WAAW1N,EAAK,CACZ,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGA,CAAG,EACnC,IACf,CACI,MAAM6C,EAAKD,EAAS,CAChB,OAAOqG,EAAO,IAAIpG,EAAKD,GAAW,KAAK,QAAQ,CACvD,CACI,OAAOI,EAAQJ,EAAS,CACpB,OAAOwI,EAAQ,MAAMpI,EAAQJ,GAAW,KAAK,QAAQ,CAC7D,CACIkJ,GAAe7J,EAAO4L,EAAQ,CAC1B,MAAO,CAAChL,EAAKD,IAAY,CACrB,IAAMkL,EAAU,CAAE,GAAGlL,CAAO,EACtB5C,EAAM,CAAE,GAAG,KAAK,SAAU,GAAG8N,CAAO,EAEtC,KAAK,SAAS,QAAU,IAAQA,EAAQ,QAAU,KAC7C9N,EAAI,QACL,QAAQ,KAAK,oHAAoH,EAErIA,EAAI,MAAQ,IAEhB,IAAM+N,EAAa,KAAKC,GAAS,CAAC,CAAChO,EAAI,OAAQ,CAAC,CAACA,EAAI,KAAK,EAE1D,GAAI,OAAO6C,EAAQ,KAAeA,IAAQ,KACtC,OAAOkL,EAAW,IAAI,MAAM,gDAAgD,CAAC,EAEjF,GAAI,OAAOlL,GAAQ,SACf,OAAOkL,EAAW,IAAI,MAAM,wCACtB,OAAO,UAAU,SAAS,KAAKlL,CAAG,EAAI,mBAAmB,CAAC,EAKpE,GAHI7C,EAAI,QACJA,EAAI,MAAM,QAAUA,GAEpBA,EAAI,MACJ,OAAO,QAAQ,QAAQA,EAAI,MAAQA,EAAI,MAAM,WAAW6C,CAAG,EAAIA,CAAG,EAC7D,KAAKA,GAAOZ,EAAMY,EAAK7C,CAAG,CAAC,EAC3B,KAAKgD,GAAUhD,EAAI,MAAQA,EAAI,MAAM,iBAAiBgD,CAAM,EAAIA,CAAM,EACtE,KAAKA,GAAUhD,EAAI,WAAa,QAAQ,IAAI,KAAK,WAAWgD,EAAQhD,EAAI,UAAU,CAAC,EAAE,KAAK,IAAMgD,CAAM,EAAIA,CAAM,EAChH,KAAKA,GAAU6K,EAAO7K,EAAQhD,CAAG,CAAC,EAClC,KAAKT,GAAQS,EAAI,MAAQA,EAAI,MAAM,YAAYT,CAAI,EAAIA,CAAI,EAC3D,MAAMwO,CAAU,EAEzB,GAAI,CACI/N,EAAI,QACJ6C,EAAM7C,EAAI,MAAM,WAAW6C,CAAG,GAElC,IAAIG,EAASf,EAAMY,EAAK7C,CAAG,EACvBA,EAAI,QACJgD,EAAShD,EAAI,MAAM,iBAAiBgD,CAAM,GAE1ChD,EAAI,YACJ,KAAK,WAAWgD,EAAQhD,EAAI,UAAU,EAE1C,IAAIT,EAAOsO,EAAO7K,EAAQhD,CAAG,EAC7B,OAAIA,EAAI,QACJT,EAAOS,EAAI,MAAM,YAAYT,CAAI,GAE9BA,CACvB,OACmB0O,EAAG,CACN,OAAOF,EAAWE,CAAC,CACnC,CACA,CACA,CACID,GAASE,EAAQC,EAAO,CACpB,OAAQF,GAAM,CAEV,GADAA,EAAE,SAAW;2DACTC,EAAQ,CACR,IAAME,EAAM,iCACN9O,EAAO2O,EAAE,QAAU,GAAI,EAAI,EAC3B,SACN,OAAIE,EACO,QAAQ,QAAQC,CAAG,EAEvBA,CACvB,CACY,GAAID,EACA,OAAO,QAAQ,OAAOF,CAAC,EAE3B,MAAMA,CAClB,CACA,CACA,EC1gBMI,EAAiB,IAAIxC,EACpB,SAASyC,EAAOzL,EAAK7C,EAAK,CAC7B,OAAOqO,EAAe,MAAMxL,EAAK7C,CAAG,CACxC,CAMAsO,EAAO,QACHA,EAAO,WAAa,SAAU1L,EAAS,CACnC,OAAAyL,EAAe,WAAWzL,CAAO,EACjC0L,EAAO,SAAWD,EAAe,SACjCxP,GAAeyP,EAAO,QAAQ,EACvBA,CACf,EAIAA,EAAO,YAAc3P,EACrB2P,EAAO,SAAW1P,EAIlB0P,EAAO,IAAM,YAAavC,EAAM,CAC5B,OAAAsC,EAAe,IAAI,GAAGtC,CAAI,EAC1BuC,EAAO,SAAWD,EAAe,SACjCxP,GAAeyP,EAAO,QAAQ,EACvBA,CACX,EAIAA,EAAO,WAAa,SAAUtL,EAAQgJ,EAAU,CAC5C,OAAOqC,EAAe,WAAWrL,EAAQgJ,CAAQ,CACrD,EAQAsC,EAAO,YAAcD,EAAe,YAIpCC,EAAO,OAASlD,EAChBkD,EAAO,OAASlD,EAAQ,MACxBkD,EAAO,SAAWtE,EAClBsE,EAAO,aAAenD,EACtBmD,EAAO,MAAQrF,EACfqF,EAAO,MAAQrF,EAAO,IACtBqF,EAAO,UAAY3L,EACnB2L,EAAO,MAAQ3C,EACf2C,EAAO,MAAQA,EACH,IAAC1L,GAAU0L,EAAO,QACjBC,GAAaD,EAAO,WACpBE,GAAMF,EAAO,IACbd,GAAac,EAAO,WACpBG,GAAcH,EAAO,YAEtB,IAACI,GAASC,EAAQ,MACjBC,GAAQC,EAAO,ICrD5B,SAASC,GAAmBC,EAAU,CAAE,iBAAAC,CAAiB,EAAG,CAE1D,IAAMC,EADYF,EAAS,QAAQ,UAAW;AAAA,CAAI,EACR,QAAQ,UAAW;AAAA,CAAI,EAC3DG,EAAqBC,GAAOF,CAAuB,EACzD,OAAID,IAAqB,GAChBE,EAAmB,QAAQ,KAAM,QAAQ,EAE3CA,CACT,CACAE,EAAON,GAAoB,oBAAoB,EAC/C,SAASO,GAAgBN,EAAUO,EAAS,CAAC,EAAG,CAC9C,IAAMC,EAAuBT,GAAmBC,EAAUO,CAAM,EAC1DE,EAAQC,EAAO,MAAMF,CAAoB,EACzCG,EAAQ,CAAC,CAAC,CAAC,EACbC,EAAc,EAClB,SAASC,EAAYC,EAAMC,EAAa,SAAU,CAC5CD,EAAK,OAAS,OACEA,EAAK,KAAK,MAAM;AAAA,CAAI,EAC5B,QAAQ,CAACE,EAAUC,IAAU,CACjCA,IAAU,IACZL,IACAD,EAAM,KAAK,CAAC,CAAC,GAEfK,EAAS,MAAM,GAAG,EAAE,QAASE,GAAS,CACpCA,EAAOA,EAAK,QAAQ,SAAU,GAAG,EAC7BA,GACFP,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASM,EAAM,KAAMH,CAAW,CAAC,CAE/D,CAAC,CACH,CAAC,EACQD,EAAK,OAAS,UAAYA,EAAK,OAAS,KACjDA,EAAK,OAAO,QAASK,GAAgB,CACnCN,EAAYM,EAAaL,EAAK,IAAI,CACpC,CAAC,EACQA,EAAK,OAAS,QACvBH,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASE,EAAK,KAAM,KAAM,QAAS,CAAC,CAElE,CACA,OAAAT,EAAOQ,EAAa,aAAa,EACjCJ,EAAM,QAASW,GAAa,CACtBA,EAAS,OAAS,YACpBA,EAAS,QAAQ,QAASD,GAAgB,CACxCN,EAAYM,CAAW,CACzB,CAAC,EACQC,EAAS,OAAS,QAC3BT,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASQ,EAAS,KAAM,KAAM,QAAS,CAAC,CAEtE,CAAC,EACMT,CACT,CACAN,EAAOC,GAAiB,iBAAiB,EACzC,SAASe,GAAerB,EAAU,CAAE,iBAAAC,CAAiB,EAAI,CAAC,EAAG,CAC3D,IAAMQ,EAAQC,EAAO,MAAMV,CAAQ,EACnC,SAASsB,EAAOR,EAAM,CACpB,OAAIA,EAAK,OAAS,OACZb,IAAqB,GAChBa,EAAK,KAAK,QAAQ,QAAS,OAAO,EAAE,QAAQ,KAAM,QAAQ,EAE5DA,EAAK,KAAK,QAAQ,QAAS,OAAO,EAChCA,EAAK,OAAS,SAChB,WAAWA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,YAC1CR,EAAK,OAAS,KAChB,OAAOA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,QACtCR,EAAK,OAAS,YAChB,MAAMA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,OACrCR,EAAK,OAAS,QAChB,GACEA,EAAK,OAAS,OAChB,GAAGA,EAAK,IAAI,GACVA,EAAK,OAAS,SAChBA,EAAK,KAEP,yBAAyBA,EAAK,IAAI,EAC3C,CACA,OAAAT,EAAOiB,EAAQ,QAAQ,EAChBb,EAAM,IAAIa,CAAM,EAAE,KAAK,EAAE,CAClC,CACAjB,EAAOgB,GAAgB,gBAAgB,EAGvC,SAASE,GAAiBC,EAAM,CAC9B,OAAI,KAAK,UACA,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQA,CAAI,CAAC,EAAE,IAAKC,GAAMA,EAAE,OAAO,EAE9D,CAAC,GAAGD,CAAI,CACjB,CACAnB,EAAOkB,GAAkB,kBAAkB,EAC3C,SAASG,GAAoBC,EAAUT,EAAM,CAC3C,IAAMU,EAAaL,GAAiBL,EAAK,OAAO,EAChD,OAAOW,EAA6BF,EAAU,CAAC,EAAGC,EAAYV,EAAK,IAAI,CACzE,CACAb,EAAOqB,GAAqB,qBAAqB,EACjD,SAASG,EAA6BF,EAAUG,EAAWC,EAAgBC,EAAM,CAC/E,GAAID,EAAe,SAAW,EAC5B,MAAO,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAAS,GAAI,KAAAA,CAAK,CACtB,EAEF,GAAM,CAACC,EAAU,GAAGC,CAAI,EAAIH,EACtBI,EAAU,CAAC,GAAGL,EAAWG,CAAQ,EACvC,OAAIN,EAAS,CAAC,CAAE,QAASQ,EAAQ,KAAK,EAAE,EAAG,KAAAH,CAAK,CAAC,CAAC,EACzCH,EAA6BF,EAAUQ,EAASD,EAAMF,CAAI,GAE/DF,EAAU,SAAW,GAAKG,IAC5BH,EAAU,KAAKG,CAAQ,EACvBF,EAAe,MAAM,GAEhB,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAASD,EAAe,KAAK,EAAE,EAAG,KAAAC,CAAK,CAC3C,EACF,CACA3B,EAAOwB,EAA8B,8BAA8B,EACnE,SAASO,GAAoBC,EAAMV,EAAU,CAC3C,GAAIU,EAAK,KAAK,CAAC,CAAE,QAAAC,CAAQ,IAAMA,EAAQ,SAAS;AAAA,CAAI,CAAC,EACnD,MAAM,IAAI,MAAM,2DAA2D,EAE7E,OAAOC,EAA6BF,EAAMV,CAAQ,CACpD,CACAtB,EAAO+B,GAAqB,qBAAqB,EACjD,SAASG,EAA6BC,EAAOb,EAAUhB,EAAQ,CAAC,EAAG8B,EAAU,CAAC,EAAG,CAC/E,GAAID,EAAM,SAAW,EACnB,OAAIC,EAAQ,OAAS,GACnB9B,EAAM,KAAK8B,CAAO,EAEb9B,EAAM,OAAS,EAAIA,EAAQ,CAAC,EAErC,IAAI+B,EAAS,GACTF,EAAM,CAAC,EAAE,UAAY,MACvBE,EAAS,IACTF,EAAM,MAAM,GAEd,IAAMG,EAAWH,EAAM,MAAM,GAAK,CAAE,QAAS,IAAK,KAAM,QAAS,EAC3DI,EAAmB,CAAC,GAAGH,CAAO,EAKpC,GAJIC,IAAW,IACbE,EAAiB,KAAK,CAAE,QAASF,EAAQ,KAAM,QAAS,CAAC,EAE3DE,EAAiB,KAAKD,CAAQ,EAC1BhB,EAASiB,CAAgB,EAC3B,OAAOL,EAA6BC,EAAOb,EAAUhB,EAAOiC,CAAgB,EAE9E,GAAIH,EAAQ,OAAS,EACnB9B,EAAM,KAAK8B,CAAO,EAClBD,EAAM,QAAQG,CAAQ,UACbA,EAAS,QAAS,CAC3B,GAAM,CAACN,EAAMH,CAAI,EAAIR,GAAoBC,EAAUgB,CAAQ,EAC3DhC,EAAM,KAAK,CAAC0B,CAAI,CAAC,EACbH,EAAK,SACPM,EAAM,QAAQN,CAAI,CAEtB,CACA,OAAOK,EAA6BC,EAAOb,EAAUhB,CAAK,CAC5D,CACAN,EAAOkC,EAA8B,8BAA8B,EAGnE,SAASM,EAAWC,EAAKC,EAAS,CAC5BA,GACFD,EAAI,KAAK,QAASC,CAAO,CAE7B,CACA1C,EAAOwC,EAAY,YAAY,EAC/B,eAAeG,GAAYC,EAASnC,EAAMoC,EAAOC,EAASC,EAAgB,GAAO,CAC/E,IAAMC,EAAKJ,EAAQ,OAAO,eAAe,EACzCI,EAAG,KAAK,QAAS,GAAG,GAAKH,CAAK,IAAI,EAClCG,EAAG,KAAK,SAAU,GAAG,GAAKH,CAAK,IAAI,EACnC,IAAMI,EAAMD,EAAG,OAAO,WAAW,EAC7BE,EAAQzC,EAAK,MACbA,EAAK,OAAS0C,EAAS1C,EAAK,KAAK,IACnCyC,EAAQ,MAAME,GAAY3C,EAAK,MAAM,QAAQ4C,GAAe,eAAgB;AAAA,CAAI,EAAGC,GAAU,CAAC,GAEhG,IAAMC,EAAa9C,EAAK,OAAS,YAAc,YACzC+C,EAAOP,EAAI,OAAO,MAAM,EAC9BO,EAAK,KAAKN,CAAK,EACfV,EAAWgB,EAAM/C,EAAK,UAAU,EAChC+C,EAAK,KAAK,QAAS,GAAGD,CAAU,IAAIT,CAAO,EAAE,EAC7CN,EAAWS,EAAKxC,EAAK,UAAU,EAC/BwC,EAAI,MAAM,UAAW,YAAY,EACjCA,EAAI,MAAM,cAAe,QAAQ,EACjCA,EAAI,MAAM,cAAe,KAAK,EAC9BA,EAAI,MAAM,YAAaJ,EAAQ,IAAI,EACnCI,EAAI,MAAM,aAAc,QAAQ,EAChCA,EAAI,KAAK,QAAS,8BAA8B,EAC5CF,GACFE,EAAI,KAAK,QAAS,UAAU,EAE9B,IAAIQ,EAAOR,EAAI,KAAK,EAAE,sBAAsB,EAC5C,OAAIQ,EAAK,QAAUZ,IACjBI,EAAI,MAAM,UAAW,OAAO,EAC5BA,EAAI,MAAM,cAAe,cAAc,EACvCA,EAAI,MAAM,QAASJ,EAAQ,IAAI,EAC/BY,EAAOR,EAAI,KAAK,EAAE,sBAAsB,GAEnCD,EAAG,KAAK,CACjB,CACAhD,EAAO2C,GAAa,aAAa,EACjC,SAASe,EAAYC,EAAaC,EAAWC,EAAY,CACvD,OAAOF,EAAY,OAAO,OAAO,EAAE,KAAK,QAAS,kBAAkB,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKC,EAAYC,EAAa,GAAM,IAAI,EAAE,KAAK,KAAMA,EAAa,IAAI,CAC/J,CACA7D,EAAO0D,EAAa,aAAa,EACjC,SAASI,GAAmBC,EAAYF,EAAY7B,EAAM,CACxD,IAAMgC,EAAcD,EAAW,OAAO,MAAM,EACtCE,EAAWP,EAAYM,EAAa,EAAGH,CAAU,EACvDK,EAA2BD,EAAUjC,CAAI,EACzC,IAAMmC,EAAaF,EAAS,KAAK,EAAE,sBAAsB,EACzD,OAAAD,EAAY,OAAO,EACZG,CACT,CACAnE,EAAO8D,GAAoB,oBAAoB,EAC/C,SAASM,GAAuBL,EAAYF,EAAY1C,EAAM,CAC5D,IAAM6C,EAAcD,EAAW,OAAO,MAAM,EACtCE,EAAWP,EAAYM,EAAa,EAAGH,CAAU,EACvDK,EAA2BD,EAAU,CAAC,CAAE,QAAS9C,EAAM,KAAM,QAAS,CAAC,CAAC,EACxE,IAAMkD,EAAgBJ,EAAS,KAAK,GAAG,sBAAsB,EAC7D,OAAII,GACFL,EAAY,OAAO,EAEdK,CACT,CACArE,EAAOoE,GAAwB,wBAAwB,EACvD,SAASE,GAAoBzB,EAAO0B,EAAGC,EAAgBzB,EAAgB,GAAO,CAE5E,IAAM0B,EAAaF,EAAE,OAAO,GAAG,EACzBG,EAAMD,EAAW,OAAO,MAAM,EAAE,KAAK,QAAS,YAAY,EAAE,KAAK,QAAS,cAAc,EACxFd,EAAcc,EAAW,OAAO,MAAM,EAAE,KAAK,IAAK,OAAO,EAC3Db,EAAY,EAChB,QAAW5B,KAAQwC,EAAgB,CACjC,IAAMG,EAA6B3E,EAAQ4E,GAAUd,GAAmBW,EAAY,IAAYG,CAAK,GAAK/B,EAAO,YAAY,EACvHgC,EAAkBF,EAAW3C,CAAI,EAAI,CAACA,CAAI,EAAID,GAAoBC,EAAM2C,CAAU,EACxF,QAAWG,KAAgBD,EAAiB,CAC1C,IAAME,EAAQrB,EAAYC,EAAaC,EAAW,GAAU,EAC5DM,EAA2Ba,EAAOD,CAAY,EAC9ClB,GACF,CACF,CACA,GAAIb,EAAe,CACjB,IAAMU,EAAOE,EAAY,KAAK,EAAE,QAAQ,EAClCqB,EAAU,EAChB,OAAAN,EAAI,KAAK,IAAKjB,EAAK,EAAIuB,CAAO,EAAE,KAAK,IAAKvB,EAAK,EAAIuB,CAAO,EAAE,KAAK,QAASvB,EAAK,MAAQ,EAAIuB,CAAO,EAAE,KAAK,SAAUvB,EAAK,OAAS,EAAIuB,CAAO,EACrIP,EAAW,KAAK,CACzB,KACE,QAAOd,EAAY,KAAK,CAE5B,CACA3D,EAAOsE,GAAqB,qBAAqB,EACjD,SAASJ,EAA2Ba,EAAOE,EAAa,CACtDF,EAAM,KAAK,EAAE,EACbE,EAAY,QAAQ,CAACpE,EAAMD,IAAU,CACnC,IAAMsE,EAAaH,EAAM,OAAO,OAAO,EAAE,KAAK,aAAclE,EAAK,OAAS,KAAO,SAAW,QAAQ,EAAE,KAAK,QAAS,kBAAkB,EAAE,KAAK,cAAeA,EAAK,OAAS,SAAW,OAAS,QAAQ,EAClMD,IAAU,EACZsE,EAAW,KAAKrE,EAAK,OAAO,EAE5BqE,EAAW,KAAK,IAAMrE,EAAK,OAAO,CAEtC,CAAC,CACH,CACAb,EAAOkE,EAA4B,4BAA4B,EAC/D,SAASiB,GAAqBhE,EAAM,CAClC,OAAOA,EAAK,QACV,wBAECC,GAAM,aAAaA,EAAE,QAAQ,IAAK,GAAG,CAAC,QACzC,CACF,CACApB,EAAOmF,GAAsB,sBAAsB,EACnD,IAAIC,GAA6BpF,EAAO,MAAOqF,EAAIlE,EAAO,GAAI,CAC5D,MAAAmE,EAAQ,GACR,QAAAC,EAAU,GACV,QAAAzC,EAAU,GACV,cAAA0C,EAAgB,GAChB,OAAAC,EAAS,GACT,MAAA5C,EAAQ,IACR,iBAAA6C,EAAmB,EACrB,EAAI,CAAC,EAAGxF,IAAW,CAYjB,GAXAyF,GAAI,MACF,iBACAxE,EACAmE,EACAC,EACAzC,EACA0C,EACAC,EACA,qBACAC,CACF,EACIF,EAAe,CACjB,IAAMI,EAAW5E,GAAeG,EAAMjB,CAAM,EACtC2F,EAAsBV,GAAqBW,GAAeF,CAAQ,CAAC,EACnEG,EAAgB5E,EAAK,QAAQ,QAAS,IAAI,EAC1CV,EAAO,CACX,OAAAgF,EACA,MAAOtC,EAAShC,CAAI,EAAI4E,EAAgBF,EACxC,WAAYP,EAAM,QAAQ,QAAS,QAAQ,CAC7C,EAEA,OADmB,MAAM3C,GAAY0C,EAAI5E,EAAMoC,EAAOC,EAAS4C,CAAgB,CAEjF,KAAO,CACL,IAAMM,EAAa7E,EAAK,QAAQ,cAAe,OAAO,EAChDqD,EAAiBvE,GAAgB+F,EAAW,QAAQ,OAAQ,OAAO,EAAG9F,CAAM,EAC5E+F,EAAW3B,GACfzB,EACAwC,EACAb,EACArD,EAAOuE,EAAmB,EAC5B,EACA,GAAID,EAAQ,CACN,UAAU,KAAKH,CAAK,IACtBA,EAAQA,EAAM,QAAQ,UAAW,YAAY,GAE/C,IAAMY,EAAqBZ,EAAM,QAAQ,kBAAmB,EAAE,EAAE,QAAQ,wBAAyB,EAAE,EAAE,QAAQ,gBAAiB,EAAE,EAAE,QAAQ,UAAW,OAAO,EAC5Ja,EAAOF,CAAQ,EAAE,KAAK,QAASC,CAAkB,CACnD,KAAO,CACL,IAAME,EAAqBd,EAAM,QAAQ,kBAAmB,EAAE,EAAE,QAAQ,wBAAyB,EAAE,EAAE,QAAQ,gBAAiB,EAAE,EAAE,QAAQ,eAAgB,OAAO,EACjKa,EAAOF,CAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,QAASG,EAAmB,QAAQ,eAAgB,OAAO,CAAC,EACjG,IAAMC,EAAqBf,EAAM,QAAQ,kBAAmB,EAAE,EAAE,QAAQ,wBAAyB,EAAE,EAAE,QAAQ,gBAAiB,EAAE,EAAE,QAAQ,UAAW,OAAO,EAC5Ja,EAAOF,CAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,QAASI,CAAkB,CAClE,CACA,OAAOJ,CACT,CACF,EAAG,YAAY", "names": ["dedent", "templ", "values", "_i", "strings", "indentLengths", "arr", "str", "matches", "match", "_a", "_b", "pattern_1", "string", "value", "i", "endentations", "endentation", "indentedValue", "_getDefaults", "_defaults", "changeDefaults", "newDefaults", "escapeTest", "escapeReplace", "escapeTestNoEncode", "escapeReplaceNoEncode", "escapeReplacements", "getEscapeReplacement", "ch", "escape", "html", "encode", "unescapeTest", "unescape", "_", "n", "caret", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "cleanUrl", "href", "noopTest", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "findClosingBracket", "b", "level", "outputLink", "cap", "link", "raw", "lexer", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "src", "trimmed", "lines", "tokens", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "lastToken", "oldToken", "newText", "newToken", "bull", "isordered", "list", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "nextLine", "blankLine", "indent", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "rawLine", "istask", "ischecked", "spacers", "hasMultipleLineBreaks", "tag", "headers", "aligns", "rows", "item", "align", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheading", "_paragraph", "blockText", "_blockLabel", "def", "_tag", "_comment", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "inlineCode", "br", "inlineText", "_punctuation", "punctuation", "blockSkip", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongRDelim<PERSON>t", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "_inlineLabel", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "_<PERSON>er", "__<PERSON><PERSON>", "rules", "next", "lastParagraphClipped", "leading", "tabs", "cutSrc", "extTokenizer", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "keepPrevChar", "_Renderer", "lang", "langString", "code", "depth", "ordered", "start", "body", "j", "type", "startAttr", "itemBody", "checkbox", "checked", "header", "k", "content", "cleanHref", "out", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "__<PERSON><PERSON><PERSON>", "anyToken", "genericToken", "ret", "textToken", "renderer", "_Hooks", "markdown", "Marked", "#parseMarkdown", "args", "callback", "values", "tableToken", "listToken", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "extLevel", "prop", "rendererProp", "rendererFunc", "#convertRendererFunction", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "func", "loose", "task", "parser", "origOpt", "throwError", "#onError", "e", "silent", "async", "msg", "markedInstance", "marked", "setOptions", "use", "parseInline", "parser", "_<PERSON><PERSON>r", "lexer", "_<PERSON>er", "preprocessMarkdown", "markdown", "markdownAutoWrap", "withoutMultipleNewlines", "withoutExtraSpaces", "dedent", "__name", "markdownToLines", "config", "preprocessedMarkdown", "nodes", "marked", "lines", "currentLine", "processNode", "node", "parentType", "textLine", "index", "word", "contentNode", "treeNode", "markdownToHTML", "output", "splitTextToChars", "text", "s", "splitWordToFitWidth", "checkFit", "characters", "splitWordToFitWidthRecursion", "usedChars", "remainingChars", "type", "nextChar", "rest", "newWord", "splitLineToFitWidth", "line", "content", "splitLineToFitWidthRecursion", "words", "newLine", "joiner", "nextWord", "lineWithNextWord", "applyStyle", "dom", "styleFn", "addHtmlSpan", "element", "width", "classes", "addBackground", "fo", "div", "label", "hasKatex", "renderKatex", "common_default", "getConfig2", "labelClass", "span", "bbox", "createTspan", "textElement", "lineIndex", "lineHeight", "computeWidthOfText", "parentNode", "testElement", "testSpan", "updateTextContentAndStyles", "textLength", "computeDimensionOfText", "textDimension", "createFormattedText", "g", "structuredText", "labelGroup", "bkg", "checkWidth", "line2", "linesUnderWidth", "preparedLine", "tspan", "padding", "wrappedLine", "innerTspan", "replaceIconSubstring", "createText", "el", "style", "isTitle", "useHtmlLabels", "isNode", "addSvgBackground", "log", "htmlText", "decodedReplacedText", "decodeEntities", "inputForKatex", "sanitizeBR", "svgLabel", "nodeLabelTextStyle", "select_default", "edgeLabelRectStyle", "edgeLabelTextStyle"]}