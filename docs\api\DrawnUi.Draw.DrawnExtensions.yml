### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DrawnExtensions
  commentId: T:DrawnUi.Draw.DrawnExtensions
  id: DrawnExtensions
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,DrawnUi.Draw.FontWeight)
  - DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,System.Int32)
  - DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl,System.Action{System.Double},System.Double,System.Double,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  - DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  - DrawnUi.Draw.DrawnExtensions.StartupSettings
  - DrawnUi.Draw.DrawnExtensions.UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder,DrawnUi.Draw.DrawnUiStartupSettings)
  langs:
  - csharp
  - vb
  name: DrawnExtensions
  nameWithType: DrawnExtensions
  fullName: DrawnUi.Draw.DrawnExtensions
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawnExtensions
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static class DrawnExtensions
    content.vb: Public Module DrawnExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.DrawnExtensions.StartupSettings
  commentId: P:DrawnUi.Draw.DrawnExtensions.StartupSettings
  id: StartupSettings
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: StartupSettings
  nameWithType: DrawnExtensions.StartupSettings
  fullName: DrawnUi.Draw.DrawnExtensions.StartupSettings
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartupSettings
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static DrawnUiStartupSettings StartupSettings { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.DrawnUiStartupSettings
    content.vb: Public Shared Property StartupSettings As DrawnUiStartupSettings
  overload: DrawnUi.Draw.DrawnExtensions.StartupSettings*
- uid: DrawnUi.Draw.DrawnExtensions.UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder,DrawnUi.Draw.DrawnUiStartupSettings)
  commentId: M:DrawnUi.Draw.DrawnExtensions.UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder,DrawnUi.Draw.DrawnUiStartupSettings)
  id: UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder,DrawnUi.Draw.DrawnUiStartupSettings)
  isExtensionMethod: true
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: UseDrawnUi(MauiAppBuilder, DrawnUiStartupSettings)
  nameWithType: DrawnExtensions.UseDrawnUi(MauiAppBuilder, DrawnUiStartupSettings)
  fullName: DrawnUi.Draw.DrawnExtensions.UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder, DrawnUi.Draw.DrawnUiStartupSettings)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UseDrawnUi
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static MauiAppBuilder UseDrawnUi(this MauiAppBuilder builder, DrawnUiStartupSettings settings = null)
    parameters:
    - id: builder
      type: Microsoft.Maui.Hosting.MauiAppBuilder
    - id: settings
      type: DrawnUi.Draw.DrawnUiStartupSettings
    return:
      type: Microsoft.Maui.Hosting.MauiAppBuilder
    content.vb: Public Shared Function UseDrawnUi(builder As MauiAppBuilder, settings As DrawnUiStartupSettings = Nothing) As MauiAppBuilder
  overload: DrawnUi.Draw.DrawnExtensions.UseDrawnUi*
- uid: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,System.Int32)
  commentId: M:DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,System.Int32)
  id: AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: AddFont(IFontCollection, string, string, int)
  nameWithType: DrawnExtensions.AddFont(IFontCollection, string, string, int)
  fullName: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection, string, string, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddFont
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 451
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static IFontCollection AddFont(this IFontCollection fontCollection, string filename, string alias, int weight)
    parameters:
    - id: fontCollection
      type: Microsoft.Maui.Hosting.IFontCollection
    - id: filename
      type: System.String
    - id: alias
      type: System.String
    - id: weight
      type: System.Int32
    return:
      type: Microsoft.Maui.Hosting.IFontCollection
    content.vb: Public Shared Function AddFont(fontCollection As IFontCollection, filename As String, [alias] As String, weight As Integer) As IFontCollection
  overload: DrawnUi.Draw.DrawnExtensions.AddFont*
  nameWithType.vb: DrawnExtensions.AddFont(IFontCollection, String, String, Integer)
  fullName.vb: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection, String, String, Integer)
  name.vb: AddFont(IFontCollection, String, String, Integer)
- uid: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,DrawnUi.Draw.FontWeight)
  commentId: M:DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,DrawnUi.Draw.FontWeight)
  id: AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,DrawnUi.Draw.FontWeight)
  isExtensionMethod: true
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: AddFont(IFontCollection, string, string, FontWeight)
  nameWithType: DrawnExtensions.AddFont(IFontCollection, string, string, FontWeight)
  fullName: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection, string, string, DrawnUi.Draw.FontWeight)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddFont
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 461
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static IFontCollection AddFont(this IFontCollection fontCollection, string filename, string alias, FontWeight weight)
    parameters:
    - id: fontCollection
      type: Microsoft.Maui.Hosting.IFontCollection
    - id: filename
      type: System.String
    - id: alias
      type: System.String
    - id: weight
      type: DrawnUi.Draw.FontWeight
    return:
      type: Microsoft.Maui.Hosting.IFontCollection
    content.vb: Public Shared Function AddFont(fontCollection As IFontCollection, filename As String, [alias] As String, weight As FontWeight) As IFontCollection
  overload: DrawnUi.Draw.DrawnExtensions.AddFont*
  nameWithType.vb: DrawnExtensions.AddFont(IFontCollection, String, String, FontWeight)
  fullName.vb: DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection, String, String, DrawnUi.Draw.FontWeight)
  name.vb: AddFont(IFontCollection, String, String, FontWeight)
- uid: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl,System.Action{System.Double},System.Double,System.Double,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  commentId: M:DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl,System.Action{System.Double},System.Double,System.Double,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  id: AnimateRangeAsync(DrawnUi.Draw.SkiaControl,System.Action{System.Double},System.Double,System.Double,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  isExtensionMethod: true
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: AnimateRangeAsync(SkiaControl, Action<double>, double, double, uint, Easing, CancellationTokenSource)
  nameWithType: DrawnExtensions.AnimateRangeAsync(SkiaControl, Action<double>, double, double, uint, Easing, CancellationTokenSource)
  fullName: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl, System.Action<double>, double, double, uint, Microsoft.Maui.Easing, System.Threading.CancellationTokenSource)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AnimateRangeAsync
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 475
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Task AnimateRangeAsync(this SkiaControl owner, Action<double> callback, double start, double end, uint length = 250, Easing easing = null, CancellationTokenSource _cancelTranslate = null)
    parameters:
    - id: owner
      type: DrawnUi.Draw.SkiaControl
    - id: callback
      type: System.Action{System.Double}
    - id: start
      type: System.Double
    - id: end
      type: System.Double
    - id: length
      type: System.UInt32
    - id: easing
      type: Microsoft.Maui.Easing
    - id: _cancelTranslate
      type: System.Threading.CancellationTokenSource
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Shared Function AnimateRangeAsync(owner As SkiaControl, callback As Action(Of Double), start As Double, [end] As Double, length As UInteger = 250, easing As Easing = Nothing, _cancelTranslate As CancellationTokenSource = Nothing) As Task
  overload: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync*
  nameWithType.vb: DrawnExtensions.AnimateRangeAsync(SkiaControl, Action(Of Double), Double, Double, UInteger, Easing, CancellationTokenSource)
  fullName.vb: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl, System.Action(Of Double), Double, Double, UInteger, Microsoft.Maui.Easing, System.Threading.CancellationTokenSource)
  name.vb: AnimateRangeAsync(SkiaControl, Action(Of Double), Double, Double, UInteger, Easing, CancellationTokenSource)
- uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  commentId: M:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  id: GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  isExtensionMethod: true
  parent: DrawnUi.Draw.DrawnExtensions
  langs:
  - csharp
  - vb
  name: GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  nameWithType: DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase, DrawnUi.Draw.ISkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetVelocityRatioForChild
    path: ../src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs
    startLine: 512
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static (float RatioX, float RatioY) GetVelocityRatioForChild(this IDrawnBase container, ISkiaControl control)
    parameters:
    - id: container
      type: DrawnUi.Draw.IDrawnBase
    - id: control
      type: DrawnUi.Draw.ISkiaControl
    return:
      type: System.ValueTuple{System.Single,System.Single}
    content.vb: Public Shared Function GetVelocityRatioForChild(container As IDrawnBase, control As ISkiaControl) As (RatioX As Single, RatioY As Single)
  overload: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.DrawnExtensions.StartupSettings*
  commentId: Overload:DrawnUi.Draw.DrawnExtensions.StartupSettings
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_StartupSettings
  name: StartupSettings
  nameWithType: DrawnExtensions.StartupSettings
  fullName: DrawnUi.Draw.DrawnExtensions.StartupSettings
- uid: DrawnUi.Draw.DrawnUiStartupSettings
  commentId: T:DrawnUi.Draw.DrawnUiStartupSettings
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnUiStartupSettings.html
  name: DrawnUiStartupSettings
  nameWithType: DrawnUiStartupSettings
  fullName: DrawnUi.Draw.DrawnUiStartupSettings
- uid: DrawnUi.Draw.DrawnExtensions.UseDrawnUi*
  commentId: Overload:DrawnUi.Draw.DrawnExtensions.UseDrawnUi
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_UseDrawnUi_Microsoft_Maui_Hosting_MauiAppBuilder_DrawnUi_Draw_DrawnUiStartupSettings_
  name: UseDrawnUi
  nameWithType: DrawnExtensions.UseDrawnUi
  fullName: DrawnUi.Draw.DrawnExtensions.UseDrawnUi
- uid: Microsoft.Maui.Hosting.MauiAppBuilder
  commentId: T:Microsoft.Maui.Hosting.MauiAppBuilder
  parent: Microsoft.Maui.Hosting
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.mauiappbuilder
  name: MauiAppBuilder
  nameWithType: MauiAppBuilder
  fullName: Microsoft.Maui.Hosting.MauiAppBuilder
- uid: Microsoft.Maui.Hosting
  commentId: N:Microsoft.Maui.Hosting
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Hosting
  nameWithType: Microsoft.Maui.Hosting
  fullName: Microsoft.Maui.Hosting
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Hosting
    name: Hosting
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Hosting
    name: Hosting
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting
- uid: DrawnUi.Draw.DrawnExtensions.AddFont*
  commentId: Overload:DrawnUi.Draw.DrawnExtensions.AddFont
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_AddFont_Microsoft_Maui_Hosting_IFontCollection_System_String_System_String_System_Int32_
  name: AddFont
  nameWithType: DrawnExtensions.AddFont
  fullName: DrawnUi.Draw.DrawnExtensions.AddFont
- uid: Microsoft.Maui.Hosting.IFontCollection
  commentId: T:Microsoft.Maui.Hosting.IFontCollection
  parent: Microsoft.Maui.Hosting
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.ifontcollection
  name: IFontCollection
  nameWithType: IFontCollection
  fullName: Microsoft.Maui.Hosting.IFontCollection
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.FontWeight
  commentId: T:DrawnUi.Draw.FontWeight
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FontWeight.html
  name: FontWeight
  nameWithType: FontWeight
  fullName: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync*
  commentId: Overload:DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_DrawnUi_Draw_SkiaControl_System_Action_System_Double__System_Double_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_
  name: AnimateRangeAsync
  nameWithType: DrawnExtensions.AnimateRangeAsync
  fullName: DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Action{System.Double}
  commentId: T:System.Action{System.Double}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<double>
  nameWithType: Action<double>
  fullName: System.Action<double>
  nameWithType.vb: Action(Of Double)
  fullName.vb: System.Action(Of Double)
  name.vb: Action(Of Double)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: System.UInt32
  commentId: T:System.UInt32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.uint32
  name: uint
  nameWithType: uint
  fullName: uint
  nameWithType.vb: UInteger
  fullName.vb: UInteger
  name.vb: UInteger
- uid: Microsoft.Maui.Easing
  commentId: T:Microsoft.Maui.Easing
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.easing
  name: Easing
  nameWithType: Easing
  fullName: Microsoft.Maui.Easing
- uid: System.Threading.CancellationTokenSource
  commentId: T:System.Threading.CancellationTokenSource
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource
  name: CancellationTokenSource
  nameWithType: CancellationTokenSource
  fullName: System.Threading.CancellationTokenSource
- uid: System.Threading.Tasks.Task
  commentId: T:System.Threading.Tasks.Task
  parent: System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task
  name: Task
  nameWithType: Task
  fullName: System.Threading.Tasks.Task
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: System.Threading
  commentId: N:System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading
  nameWithType: System.Threading
  fullName: System.Threading
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
- uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild*
  commentId: Overload:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  name: GetVelocityRatioForChild
  nameWithType: DrawnExtensions.GetVelocityRatioForChild
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: DrawnUi.Draw.ISkiaControl
  commentId: T:DrawnUi.Draw.ISkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaControl.html
  name: ISkiaControl
  nameWithType: ISkiaControl
  fullName: DrawnUi.Draw.ISkiaControl
- uid: System.ValueTuple{System.Single,System.Single}
  commentId: T:System.ValueTuple{System.Single,System.Single}
  parent: System
  definition: System.ValueTuple`2
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: (float X, float Y)
  nameWithType: (float X, float Y)
  fullName: (float X, float Y)
  nameWithType.vb: (X As Single, Y As Single)
  fullName.vb: (X As Single, Y As Single)
  name.vb: (X As Single, Y As Single)
  spec.csharp:
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: " "
  - uid: System.ValueTuple{System.Single,System.Single}.X
    name: X
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.x
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: " "
  - uid: System.ValueTuple{System.Single,System.Single}.Y
    name: Y
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.y
  - name: )
  spec.vb:
  - name: (
  - uid: System.ValueTuple{System.Single,System.Single}.X
    name: X
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.x
  - name: " "
  - name: As
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.ValueTuple{System.Single,System.Single}.Y
    name: Y
    href: https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.y
  - name: " "
  - name: As
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: System.ValueTuple`2
  commentId: T:System.ValueTuple`2
  name: (T1, T2)
  nameWithType: (T1, T2)
  fullName: (T1, T2)
  spec.csharp:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
  spec.vb:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
