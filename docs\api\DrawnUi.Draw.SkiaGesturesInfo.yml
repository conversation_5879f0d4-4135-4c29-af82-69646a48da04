### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaGesturesInfo
  commentId: T:DrawnUi.Draw.SkiaGesturesInfo
  id: SkiaGesturesInfo
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaGesturesInfo.Args
  - DrawnUi.Draw.SkiaGesturesInfo.Consumed
  - DrawnUi.Draw.SkiaGesturesInfo.Create(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  - DrawnUi.Draw.SkiaGesturesInfo.Info
  langs:
  - csharp
  - vb
  name: SkiaGesturesInfo
  nameWithType: SkiaGesturesInfo
  fullName: DrawnUi.Draw.SkiaGesturesInfo
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaGesturesInfo
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkiaGesturesInfo
    content.vb: Public Class SkiaGesturesInfo
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaGesturesInfo.Args
  commentId: P:DrawnUi.Draw.SkiaGesturesInfo.Args
  id: Args
  parent: DrawnUi.Draw.SkiaGesturesInfo
  langs:
  - csharp
  - vb
  name: Args
  nameWithType: SkiaGesturesInfo.Args
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Args
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Args
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaGesturesParameters Args { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaGesturesParameters
    content.vb: Public Property Args As SkiaGesturesParameters
  overload: DrawnUi.Draw.SkiaGesturesInfo.Args*
- uid: DrawnUi.Draw.SkiaGesturesInfo.Info
  commentId: P:DrawnUi.Draw.SkiaGesturesInfo.Info
  id: Info
  parent: DrawnUi.Draw.SkiaGesturesInfo
  langs:
  - csharp
  - vb
  name: Info
  nameWithType: SkiaGesturesInfo.Info
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Info
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Info
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public GestureEventProcessingInfo Info { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.GestureEventProcessingInfo
    content.vb: Public Property Info As GestureEventProcessingInfo
  overload: DrawnUi.Draw.SkiaGesturesInfo.Info*
- uid: DrawnUi.Draw.SkiaGesturesInfo.Consumed
  commentId: P:DrawnUi.Draw.SkiaGesturesInfo.Consumed
  id: Consumed
  parent: DrawnUi.Draw.SkiaGesturesInfo
  langs:
  - csharp
  - vb
  name: Consumed
  nameWithType: SkiaGesturesInfo.Consumed
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Consumed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Consumed
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Consumed { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Consumed As Boolean
  overload: DrawnUi.Draw.SkiaGesturesInfo.Consumed*
- uid: DrawnUi.Draw.SkiaGesturesInfo.Create(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  commentId: M:DrawnUi.Draw.SkiaGesturesInfo.Create(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  id: Create(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  parent: DrawnUi.Draw.SkiaGesturesInfo
  langs:
  - csharp
  - vb
  name: Create(SkiaGesturesParameters, GestureEventProcessingInfo)
  nameWithType: SkiaGesturesInfo.Create(SkiaGesturesParameters, GestureEventProcessingInfo)
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Create(DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Create
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static SkiaGesturesInfo Create(SkiaGesturesParameters args, GestureEventProcessingInfo info)
    parameters:
    - id: args
      type: DrawnUi.Draw.SkiaGesturesParameters
    - id: info
      type: DrawnUi.Draw.GestureEventProcessingInfo
    return:
      type: DrawnUi.Draw.SkiaGesturesInfo
    content.vb: Public Shared Function Create(args As SkiaGesturesParameters, info As GestureEventProcessingInfo) As SkiaGesturesInfo
  overload: DrawnUi.Draw.SkiaGesturesInfo.Create*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaGesturesInfo.Args*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesInfo.Args
  href: DrawnUi.Draw.SkiaGesturesInfo.html#DrawnUi_Draw_SkiaGesturesInfo_Args
  name: Args
  nameWithType: SkiaGesturesInfo.Args
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Args
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Draw.SkiaGesturesInfo.Info*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesInfo.Info
  href: DrawnUi.Draw.SkiaGesturesInfo.html#DrawnUi_Draw_SkiaGesturesInfo_Info
  name: Info
  nameWithType: SkiaGesturesInfo.Info
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Info
- uid: DrawnUi.Draw.GestureEventProcessingInfo
  commentId: T:DrawnUi.Draw.GestureEventProcessingInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GestureEventProcessingInfo.html
  name: GestureEventProcessingInfo
  nameWithType: GestureEventProcessingInfo
  fullName: DrawnUi.Draw.GestureEventProcessingInfo
- uid: DrawnUi.Draw.SkiaGesturesInfo.Consumed*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesInfo.Consumed
  href: DrawnUi.Draw.SkiaGesturesInfo.html#DrawnUi_Draw_SkiaGesturesInfo_Consumed
  name: Consumed
  nameWithType: SkiaGesturesInfo.Consumed
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Consumed
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SkiaGesturesInfo.Create*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesInfo.Create
  href: DrawnUi.Draw.SkiaGesturesInfo.html#DrawnUi_Draw_SkiaGesturesInfo_Create_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_
  name: Create
  nameWithType: SkiaGesturesInfo.Create
  fullName: DrawnUi.Draw.SkiaGesturesInfo.Create
- uid: DrawnUi.Draw.SkiaGesturesInfo
  commentId: T:DrawnUi.Draw.SkiaGesturesInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesInfo.html
  name: SkiaGesturesInfo
  nameWithType: SkiaGesturesInfo
  fullName: DrawnUi.Draw.SkiaGesturesInfo
