import{a as Ut}from"./chunk-3Z74ZUXG.min.js";import{e as Xt}from"./chunk-U4DUTLYF.min.js";import"./chunk-IQQ46AC6.min.js";import{a as Kt}from"./chunk-CLIYZZ5Y.min.js";import{a as Ft}from"./chunk-V55NTXQN.min.js";import{c as Ht,d as mt}from"./chunk-AUO2PXKS.min.js";import{d as Yt,m as tt,o as nt}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{D as $,Fa as Wt,H as yt,I as X,Ja as Pt,N as zt,O as At,S as Mt,Z as I,b as Ot,c as Rt,h as d,ia as N,j as k}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var wt=function(){var e=d(function(D,x,g,f){for(g=g||{},f=D.length;f--;g[D[f]]=x);return g},"o"),t=[1,7],s=[1,13],n=[1,14],i=[1,15],r=[1,19],a=[1,16],l=[1,17],c=[1,18],u=[8,30],h=[8,21,28,29,30,31,32,40,44,47],b=[1,23],m=[1,24],y=[8,15,16,21,28,29,30,31,32,40,44,47],L=[8,15,16,21,27,28,29,30,31,32,40,44,47],E=[1,49],S={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(x,g,f,w,v,o,W){var p=o.length-1;switch(v){case 4:w.getLogger().debug("Rule: separator (NL) ");break;case 5:w.getLogger().debug("Rule: separator (Space) ");break;case 6:w.getLogger().debug("Rule: separator (EOF) ");break;case 7:w.getLogger().debug("Rule: hierarchy: ",o[p-1]),w.setHierarchy(o[p-1]);break;case 8:w.getLogger().debug("Stop NL ");break;case 9:w.getLogger().debug("Stop EOF ");break;case 10:w.getLogger().debug("Stop NL2 ");break;case 11:w.getLogger().debug("Stop EOF2 ");break;case 12:w.getLogger().debug("Rule: statement: ",o[p]),typeof o[p].length=="number"?this.$=o[p]:this.$=[o[p]];break;case 13:w.getLogger().debug("Rule: statement #2: ",o[p-1]),this.$=[o[p-1]].concat(o[p]);break;case 14:w.getLogger().debug("Rule: link: ",o[p],x),this.$={edgeTypeStr:o[p],label:""};break;case 15:w.getLogger().debug("Rule: LABEL link: ",o[p-3],o[p-1],o[p]),this.$={edgeTypeStr:o[p],label:o[p-1]};break;case 18:let R=parseInt(o[p]),G=w.generateId();this.$={id:G,type:"space",label:"",width:R,children:[]};break;case 23:w.getLogger().debug("Rule: (nodeStatement link node) ",o[p-2],o[p-1],o[p]," typestr: ",o[p-1].edgeTypeStr);let V=w.edgeStrToEdgeData(o[p-1].edgeTypeStr);this.$=[{id:o[p-2].id,label:o[p-2].label,type:o[p-2].type,directions:o[p-2].directions},{id:o[p-2].id+"-"+o[p].id,start:o[p-2].id,end:o[p].id,label:o[p-1].label,type:"edge",directions:o[p].directions,arrowTypeEnd:V,arrowTypeStart:"arrow_open"},{id:o[p].id,label:o[p].label,type:w.typeStr2Type(o[p].typeStr),directions:o[p].directions}];break;case 24:w.getLogger().debug("Rule: nodeStatement (abc88 node size) ",o[p-1],o[p]),this.$={id:o[p-1].id,label:o[p-1].label,type:w.typeStr2Type(o[p-1].typeStr),directions:o[p-1].directions,widthInColumns:parseInt(o[p],10)};break;case 25:w.getLogger().debug("Rule: nodeStatement (node) ",o[p]),this.$={id:o[p].id,label:o[p].label,type:w.typeStr2Type(o[p].typeStr),directions:o[p].directions,widthInColumns:1};break;case 26:w.getLogger().debug("APA123",this?this:"na"),w.getLogger().debug("COLUMNS: ",o[p]),this.$={type:"column-setting",columns:o[p]==="auto"?-1:parseInt(o[p])};break;case 27:w.getLogger().debug("Rule: id-block statement : ",o[p-2],o[p-1]);let Bt=w.generateId();this.$={...o[p-2],type:"composite",children:o[p-1]};break;case 28:w.getLogger().debug("Rule: blockStatement : ",o[p-2],o[p-1],o[p]);let at=w.generateId();this.$={id:at,type:"composite",label:"",children:o[p-1]};break;case 29:w.getLogger().debug("Rule: node (NODE_ID separator): ",o[p]),this.$={id:o[p]};break;case 30:w.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",o[p-1],o[p]),this.$={id:o[p-1],label:o[p].label,typeStr:o[p].typeStr,directions:o[p].directions};break;case 31:w.getLogger().debug("Rule: dirList: ",o[p]),this.$=[o[p]];break;case 32:w.getLogger().debug("Rule: dirList: ",o[p-1],o[p]),this.$=[o[p-1]].concat(o[p]);break;case 33:w.getLogger().debug("Rule: nodeShapeNLabel: ",o[p-2],o[p-1],o[p]),this.$={typeStr:o[p-2]+o[p],label:o[p-1]};break;case 34:w.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",o[p-3],o[p-2]," #3:",o[p-1],o[p]),this.$={typeStr:o[p-3]+o[p],label:o[p-2],directions:o[p-1]};break;case 35:case 36:this.$={type:"classDef",id:o[p-1].trim(),css:o[p].trim()};break;case 37:this.$={type:"applyClass",id:o[p-1].trim(),styleClass:o[p].trim()};break;case 38:this.$={type:"applyStyles",id:o[p-1].trim(),stylesStr:o[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{8:[1,20]},e(u,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:s,29:n,31:i,32:r,40:a,44:l,47:c}),e(h,[2,16],{14:22,15:b,16:m}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(y,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,32:r},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(L,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(u,[2,13]),{26:35,32:r},{32:[2,14]},{17:[1,36]},e(y,[2,24]),{11:37,13:4,14:22,15:b,16:m,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(L,[2,30]),{18:[1,43]},{18:[1,44]},e(y,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{37:[1,47]},{34:48,35:E},{15:[1,50]},e(h,[2,27]),e(L,[2,33]),{39:[1,51]},{34:52,35:E,39:[2,31]},{32:[2,15]},e(L,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(x,g){if(g.recoverable)this.trace(x);else{var f=new Error(x);throw f.hash=g,f}},"parseError"),parse:d(function(x){var g=this,f=[0],w=[],v=[null],o=[],W=this.table,p="",R=0,G=0,V=0,Bt=2,at=1,ke=o.slice.call(arguments,1),z=Object.create(this.lexer),q={yy:{}};for(var ut in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ut)&&(q.yy[ut]=this.yy[ut]);z.setInput(x,q.yy),q.yy.lexer=z,q.yy.parser=this,typeof z.yylloc>"u"&&(z.yylloc={});var pt=z.yylloc;o.push(pt);var Le=z.options&&z.options.ranges;typeof q.yy.parseError=="function"?this.parseError=q.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Se(P){f.length=f.length-2*P,v.length=v.length-P,o.length=o.length-P}d(Se,"popStack");function Ct(){var P;return P=w.pop()||z.lex()||at,typeof P!="number"&&(P instanceof Array&&(w=P,P=w.pop()),P=g.symbols_[P]||P),P}d(Ct,"lex");for(var F,ft,J,H,Zr,bt,Q={},st,Z,It,it;;){if(J=f[f.length-1],this.defaultActions[J]?H=this.defaultActions[J]:((F===null||typeof F>"u")&&(F=Ct()),H=W[J]&&W[J][F]),typeof H>"u"||!H.length||!H[0]){var xt="";it=[];for(st in W[J])this.terminals_[st]&&st>Bt&&it.push("'"+this.terminals_[st]+"'");z.showPosition?xt="Parse error on line "+(R+1)+`:
`+z.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[F]||F)+"'":xt="Parse error on line "+(R+1)+": Unexpected "+(F==at?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(xt,{text:z.match,token:this.terminals_[F]||F,line:z.yylineno,loc:pt,expected:it})}if(H[0]instanceof Array&&H.length>1)throw new Error("Parse Error: multiple actions possible at state: "+J+", token: "+F);switch(H[0]){case 1:f.push(F),v.push(z.yytext),o.push(z.yylloc),f.push(H[1]),F=null,ft?(F=ft,ft=null):(G=z.yyleng,p=z.yytext,R=z.yylineno,pt=z.yylloc,V>0&&V--);break;case 2:if(Z=this.productions_[H[1]][1],Q.$=v[v.length-Z],Q._$={first_line:o[o.length-(Z||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(Z||1)].first_column,last_column:o[o.length-1].last_column},Le&&(Q._$.range=[o[o.length-(Z||1)].range[0],o[o.length-1].range[1]]),bt=this.performAction.apply(Q,[p,G,R,q.yy,H[1],v,o].concat(ke)),typeof bt<"u")return bt;Z&&(f=f.slice(0,-1*Z*2),v=v.slice(0,-1*Z),o=o.slice(0,-1*Z)),f.push(this.productions_[H[1]][0]),v.push(Q.$),o.push(Q._$),It=W[f[f.length-2]][f[f.length-1]],f.push(It);break;case 3:return!0}}return!0},"parse")},C=function(){var D={EOF:1,parseError:d(function(g,f){if(this.yy.parser)this.yy.parser.parseError(g,f);else throw new Error(g)},"parseError"),setInput:d(function(x,g){return this.yy=g||this.yy||{},this._input=x,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var x=this._input[0];this.yytext+=x,this.yyleng++,this.offset++,this.match+=x,this.matched+=x;var g=x.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),x},"input"),unput:d(function(x){var g=x.length,f=x.split(/(?:\r\n?|\n)/g);this._input=x+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var v=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===w.length?this.yylloc.first_column:0)+w[w.length-f.length].length-f[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[v[0],v[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(x){this.unput(this.match.slice(x))},"less"),pastInput:d(function(){var x=this.matched.substr(0,this.matched.length-this.match.length);return(x.length>20?"...":"")+x.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var x=this.match;return x.length<20&&(x+=this._input.substr(0,20-x.length)),(x.substr(0,20)+(x.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var x=this.pastInput(),g=new Array(x.length+1).join("-");return x+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(x,g){var f,w,v;if(this.options.backtrack_lexer&&(v={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(v.yylloc.range=this.yylloc.range.slice(0))),w=x[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+x[0].length},this.yytext+=x[0],this.match+=x[0],this.matches=x,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(x[0].length),this.matched+=x[0],f=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var o in v)this[o]=v[o];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var x,g,f,w;this._more||(this.yytext="",this.match="");for(var v=this._currentRules(),o=0;o<v.length;o++)if(f=this._input.match(this.rules[v[o]]),f&&(!g||f[0].length>g[0].length)){if(g=f,w=o,this.options.backtrack_lexer){if(x=this.test_match(f,v[o]),x!==!1)return x;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(x=this.test_match(g,v[w]),x!==!1?x:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,f,w,v){var o=v;switch(w){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;break;case 2:return g.getLogger().debug("Found nl-block"),31;break;case 3:return g.getLogger().debug("Found space-block"),29;break;case 4:g.getLogger().debug(".",f.yytext);break;case 5:g.getLogger().debug("_",f.yytext);break;case 6:return 5;case 7:return f.yytext=-1,28;break;case 8:return f.yytext=f.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",f.yytext),28;break;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",f.yytext),"STR";break;case 15:return f.yytext=f.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;break;case 16:return f.yytext="1",g.getLogger().debug("COLUMNS (LEX)",f.yytext),21;break;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;break;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";break;case 22:return this.popState(),this.pushState("CLASSDEFID"),41;break;case 23:return this.popState(),42;break;case 24:return this.pushState("CLASS"),44;break;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;break;case 26:return this.popState(),46;break;case 27:return this.pushState("STYLE_STMNT"),47;break;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;break;case 29:return this.popState(),49;break;case 30:return this.pushState("acc_title"),"acc_title";break;case 31:return this.popState(),"acc_title_value";break;case 32:return this.pushState("acc_descr"),"acc_descr";break;case 33:return this.popState(),"acc_descr_value";break;case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";break;case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";break;case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";break;case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";break;case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";break;case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";break;case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";break;case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";break;case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";break;case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";break;case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;break;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;break;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;break;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;break;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;break;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;break;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 66:return this.pushState("NODE"),36;break;case 67:return this.pushState("NODE"),36;break;case 68:return this.pushState("NODE"),36;break;case 69:return this.pushState("NODE"),36;break;case 70:return this.pushState("NODE"),36;break;case 71:return this.pushState("NODE"),36;break;case 72:return this.pushState("NODE"),36;break;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;break;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;break;case 75:return g.getLogger().debug("Lex: NODE_ID",f.yytext),32;break;case 76:return g.getLogger().debug("Lex: EOF",f.yytext),8;break;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";break;case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";break;case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",f.yytext),"DIR";break;case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",f.yytext),"DIR";break;case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",f.yytext),"DIR";break;case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",f.yytext),"DIR";break;case 91:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",f.yytext),"DIR";break;case 92:return f.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";break;case 93:return g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 94:return g.getLogger().debug("Lex: LINK",f.yytext),15;break;case 95:return g.getLogger().debug("Lex: LINK",f.yytext),15;break;case 96:return g.getLogger().debug("Lex: LINK",f.yytext),15;break;case 97:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 98:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 99:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";break;case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;break;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;break;case 105:return g.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27;break}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return D}();S.lexer=C;function _(){this.yy={}}return d(_,"Parser"),_.prototype=S,S.Parser=_,new _}();wt.parser=wt;var ve=wt,U=new Map,Et=[],kt=new Map,jt="color",Vt="fill",Ee="bgFill",ee=",",_e=I(),ot=new Map,De=d(e=>zt.sanitizeText(e,_e),"sanitizeText"),Ne=d(function(e,t=""){let s=ot.get(e);s||(s={id:e,styles:[],textStyles:[]},ot.set(e,s)),t?.split(ee).forEach(n=>{let i=n.replace(/([^;]*);/,"$1").trim();if(RegExp(jt).exec(n)){let a=i.replace(Vt,Ee).replace(jt,Vt);s.textStyles.push(a)}s.styles.push(i)})},"addStyleClass"),Te=d(function(e,t=""){let s=U.get(e);t!=null&&(s.styles=t.split(ee))},"addStyle2Node"),Be=d(function(e,t){e.split(",").forEach(function(s){let n=U.get(s);if(n===void 0){let i=s.trim();n={id:i,type:"na",children:[]},U.set(i,n)}n.classes||(n.classes=[]),n.classes.push(t)})},"setCssClass"),re=d((e,t)=>{let s=e.flat(),n=[];for(let i of s){if(i.label&&(i.label=De(i.label)),i.type==="classDef"){Ne(i.id,i.css);continue}if(i.type==="applyClass"){Be(i.id,i?.styleClass??"");continue}if(i.type==="applyStyles"){i?.stylesStr&&Te(i.id,i?.stylesStr);continue}if(i.type==="column-setting")t.columns=i.columns??-1;else if(i.type==="edge"){let r=(kt.get(i.id)??0)+1;kt.set(i.id,r),i.id=r+"-"+i.id,Et.push(i)}else{i.label||(i.type==="composite"?i.label="":i.label=i.id);let r=U.get(i.id);if(r===void 0?U.set(i.id,i):(i.type!=="na"&&(r.type=i.type),i.label!==i.id&&(r.label=i.label)),i.children&&re(i.children,i),i.type==="space"){let a=i.width??1;for(let l=0;l<a;l++){let c=Xt(i);c.id=c.id+"-"+l,U.set(c.id,c),n.push(c)}}else r===void 0&&n.push(i)}}t.children=n},"populateBlockDatabase"),_t=[],rt={id:"root",type:"composite",children:[],columns:-1},Ce=d(()=>{k.debug("Clear called"),Mt(),rt={id:"root",type:"composite",children:[],columns:-1},U=new Map([["root",rt]]),_t=[],ot=new Map,Et=[],kt=new Map},"clear");function ae(e){switch(k.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return k.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(ae,"typeStr2Type");function se(e){switch(k.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(se,"edgeTypeStr2Type");function ie(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}d(ie,"edgeStrToEdgeData");var Zt=0,Ie=d(()=>(Zt++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Zt),"generateId"),Oe=d(e=>{rt.children=e,re(e,rt),_t=rt.children},"setHierarchy"),Re=d(e=>{let t=U.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),ze=d(()=>[...U.values()],"getBlocksFlat"),Ae=d(()=>_t||[],"getBlocks"),Me=d(()=>Et,"getEdges"),Fe=d(e=>U.get(e),"getBlock"),We=d(e=>{U.set(e.id,e)},"setBlock"),Pe=d(()=>console,"getLogger"),Ye=d(function(){return ot},"getClasses"),He={getConfig:d(()=>$().block,"getConfig"),typeStr2Type:ae,edgeTypeStr2Type:se,edgeStrToEdgeData:ie,getLogger:Pe,getBlocksFlat:ze,getBlocks:Ae,getEdges:Me,setHierarchy:Oe,getBlock:Fe,setBlock:We,getColumns:Re,getClasses:Ye,clear:Ce,generateId:Ie},Ke=He,lt=d((e,t)=>{let s=Rt,n=s(e,"r"),i=s(e,"g"),r=s(e,"b");return Ot(n,i,r,t)},"fade"),Xe=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${lt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${lt(e.mainBkg,.5)};
    fill: ${lt(e.clusterBkg,.5)};
    stroke: ${lt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),Ue=Xe,je=d((e,t,s,n)=>{t.forEach(i=>{rr[i](e,s,n)})},"insertMarkers"),Ve=d((e,t,s)=>{k.trace("Making markers for ",s),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),Ze=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),Ge=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),qe=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),Je=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),Qe=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),$e=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),tr=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),er=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),rr={extension:Ve,composition:Ze,aggregation:Ge,dependency:qe,lollipop:Je,point:Qe,circle:$e,cross:tr,barb:er},ar=je,O=I()?.block?.padding??8;function ne(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};let s=t%e,n=Math.floor(t/e);return{px:s,py:n}}d(ne,"calculateBlockPosition");var sr=d(e=>{let t=0,s=0;for(let n of e.children){let{width:i,height:r,x:a,y:l}=n.size??{width:0,height:0,x:0,y:0};k.debug("getMaxChildSize abc95 child:",n.id,"width:",i,"height:",r,"x:",a,"y:",l,n.type),n.type!=="space"&&(i>t&&(t=i/(e.widthInColumns??1)),r>s&&(s=r))}return{width:t,height:s}},"getMaxChildSize");function ht(e,t,s=0,n=0){k.debug("setBlockSizes abc95 (start)",e.id,e?.size?.x,"block width =",e?.size,"sieblingWidth",s),e?.size?.width||(e.size={width:s,height:n,x:0,y:0});let i=0,r=0;if(e.children?.length>0){for(let y of e.children)ht(y,t);let a=sr(e);i=a.width,r=a.height,k.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",i,r);for(let y of e.children)y.size&&(k.debug(`abc95 Setting size of children of ${e.id} id=${y.id} ${i} ${r} ${JSON.stringify(y.size)}`),y.size.width=i*(y.widthInColumns??1)+O*((y.widthInColumns??1)-1),y.size.height=r,y.size.x=0,y.size.y=0,k.debug(`abc95 updating size of ${e.id} children child:${y.id} maxWidth:${i} maxHeight:${r}`));for(let y of e.children)ht(y,t,i,r);let l=e.columns??-1,c=0;for(let y of e.children)c+=y.widthInColumns??1;let u=e.children.length;l>0&&l<c&&(u=l);let h=Math.ceil(c/u),b=u*(i+O)+O,m=h*(r+O)+O;if(b<s){k.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${s} sieblingHeight ${n} width ${b}`),b=s,m=n;let y=(s-u*O-O)/u,L=(n-h*O-O)/h;k.debug("Size indata abc88",e.id,"childWidth",y,"maxWidth",i),k.debug("Size indata abc88",e.id,"childHeight",L,"maxHeight",r),k.debug("Size indata abc88 xSize",u,"padding",O);for(let E of e.children)E.size&&(E.size.width=y,E.size.height=L,E.size.x=0,E.size.y=0)}if(k.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${h} columns ${l}${e.children.length} width=${Math.max(b,e.size?.width||0)}`),b<(e?.size?.width||0)){b=e?.size?.width||0;let y=l>0?Math.min(e.children.length,l):e.children.length;if(y>0){let L=(b-y*O-O)/y;k.debug("abc95 (growing to fit) width",e.id,b,e.size?.width,L);for(let E of e.children)E.size&&(E.size.width=L)}}e.size={width:b,height:m,x:0,y:0}}k.debug("setBlockSizes abc94 (done)",e.id,e?.size?.x,e?.size?.width,e?.size?.y,e?.size?.height)}d(ht,"setBlockSizes");function Dt(e,t){k.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`);let s=e.columns??-1;if(k.debug("layoutBlocks columns abc95",e.id,"=>",s,e),e.children&&e.children.length>0){let n=e?.children[0]?.size?.width??0,i=e.children.length*n+(e.children.length-1)*O;k.debug("widthOfChildren 88",i,"posX");let r=0;k.debug("abc91 block?.size?.x",e.id,e?.size?.x);let a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,l=0;for(let c of e.children){let u=e;if(!c.size)continue;let{width:h,height:b}=c.size,{px:m,py:y}=ne(s,r);if(y!=l&&(l=y,a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,k.debug("New row in layout for block",e.id," and child ",c.id,l)),k.debug(`abc89 layout blocks (child) id: ${c.id} Pos: ${r} (px, py) ${m},${y} (${u?.size?.x},${u?.size?.y}) parent: ${u.id} width: ${h}${O}`),u.size){let L=h/2;c.size.x=a+O+L,k.debug(`abc91 layout blocks (calc) px, pyid:${c.id} startingPos=X${a} new startingPosX${c.size.x} ${L} padding=${O} width=${h} halfWidth=${L} => x:${c.size.x} y:${c.size.y} ${c.widthInColumns} (width * (child?.w || 1)) / 2 ${h*(c?.widthInColumns??1)/2}`),a=c.size.x+L,c.size.y=u.size.y-u.size.height/2+y*(b+O)+b/2+O,k.debug(`abc88 layout blocks (calc) px, pyid:${c.id}startingPosX${a}${O}${L}=>x:${c.size.x}y:${c.size.y}${c.widthInColumns}(width * (child?.w || 1)) / 2${h*(c?.widthInColumns??1)/2}`)}c.children&&Dt(c,t),r+=c?.widthInColumns??1,k.debug("abc88 columnsPos",c,r)}}k.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`)}d(Dt,"layoutBlocks");function Nt(e,{minX:t,minY:s,maxX:n,maxY:i}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){let{x:r,y:a,width:l,height:c}=e.size;r-l/2<t&&(t=r-l/2),a-c/2<s&&(s=a-c/2),r+l/2>n&&(n=r+l/2),a+c/2>i&&(i=a+c/2)}if(e.children)for(let r of e.children)({minX:t,minY:s,maxX:n,maxY:i}=Nt(r,{minX:t,minY:s,maxX:n,maxY:i}));return{minX:t,minY:s,maxX:n,maxY:i}}d(Nt,"findBounds");function le(e){let t=e.getBlock("root");if(!t)return;ht(t,e,0,0),Dt(t,e),k.debug("getBlocks",JSON.stringify(t,null,2));let{minX:s,minY:n,maxX:i,maxY:r}=Nt(t),a=r-n,l=i-s;return{x:s,y:n,width:l,height:a}}d(le,"layout");function Lt(e,t){t&&e.attr("style",t)}d(Lt,"applyStyle");function ce(e){let t=N(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),s=t.append("xhtml:div"),n=e.label,i=e.isNode?"nodeLabel":"edgeLabel",r=s.append("span");return r.html(n),Lt(r,e.labelStyle),r.attr("class",i),Lt(s,e.labelStyle),s.style("display","inline-block"),s.style("white-space","nowrap"),s.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(ce,"addHtmlLabel");var ir=d((e,t,s,n)=>{let i=e||"";if(typeof i=="object"&&(i=i[0]),X(I().flowchart.htmlLabels)){i=i.replace(/\\n|\n/g,"<br />"),k.debug("vertexText"+i);let r={isNode:n,label:Ht(nt(i)),labelStyle:t.replace("fill:","color:")};return ce(r)}else{let r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("style",t.replace("color:","fill:"));let a=[];typeof i=="string"?a=i.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(i)?a=i:a=[];for(let l of a){let c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),s?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=l.trim(),r.appendChild(c)}return r}},"createLabel"),K=ir,nr=d((e,t,s,n,i)=>{t.arrowTypeStart&&Gt(e,"start",t.arrowTypeStart,s,n,i),t.arrowTypeEnd&&Gt(e,"end",t.arrowTypeEnd,s,n,i)},"addEdgeMarkers"),lr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Gt=d((e,t,s,n,i,r)=>{let a=lr[s];if(!a){k.warn(`Unknown arrow type: ${s}`);return}let l=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${n}#${i}_${r}-${a}${l})`)},"addEdgeMarker"),St={},M={},cr=d((e,t)=>{let s=I(),n=X(s.flowchart.htmlLabels),i=t.labelType==="markdown"?mt(e,t.label,{style:t.labelStyle,useHtmlLabels:n,addSvgBackground:!0},s):K(t.label,t.labelStyle),r=e.insert("g").attr("class","edgeLabel"),a=r.insert("g").attr("class","label");a.node().appendChild(i);let l=i.getBBox();if(n){let u=i.children[0],h=N(i);l=u.getBoundingClientRect(),h.attr("width",l.width),h.attr("height",l.height)}a.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),St[t.id]=r,t.width=l.width,t.height=l.height;let c;if(t.startLabelLeft){let u=K(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),b=h.insert("g").attr("class","inner");c=b.node().appendChild(u);let m=u.getBBox();b.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startLeft=h,et(c,t.startLabelLeft)}if(t.startLabelRight){let u=K(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),b=h.insert("g").attr("class","inner");c=h.node().appendChild(u),b.node().appendChild(u);let m=u.getBBox();b.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startRight=h,et(c,t.startLabelRight)}if(t.endLabelLeft){let u=K(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),b=h.insert("g").attr("class","inner");c=b.node().appendChild(u);let m=u.getBBox();b.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endLeft=h,et(c,t.endLabelLeft)}if(t.endLabelRight){let u=K(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),b=h.insert("g").attr("class","inner");c=b.node().appendChild(u);let m=u.getBBox();b.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endRight=h,et(c,t.endLabelRight)}return i},"insertEdgeLabel");function et(e,t){I().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(et,"setTerminalWidth");var or=d((e,t)=>{k.debug("Moving label abc88 ",e.id,e.label,St[e.id],t);let s=t.updatedPath?t.updatedPath:t.originalPath,n=I(),{subGraphTitleTotalMargin:i}=Ft(n);if(e.label){let r=St[e.id],a=e.x,l=e.y;if(s){let c=tt.calcLabelPosition(s);k.debug("Moving label "+e.label+" from (",a,",",l,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(a=c.x,l=c.y)}r.attr("transform",`translate(${a}, ${l+i/2})`)}if(e.startLabelLeft){let r=M[e.id].startLeft,a=e.x,l=e.y;if(s){let c=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.startLabelRight){let r=M[e.id].startRight,a=e.x,l=e.y;if(s){let c=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelLeft){let r=M[e.id].endLeft,a=e.x,l=e.y;if(s){let c=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelRight){let r=M[e.id].endRight,a=e.x,l=e.y;if(s){let c=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}},"positionEdgeLabel"),hr=d((e,t)=>{let s=e.x,n=e.y,i=Math.abs(t.x-s),r=Math.abs(t.y-n),a=e.width/2,l=e.height/2;return i>=a||r>=l},"outsideNode"),dr=d((e,t,s)=>{k.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(s)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);let n=e.x,i=e.y,r=Math.abs(n-s.x),a=e.width/2,l=s.x<t.x?a-r:a+r,c=e.height/2,u=Math.abs(t.y-s.y),h=Math.abs(t.x-s.x);if(Math.abs(i-t.y)*a>Math.abs(n-t.x)*c){let b=s.y<t.y?t.y-c-i:i-c-t.y;l=h*b/u;let m={x:s.x<t.x?s.x+l:s.x-h+l,y:s.y<t.y?s.y+u-b:s.y-u+b};return l===0&&(m.x=t.x,m.y=t.y),h===0&&(m.x=t.x),u===0&&(m.y=t.y),k.debug(`abc89 topp/bott calc, Q ${u}, q ${b}, R ${h}, r ${l}`,m),m}else{s.x<t.x?l=t.x-a-n:l=n-a-t.x;let b=u*l/h,m=s.x<t.x?s.x+h-l:s.x-h+l,y=s.y<t.y?s.y+b:s.y-b;return k.debug(`sides calc abc89, Q ${u}, q ${b}, R ${h}, r ${l}`,{_x:m,_y:y}),l===0&&(m=t.x,y=t.y),h===0&&(m=t.x),u===0&&(y=t.y),{x:m,y}}},"intersection"),qt=d((e,t)=>{k.debug("abc88 cutPathAtIntersect",e,t);let s=[],n=e[0],i=!1;return e.forEach(r=>{if(!hr(t,r)&&!i){let a=dr(t,n,r),l=!1;s.forEach(c=>{l=l||c.x===a.x&&c.y===a.y}),s.some(c=>c.x===a.x&&c.y===a.y)||s.push(a),i=!0}else n=r,i||s.push(r)}),s},"cutPathAtIntersect"),gr=d(function(e,t,s,n,i,r,a){let l=s.points;k.debug("abc88 InsertEdge: edge=",s,"e=",t);let c=!1,u=r.node(t.v);var h=r.node(t.w);h?.intersect&&u?.intersect&&(l=l.slice(1,s.points.length-1),l.unshift(u.intersect(l[0])),l.push(h.intersect(l[l.length-1]))),s.toCluster&&(k.debug("to cluster abc88",n[s.toCluster]),l=qt(s.points,n[s.toCluster].node),c=!0),s.fromCluster&&(k.debug("from cluster abc88",n[s.fromCluster]),l=qt(l.reverse(),n[s.fromCluster].node).reverse(),c=!0);let b=l.filter(x=>!Number.isNaN(x.y)),m=Pt;s.curve&&(i==="graph"||i==="flowchart")&&(m=s.curve);let{x:y,y:L}=Kt(s),E=Wt().x(y).y(L).curve(m),S;switch(s.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(s.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}let C=e.append("path").attr("d",E(b)).attr("id",s.id).attr("class"," "+S+(s.classes?" "+s.classes:"")).attr("style",s.style),_="";(I().flowchart.arrowMarkerAbsolute||I().state.arrowMarkerAbsolute)&&(_=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,_=_.replace(/\(/g,"\\("),_=_.replace(/\)/g,"\\)")),nr(C,s,_,a,i);let D={};return c&&(D.updatedPath=l),D.originalPath=s.points,D},"insertEdge"),ur=d(e=>{let t=new Set;for(let s of e)switch(s){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(s);break}return t},"expandAndDeduplicateDirections"),pr=d((e,t,s)=>{let n=ur(e),i=2,r=t.height+2*s.padding,a=r/i,l=t.width+2*a+s.padding,c=s.padding/2;return n.has("right")&&n.has("left")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:a,y:0},{x:l/2,y:2*c},{x:l-a,y:0},{x:l,y:0},{x:l,y:-r/3},{x:l+2*c,y:-r/2},{x:l,y:-2*r/3},{x:l,y:-r},{x:l-a,y:-r},{x:l/2,y:-r-2*c},{x:a,y:-r},{x:0,y:-r},{x:0,y:-2*r/3},{x:-2*c,y:-r/2},{x:0,y:-r/3}]:n.has("right")&&n.has("left")&&n.has("up")?[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}]:n.has("right")&&n.has("left")&&n.has("down")?[{x:0,y:0},{x:a,y:-r},{x:l-a,y:-r},{x:l,y:0}]:n.has("right")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:l,y:-a},{x:l,y:-r+a},{x:0,y:-r}]:n.has("left")&&n.has("up")&&n.has("down")?[{x:l,y:0},{x:0,y:-a},{x:0,y:-r+a},{x:l,y:-r}]:n.has("right")&&n.has("left")?[{x:a,y:0},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")&&n.has("down")?[{x:l/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-r+c},{x:0,y:-r+c},{x:l/2,y:-r},{x:l,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c},{x:l,y:-c}]:n.has("right")&&n.has("up")?[{x:0,y:0},{x:l,y:-a},{x:0,y:-r}]:n.has("right")&&n.has("down")?[{x:0,y:0},{x:l,y:0},{x:0,y:-r}]:n.has("left")&&n.has("up")?[{x:l,y:0},{x:0,y:-a},{x:l,y:-r}]:n.has("left")&&n.has("down")?[{x:l,y:0},{x:0,y:0},{x:l,y:-r}]:n.has("right")?[{x:a,y:-c},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r+c}]:n.has("left")?[{x:a,y:0},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")?[{x:a,y:-c},{x:a,y:-r+c},{x:0,y:-r+c},{x:l/2,y:-r},{x:l,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c}]:n.has("down")?[{x:l/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c},{x:l,y:-c}]:[{x:0,y:0}]},"getArrowPoints");function oe(e,t){return e.intersect(t)}d(oe,"intersectNode");var fr=oe;function he(e,t,s,n){var i=e.x,r=e.y,a=i-n.x,l=r-n.y,c=Math.sqrt(t*t*l*l+s*s*a*a),u=Math.abs(t*s*a/c);n.x<i&&(u=-u);var h=Math.abs(t*s*l/c);return n.y<r&&(h=-h),{x:i+u,y:r+h}}d(he,"intersectEllipse");var de=he;function ge(e,t,s){return de(e,t,t,s)}d(ge,"intersectCircle");var br=ge;function ue(e,t,s,n){var i,r,a,l,c,u,h,b,m,y,L,E,S,C,_;if(i=t.y-e.y,a=e.x-t.x,c=t.x*e.y-e.x*t.y,m=i*s.x+a*s.y+c,y=i*n.x+a*n.y+c,!(m!==0&&y!==0&&vt(m,y))&&(r=n.y-s.y,l=s.x-n.x,u=n.x*s.y-s.x*n.y,h=r*e.x+l*e.y+u,b=r*t.x+l*t.y+u,!(h!==0&&b!==0&&vt(h,b))&&(L=i*l-r*a,L!==0)))return E=Math.abs(L/2),S=a*u-l*c,C=S<0?(S-E)/L:(S+E)/L,S=r*c-i*u,_=S<0?(S-E)/L:(S+E)/L,{x:C,y:_}}d(ue,"intersectLine");function vt(e,t){return e*t>0}d(vt,"sameSign");var xr=ue,yr=pe;function pe(e,t,s){var n=e.x,i=e.y,r=[],a=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(L){a=Math.min(a,L.x),l=Math.min(l,L.y)}):(a=Math.min(a,t.x),l=Math.min(l,t.y));for(var c=n-e.width/2-a,u=i-e.height/2-l,h=0;h<t.length;h++){var b=t[h],m=t[h<t.length-1?h+1:0],y=xr(e,s,{x:c+b.x,y:u+b.y},{x:c+m.x,y:u+m.y});y&&r.push(y)}return r.length?(r.length>1&&r.sort(function(L,E){var S=L.x-s.x,C=L.y-s.y,_=Math.sqrt(S*S+C*C),D=E.x-s.x,x=E.y-s.y,g=Math.sqrt(D*D+x*x);return _<g?-1:_===g?0:1}),r[0]):e}d(pe,"intersectPolygon");var mr=d((e,t)=>{var s=e.x,n=e.y,i=t.x-s,r=t.y-n,a=e.width/2,l=e.height/2,c,u;return Math.abs(r)*a>Math.abs(i)*l?(r<0&&(l=-l),c=r===0?0:l*i/r,u=l):(i<0&&(a=-a),c=a,u=i===0?0:a*r/i),{x:s+c,y:n+u}},"intersectRect"),wr=mr,T={node:fr,circle:br,ellipse:de,polygon:yr,rect:wr},A=d(async(e,t,s,n)=>{let i=I(),r,a=t.useHtmlLabels||X(i.flowchart.htmlLabels);s?r=s:r="node default";let l=e.insert("g").attr("class",r).attr("id",t.domId||t.id),c=l.insert("g").attr("class","label").attr("style",t.labelStyle),u;t.labelText===void 0?u="":u=typeof t.labelText=="string"?t.labelText:t.labelText[0];let h=c.node(),b;t.labelType==="markdown"?b=mt(c,yt(nt(u),i),{useHtmlLabels:a,width:t.width||i.flowchart.wrappingWidth,classes:"markdown-node-label"},i):b=h.appendChild(K(yt(nt(u),i),t.labelStyle,!1,n));let m=b.getBBox(),y=t.padding/2;if(X(i.flowchart.htmlLabels)){let L=b.children[0],E=N(b),S=L.getElementsByTagName("img");if(S){let C=u.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...S].map(_=>new Promise(D=>{function x(){if(_.style.display="flex",_.style.flexDirection="column",C){let g=i.fontSize?i.fontSize:window.getComputedStyle(document.body).fontSize,w=parseInt(g,10)*5+"px";_.style.minWidth=w,_.style.maxWidth=w}else _.style.width="100%";D(_)}d(x,"setupImage"),setTimeout(()=>{_.complete&&x()}),_.addEventListener("error",x),_.addEventListener("load",x)})))}m=L.getBoundingClientRect(),E.attr("width",m.width),E.attr("height",m.height)}return a?c.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"):c.attr("transform","translate(0, "+-m.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:l,bbox:m,halfPadding:y,label:c}},"labelHelper"),B=d((e,t)=>{let s=t.node().getBBox();e.width=s.width,e.height=s.height},"updateNodeBounds");function j(e,t,s,n){return e.insert("polygon",":first-child").attr("points",n.map(function(i){return i.x+","+i.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+s/2+")")}d(j,"insertPolygonShape");var kr=d(async(e,t)=>{t.useHtmlLabels||I().flowchart.htmlLabels||(t.centerLabel=!0);let{shapeSvg:n,bbox:i,halfPadding:r}=await A(e,t,"node "+t.classes,!0);k.info("Classes = ",t.classes);let a=n.insert("rect",":first-child");return a.attr("rx",t.rx).attr("ry",t.ry).attr("x",-i.width/2-r).attr("y",-i.height/2-r).attr("width",i.width+t.padding).attr("height",i.height+t.padding),B(t,a),t.intersect=function(l){return T.rect(t,l)},n},"note"),Lr=kr,Jt=d(e=>e?" "+e:"","formatClass"),Y=d((e,t)=>`${t||"node default"}${Jt(e.classes)} ${Jt(e.class)}`,"getClassesFromNode"),Qt=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=i+r,l=[{x:a/2,y:0},{x:a,y:-a/2},{x:a/2,y:-a},{x:0,y:-a/2}];k.info("Question main (Circle)");let c=j(s,a,a,l);return c.attr("style",t.style),B(t,c),t.intersect=function(u){return k.warn("Intersect called"),T.polygon(t,l,u)},s},"question"),Sr=d((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=28,i=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}];return s.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(a){return T.circle(t,14,a)},s},"choice"),vr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=4,r=n.height+t.padding,a=r/i,l=n.width+2*a+t.padding,c=[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}],u=j(s,l,r,c);return u.attr("style",t.style),B(t,u),t.intersect=function(h){return T.polygon(t,c,h)},s},"hexagon"),Er=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,void 0,!0),i=2,r=n.height+2*t.padding,a=r/i,l=n.width+2*a+t.padding,c=pr(t.directions,n,t),u=j(s,l,r,c);return u.attr("style",t.style),B(t,u),t.intersect=function(h){return T.polygon(t,c,h)},s},"block_arrow"),_r=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-r/2,y:0},{x:i,y:0},{x:i,y:-r},{x:-r/2,y:-r},{x:0,y:-r/2}];return j(s,i,r,a).attr("style",t.style),t.width=i+r,t.height=r,t.intersect=function(c){return T.polygon(t,a,c)},s},"rect_left_inv_arrow"),Dr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:r/6,y:-r}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"lean_right"),Nr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:2*r/6,y:0},{x:i+r/6,y:0},{x:i-2*r/6,y:-r},{x:-r/6,y:-r}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"lean_left"),Tr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i+2*r/6,y:0},{x:i-r/6,y:-r},{x:r/6,y:-r}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"trapezoid"),Br=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:-2*r/6,y:-r}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"inv_trapezoid"),Cr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i+r/2,y:0},{x:i,y:-r/2},{x:i+r/2,y:-r},{x:0,y:-r}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"rect_right_inv_arrow"),Ir=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=i/2,a=r/(2.5+i/50),l=n.height+a+t.padding,c="M 0,"+a+" a "+r+","+a+" 0,0,0 "+i+" 0 a "+r+","+a+" 0,0,0 "+-i+" 0 l 0,"+l+" a "+r+","+a+" 0,0,0 "+i+" 0 l 0,"+-l,u=s.attr("label-offset-y",a).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-i/2+","+-(l/2+a)+")");return B(t,u),t.intersect=function(h){let b=T.rect(t,h),m=b.x-t.x;if(r!=0&&(Math.abs(m)<t.width/2||Math.abs(m)==t.width/2&&Math.abs(b.y-t.y)>t.height/2-a)){let y=a*a*(1-m*m/(r*r));y!=0&&(y=Math.sqrt(y)),y=a-y,h.y-t.y>0&&(y=-y),b.y+=y}return b},s},"cylinder"),Or=d(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes+" "+t.class,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,c=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",a).attr("height",l),t.props){let h=new Set(Object.keys(t.props));t.props.borders&&(dt(r,t.props.borders,a,l),h.delete("borders")),h.forEach(b=>{k.warn(`Unknown node property ${b}`)})}return B(t,r),t.intersect=function(h){return T.rect(t,h)},s},"rect"),Rr=d(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,c=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",a).attr("height",l),t.props){let h=new Set(Object.keys(t.props));t.props.borders&&(dt(r,t.props.borders,a,l),h.delete("borders")),h.forEach(b=>{k.warn(`Unknown node property ${b}`)})}return B(t,r),t.intersect=function(h){return T.rect(t,h)},s},"composite"),zr=d(async(e,t)=>{let{shapeSvg:s}=await A(e,t,"label",!0);k.trace("Classes = ",t.class);let n=s.insert("rect",":first-child"),i=0,r=0;if(n.attr("width",i).attr("height",r),s.attr("class","label edgeLabel"),t.props){let a=new Set(Object.keys(t.props));t.props.borders&&(dt(n,t.props.borders,i,r),a.delete("borders")),a.forEach(l=>{k.warn(`Unknown node property ${l}`)})}return B(t,n),t.intersect=function(a){return T.rect(t,a)},s},"labelRect");function dt(e,t,s,n){let i=[],r=d(l=>{i.push(l,0)},"addBorder"),a=d(l=>{i.push(0,l)},"skipBorder");t.includes("t")?(k.debug("add top border"),r(s)):a(s),t.includes("r")?(k.debug("add right border"),r(n)):a(n),t.includes("b")?(k.debug("add bottom border"),r(s)):a(s),t.includes("l")?(k.debug("add left border"),r(n)):a(n),e.attr("stroke-dasharray",i.join(" "))}d(dt,"applyNodePropertyBorders");var Ar=d((e,t)=>{let s;t.classes?s="node "+t.classes:s="node default";let n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),i=n.insert("rect",":first-child"),r=n.insert("line"),a=n.insert("g").attr("class","label"),l=t.labelText.flat?t.labelText.flat():t.labelText,c="";typeof l=="object"?c=l[0]:c=l,k.info("Label text abc79",c,l,typeof l=="object");let u=a.node().appendChild(K(c,t.labelStyle,!0,!0)),h={width:0,height:0};if(X(I().flowchart.htmlLabels)){let E=u.children[0],S=N(u);h=E.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}k.info("Text 2",l);let b=l.slice(1,l.length),m=u.getBBox(),y=a.node().appendChild(K(b.join?b.join("<br/>"):b,t.labelStyle,!0,!0));if(X(I().flowchart.htmlLabels)){let E=y.children[0],S=N(y);h=E.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}let L=t.padding/2;return N(y).attr("transform","translate( "+(h.width>m.width?0:(m.width-h.width)/2)+", "+(m.height+L+5)+")"),N(u).attr("transform","translate( "+(h.width<m.width?0:-(m.width-h.width)/2)+", 0)"),h=a.node().getBBox(),a.attr("transform","translate("+-h.width/2+", "+(-h.height/2-L+3)+")"),i.attr("class","outer title-state").attr("x",-h.width/2-L).attr("y",-h.height/2-L).attr("width",h.width+t.padding).attr("height",h.height+t.padding),r.attr("class","divider").attr("x1",-h.width/2-L).attr("x2",h.width/2+L).attr("y1",-h.height/2-L+m.height+L).attr("y2",-h.height/2-L+m.height+L),B(t,i),t.intersect=function(E){return T.rect(t,E)},n},"rectWithTitle"),Mr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.height+t.padding,r=n.width+i/4+t.padding,a=s.insert("rect",":first-child").attr("style",t.style).attr("rx",i/2).attr("ry",i/2).attr("x",-r/2).attr("y",-i/2).attr("width",r).attr("height",i);return B(t,a),t.intersect=function(l){return T.rect(t,l)},s},"stadium"),Fr=d(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,Y(t,void 0),!0),r=s.insert("circle",":first-child");return r.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),k.info("Circle main"),B(t,r),t.intersect=function(a){return k.info("Circle intersect",t,n.width/2+i,a),T.circle(t,n.width/2+i,a)},s},"circle"),Wr=d(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,Y(t,void 0),!0),r=5,a=s.insert("g",":first-child"),l=a.insert("circle"),c=a.insert("circle");return a.attr("class",t.class),l.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i+r).attr("width",n.width+t.padding+r*2).attr("height",n.height+t.padding+r*2),c.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),k.info("DoubleCircle main"),B(t,l),t.intersect=function(u){return k.info("DoubleCircle intersect",t,n.width/2+i+r,u),T.circle(t,n.width/2+i+r,u)},s},"doublecircle"),Pr=d(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i,y:0},{x:i,y:-r},{x:0,y:-r},{x:0,y:0},{x:-8,y:0},{x:i+8,y:0},{x:i+8,y:-r},{x:-8,y:-r},{x:-8,y:0}],l=j(s,i,r,a);return l.attr("style",t.style),B(t,l),t.intersect=function(c){return T.polygon(t,a,c)},s},"subroutine"),Yr=d((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child");return n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),B(t,n),t.intersect=function(i){return T.circle(t,7,i)},s},"start"),$t=d((e,t,s)=>{let n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=70,r=10;s==="LR"&&(i=10,r=70);let a=n.append("rect").attr("x",-1*i/2).attr("y",-1*r/2).attr("width",i).attr("height",r).attr("class","fork-join");return B(t,a),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(l){return T.rect(t,l)},n},"forkJoin"),Hr=d((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child"),i=s.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),n.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),B(t,i),t.intersect=function(r){return T.circle(t,7,r)},s},"end"),Kr=d((e,t)=>{let s=t.padding/2,n=4,i=8,r;t.classes?r="node "+t.classes:r="node default";let a=e.insert("g").attr("class",r).attr("id",t.domId||t.id),l=a.insert("rect",":first-child"),c=a.insert("line"),u=a.insert("line"),h=0,b=n,m=a.insert("g").attr("class","label"),y=0,L=t.classData.annotations?.[0],E=t.classData.annotations[0]?"\xAB"+t.classData.annotations[0]+"\xBB":"",S=m.node().appendChild(K(E,t.labelStyle,!0,!0)),C=S.getBBox();if(X(I().flowchart.htmlLabels)){let v=S.children[0],o=N(S);C=v.getBoundingClientRect(),o.attr("width",C.width),o.attr("height",C.height)}t.classData.annotations[0]&&(b+=C.height+n,h+=C.width);let _=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(I().flowchart.htmlLabels?_+="&lt;"+t.classData.type+"&gt;":_+="<"+t.classData.type+">");let D=m.node().appendChild(K(_,t.labelStyle,!0,!0));N(D).attr("class","classTitle");let x=D.getBBox();if(X(I().flowchart.htmlLabels)){let v=D.children[0],o=N(D);x=v.getBoundingClientRect(),o.attr("width",x.width),o.attr("height",x.height)}b+=x.height+n,x.width>h&&(h=x.width);let g=[];t.classData.members.forEach(v=>{let o=v.getDisplayDetails(),W=o.displayText;I().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(K(W,o.cssStyle?o.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(X(I().flowchart.htmlLabels)){let G=p.children[0],V=N(p);R=G.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>h&&(h=R.width),b+=R.height+n,g.push(p)}),b+=i;let f=[];if(t.classData.methods.forEach(v=>{let o=v.getDisplayDetails(),W=o.displayText;I().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(K(W,o.cssStyle?o.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(X(I().flowchart.htmlLabels)){let G=p.children[0],V=N(p);R=G.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>h&&(h=R.width),b+=R.height+n,f.push(p)}),b+=i,L){let v=(h-C.width)/2;N(S).attr("transform","translate( "+(-1*h/2+v)+", "+-1*b/2+")"),y=C.height+n}let w=(h-x.width)/2;return N(D).attr("transform","translate( "+(-1*h/2+w)+", "+(-1*b/2+y)+")"),y+=x.height+n,c.attr("class","divider").attr("x1",-h/2-s).attr("x2",h/2+s).attr("y1",-b/2-s+i+y).attr("y2",-b/2-s+i+y),y+=i,g.forEach(v=>{N(v).attr("transform","translate( "+-h/2+", "+(-1*b/2+y+i/2)+")");let o=v?.getBBox();y+=(o?.height??0)+n}),y+=i,u.attr("class","divider").attr("x1",-h/2-s).attr("x2",h/2+s).attr("y1",-b/2-s+i+y).attr("y2",-b/2-s+i+y),y+=i,f.forEach(v=>{N(v).attr("transform","translate( "+-h/2+", "+(-1*b/2+y)+")");let o=v?.getBBox();y+=(o?.height??0)+n}),l.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-s).attr("y",-(b/2)-s).attr("width",h+t.padding).attr("height",b+t.padding),B(t,l),t.intersect=function(v){return T.rect(t,v)},a},"class_box"),te={rhombus:Qt,composite:Rr,question:Qt,rect:Or,labelRect:zr,rectWithTitle:Ar,choice:Sr,circle:Fr,doublecircle:Wr,stadium:Mr,hexagon:vr,block_arrow:Er,rect_left_inv_arrow:_r,lean_right:Dr,lean_left:Nr,trapezoid:Tr,inv_trapezoid:Br,rect_right_inv_arrow:Cr,cylinder:Ir,start:Yr,end:Hr,note:Lr,subroutine:Pr,fork:$t,join:$t,class_box:Kr},ct={},fe=d(async(e,t,s)=>{let n,i;if(t.link){let r;I().securityLevel==="sandbox"?r="_top":t.linkTarget&&(r=t.linkTarget||"_blank"),n=e.insert("svg:a").attr("xlink:href",t.link).attr("target",r),i=await te[t.shape](n,t,s)}else i=await te[t.shape](e,t,s),n=i;return t.tooltip&&i.attr("title",t.tooltip),t.class&&i.attr("class","node default "+t.class),ct[t.id]=n,t.haveCallback&&ct[t.id].attr("class",ct[t.id].attr("class")+" clickable"),n},"insertNode"),Xr=d(e=>{let t=ct[e.id];k.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");let s=8,n=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+n-e.width/2)+", "+(e.y-e.height/2-s)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),n},"positionNode");function Tt(e,t,s=!1){let n=e,i="default";(n?.classes?.length||0)>0&&(i=(n?.classes??[]).join(" ")),i=i+" flowchart-label";let r=0,a="",l;switch(n.type){case"round":r=5,a="rect";break;case"composite":r=0,a="composite",l=0;break;case"square":a="rect";break;case"diamond":a="question";break;case"hexagon":a="hexagon";break;case"block_arrow":a="block_arrow";break;case"odd":a="rect_left_inv_arrow";break;case"lean_right":a="lean_right";break;case"lean_left":a="lean_left";break;case"trapezoid":a="trapezoid";break;case"inv_trapezoid":a="inv_trapezoid";break;case"rect_left_inv_arrow":a="rect_left_inv_arrow";break;case"circle":a="circle";break;case"ellipse":a="ellipse";break;case"stadium":a="stadium";break;case"subroutine":a="subroutine";break;case"cylinder":a="cylinder";break;case"group":a="rect";break;case"doublecircle":a="doublecircle";break;default:a="rect"}let c=Yt(n?.styles??[]),u=n.label,h=n.size??{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:a,labelText:u,rx:r,ry:r,class:i,style:c.style,id:n.id,directions:n.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:s,intersect:void 0,type:n.type,padding:l??$()?.block?.padding??0}}d(Tt,"getNodeFromBlock");async function be(e,t,s){let n=Tt(t,s,!1);if(n.type==="group")return;let i=$(),r=await fe(e,n,{config:i}),a=r.node().getBBox(),l=s.getBlock(n.id);l.size={width:a.width,height:a.height,x:0,y:0,node:r},s.setBlock(l),r.remove()}d(be,"calculateBlockSize");async function xe(e,t,s){let n=Tt(t,s,!0);if(s.getBlock(n.id).type!=="space"){let r=$();await fe(e,n,{config:r}),t.intersect=n?.intersect,Xr(n)}}d(xe,"insertBlockPositioned");async function gt(e,t,s,n){for(let i of t)await n(e,i,s),i.children&&await gt(e,i.children,s,n)}d(gt,"performOperations");async function ye(e,t,s){await gt(e,t,s,be)}d(ye,"calculateBlockSizes");async function me(e,t,s){await gt(e,t,s,xe)}d(me,"insertBlocks");async function we(e,t,s,n,i){let r=new Ut({multigraph:!0,compound:!0});r.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(let a of s)a.size&&r.setNode(a.id,{width:a.size.width,height:a.size.height,intersect:a.intersect});for(let a of t)if(a.start&&a.end){let l=n.getBlock(a.start),c=n.getBlock(a.end);if(l?.size&&c?.size){let u=l.size,h=c.size,b=[{x:u.x,y:u.y},{x:u.x+(h.x-u.x)/2,y:u.y+(h.y-u.y)/2},{x:h.x,y:h.y}];gr(e,{v:a.start,w:a.end,name:a.id},{...a,arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:b,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",r,i),a.label&&(await cr(e,{...a,label:a.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:b,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),or({...a,x:b[1].x,y:b[1].y},{originalPath:b}))}}}d(we,"insertEdges");var Ur=d(function(e,t){return t.db.getClasses()},"getClasses"),jr=d(async function(e,t,s,n){let{securityLevel:i,block:r}=$(),a=n.db,l;i==="sandbox"&&(l=N("#i"+t));let c=i==="sandbox"?N(l.nodes()[0].contentDocument.body):N("body"),u=i==="sandbox"?c.select(`[id="${t}"]`):N(`[id="${t}"]`);ar(u,["point","circle","cross"],n.type,t);let b=a.getBlocks(),m=a.getBlocksFlat(),y=a.getEdges(),L=u.insert("g").attr("class","block");await ye(L,b,a);let E=le(a);if(await me(L,b,a),await we(L,y,m,a,t),E){let S=E,C=Math.max(1,Math.round(.125*(S.width/S.height))),_=S.height+C+10,D=S.width+10,{useMaxWidth:x}=r;At(u,_,D,!!x),k.debug("Here Bounds",E,S),u.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}},"draw"),Vr={draw:jr,getClasses:Ur},ca={parser:ve,db:Ke,renderer:Vr,styles:Ue};export{ca as diagram};
//# sourceMappingURL=blockDiagram-ZHA2E4KO-IZKTV5IP.min.js.map
