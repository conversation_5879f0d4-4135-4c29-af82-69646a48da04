import{o as oe}from"./chunk-PYPO7LRM.min.js";import{K as Q,M as re,N as ie,Z as le,h as x,ia as B,j as se}from"./chunk-U3SD26FK.min.js";function ae(a){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];var s=Array.from(typeof a=="string"?[a]:a);s[s.length-1]=s[s.length-1].replace(/\r?\n([\t ]*)$/,"");var n=s.reduce(function(l,o){var p=o.match(/\n([\t ]+|(?!\s).)/g);return p?l.concat(p.map(function(c){var h,u;return(u=(h=c.match(/[\t ]/g))===null||h===void 0?void 0:h.length)!==null&&u!==void 0?u:0})):l},[]);if(n.length){var r=new RegExp(`
[	 ]{`+Math.min.apply(Math,n)+"}","g");s=s.map(function(l){return l.replace(r,`
`)})}s[0]=s[0].replace(/^\r?\n/,"");var i=s[0];return e.forEach(function(l,o){var p=i.match(/(?:^|\n)( *)$/),c=p?p[1]:"",h=l;typeof l=="string"&&l.includes(`
`)&&(h=String(l).split(`
`).map(function(u,f){return f===0?u:""+c+u}).join(`
`)),i+=h+s[o+1]}),i}function N(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var R=N();function ge(a){R=a}var de=/[&<>"']/,Be=new RegExp(de.source,"g"),ke=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,qe=new RegExp(ke.source,"g"),je={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ce=a=>je[a];function m(a,e){if(e){if(de.test(a))return a.replace(Be,ce)}else if(ke.test(a))return a.replace(qe,ce);return a}var Ze=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function We(a){return a.replace(Ze,(e,t)=>(t=t.toLowerCase(),t==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}var Pe=/(^|[^\[])\^/g;function k(a,e){let t=typeof a=="string"?a:a.source;e=e||"";let s={replace:(n,r)=>{let i=typeof r=="string"?r:r.source;return i=i.replace(Pe,"$1"),t=t.replace(n,i),s},getRegex:()=>new RegExp(t,e)};return s}function pe(a){try{a=encodeURI(a).replace(/%25/g,"%")}catch{return null}return a}var L={exec:()=>null};function he(a,e){let t=a.replace(/\|/g,(r,i,l)=>{let o=!1,p=i;for(;--p>=0&&l[p]==="\\";)o=!o;return o?"|":" |"}),s=t.split(/ \|/),n=0;if(s[0].trim()||s.shift(),s.length>0&&!s[s.length-1].trim()&&s.pop(),e)if(s.length>e)s.splice(e);else for(;s.length<e;)s.push("");for(;n<s.length;n++)s[n]=s[n].trim().replace(/\\\|/g,"|");return s}function I(a,e,t){let s=a.length;if(s===0)return"";let n=0;for(;n<s;){let r=a.charAt(s-n-1);if(r===e&&!t)n++;else if(r!==e&&t)n++;else break}return a.slice(0,s-n)}function Me(a,e){if(a.indexOf(e[1])===-1)return-1;let t=0;for(let s=0;s<a.length;s++)if(a[s]==="\\")s++;else if(a[s]===e[0])t++;else if(a[s]===e[1]&&(t--,t<0))return s;return-1}function ue(a,e,t,s){let n=e.href,r=e.title?m(e.title):null,i=a[1].replace(/\\([\[\]])/g,"$1");if(a[0].charAt(0)!=="!"){s.state.inLink=!0;let l={type:"link",raw:t,href:n,title:r,text:i,tokens:s.inlineTokens(i)};return s.state.inLink=!1,l}return{type:"image",raw:t,href:n,title:r,text:m(i)}}function Oe(a,e){let t=a.match(/^(\s+)(?:```)/);if(t===null)return e;let s=t[1];return e.split(`
`).map(n=>{let r=n.match(/^\s+/);if(r===null)return n;let[i]=r;return i.length>=s.length?n.slice(s.length):n}).join(`
`)}var S=class{options;rules;lexer;constructor(e){this.options=e||R}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let s=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?s:I(s,`
`)}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let s=t[0],n=Oe(s,t[3]||"");return{type:"code",raw:s,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let s=t[2].trim();if(/#$/.test(s)){let n=I(s,"#");(this.options.pedantic||!n||/ $/.test(n))&&(s=n.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:I(t[0],`
`)}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let s=I(t[0],`
`).split(`
`),n="",r="",i=[];for(;s.length>0;){let l=!1,o=[],p;for(p=0;p<s.length;p++)if(/^ {0,3}>/.test(s[p]))o.push(s[p]),l=!0;else if(!l)o.push(s[p]);else break;s=s.slice(p);let c=o.join(`
`),h=c.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,`
    $1`).replace(/^ {0,3}>[ \t]?/gm,"");n=n?`${n}
${c}`:c,r=r?`${r}
${h}`:h;let u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(h,i,!0),this.lexer.state.top=u,s.length===0)break;let f=i[i.length-1];if(f?.type==="code")break;if(f?.type==="blockquote"){let g=f,b=g.raw+`
`+s.join(`
`),w=this.blockquote(b);i[i.length-1]=w,n=n.substring(0,n.length-g.raw.length)+w.raw,r=r.substring(0,r.length-g.text.length)+w.text;break}else if(f?.type==="list"){let g=f,b=g.raw+`
`+s.join(`
`),w=this.list(b);i[i.length-1]=w,n=n.substring(0,n.length-f.raw.length)+w.raw,r=r.substring(0,r.length-g.raw.length)+w.raw,s=b.substring(i[i.length-1].raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:i,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let s=t[1].trim(),n=s.length>1,r={type:"list",raw:"",ordered:n,start:n?+s.slice(0,-1):"",loose:!1,items:[]};s=n?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=n?s:"[*+-]");let i=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`),l=!1;for(;e;){let o=!1,p="",c="";if(!(t=i.exec(e))||this.rules.block.hr.test(e))break;p=t[0],e=e.substring(p.length);let h=t[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),u=e.split(`
`,1)[0],f=!h.trim(),g=0;if(this.options.pedantic?(g=2,c=h.trimStart()):f?g=t[1].length+1:(g=t[2].search(/[^ ]/),g=g>4?1:g,c=h.slice(g),g+=t[1].length),f&&/^ *$/.test(u)&&(p+=u+`
`,e=e.substring(u.length+1),o=!0),!o){let O=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),ee=new RegExp(`^ {0,${Math.min(3,g-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),te=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:\`\`\`|~~~)`),ne=new RegExp(`^ {0,${Math.min(3,g-1)}}#`);for(;e;){let D=e.split(`
`,1)[0];if(u=D,this.options.pedantic&&(u=u.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),te.test(u)||ne.test(u)||O.test(u)||ee.test(e))break;if(u.search(/[^ ]/)>=g||!u.trim())c+=`
`+u.slice(g);else{if(f||h.search(/[^ ]/)>=4||te.test(h)||ne.test(h)||ee.test(h))break;c+=`
`+u}!f&&!u.trim()&&(f=!0),p+=D+`
`,e=e.substring(D.length+1),h=u.slice(g)}}r.loose||(l?r.loose=!0:/\n *\n *$/.test(p)&&(l=!0));let b=null,w;this.options.gfm&&(b=/^\[[ xX]\] /.exec(c),b&&(w=b[0]!=="[ ] ",c=c.replace(/^\[[ xX]\] +/,""))),r.items.push({type:"list_item",raw:p,task:!!b,checked:w,loose:!1,text:c,tokens:[]}),r.raw+=p}r.items[r.items.length-1].raw=r.items[r.items.length-1].raw.trimEnd(),r.items[r.items.length-1].text=r.items[r.items.length-1].text.trimEnd(),r.raw=r.raw.trimEnd();for(let o=0;o<r.items.length;o++)if(this.lexer.state.top=!1,r.items[o].tokens=this.lexer.blockTokens(r.items[o].text,[]),!r.loose){let p=r.items[o].tokens.filter(h=>h.type==="space"),c=p.length>0&&p.some(h=>/\n.*\n/.test(h.raw));r.loose=c}if(r.loose)for(let o=0;o<r.items.length;o++)r.items[o].loose=!0;return r}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let s=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:s,raw:t[0],href:n,title:r}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;let s=he(t[1]),n=t[2].replace(/^\||\| *$/g,"").split("|"),r=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(s.length===n.length){for(let l of n)/^ *-+: *$/.test(l)?i.align.push("right"):/^ *:-+: *$/.test(l)?i.align.push("center"):/^ *:-+ *$/.test(l)?i.align.push("left"):i.align.push(null);for(let l=0;l<s.length;l++)i.header.push({text:s[l],tokens:this.lexer.inline(s[l]),header:!0,align:i.align[l]});for(let l of r)i.rows.push(he(l,i.header.length).map((o,p)=>({text:o,tokens:this.lexer.inline(o),header:!1,align:i.align[p]})));return i}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let s=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:s,tokens:this.lexer.inline(s)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:m(t[1])}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let s=t[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;let i=I(s.slice(0,-1),"\\");if((s.length-i.length)%2===0)return}else{let i=Me(t[2],"()");if(i>-1){let o=(t[0].indexOf("!")===0?5:4)+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,o).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){let i=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);i&&(n=i[1],r=i[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(this.options.pedantic&&!/>$/.test(s)?n=n.slice(1):n=n.slice(1,-1)),ue(t,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let s;if((s=this.rules.inline.reflink.exec(e))||(s=this.rules.inline.nolink.exec(e))){let n=(s[2]||s[1]).replace(/\s+/g," "),r=t[n.toLowerCase()];if(!r){let i=s[0].charAt(0);return{type:"text",raw:i,text:i}}return ue(s,r,s[0],this.lexer)}}emStrong(e,t,s=""){let n=this.rules.inline.emStrongLDelim.exec(e);if(!n||n[3]&&s.match(/[\p{L}\p{N}]/u))return;if(!(n[1]||n[2]||"")||!s||this.rules.inline.punctuation.exec(s)){let i=[...n[0]].length-1,l,o,p=i,c=0,h=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,t=t.slice(-1*e.length+i);(n=h.exec(t))!=null;){if(l=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!l)continue;if(o=[...l].length,n[3]||n[4]){p+=o;continue}else if((n[5]||n[6])&&i%3&&!((i+o)%3)){c+=o;continue}if(p-=o,p>0)continue;o=Math.min(o,o+p+c);let u=[...n[0]][0].length,f=e.slice(0,i+n.index+u+o);if(Math.min(i,o)%2){let b=f.slice(1,-1);return{type:"em",raw:f,text:b,tokens:this.lexer.inlineTokens(b)}}let g=f.slice(2,-2);return{type:"strong",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let s=t[2].replace(/\n/g," "),n=/[^ ]/.test(s),r=/^ /.test(s)&&/ $/.test(s);return n&&r&&(s=s.substring(1,s.length-1)),s=m(s,!0),{type:"codespan",raw:t[0],text:s}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let s,n;return t[2]==="@"?(s=m(t[1]),n="mailto:"+s):(s=m(t[1]),n=s),{type:"link",raw:t[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let s,n;if(t[2]==="@")s=m(t[0]),n="mailto:"+s;else{let r;do r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(r!==t[0]);s=m(t[0]),t[1]==="www."?n="http://"+t[0]:n=t[0]}return{type:"link",raw:t[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let s;return this.lexer.state.inRawBlock?s=t[0]:s=m(t[0]),{type:"text",raw:t[0],text:s}}}},De=/^(?: *(?:\n|$))+/,Qe=/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,Fe=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,v=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,He=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,xe=/(?:[*+-]|\d{1,9}[.)])/,me=k(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,xe).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),U=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ne=/^[^\n]+/,X=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ue=k(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",X).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Xe=k(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,xe).getRegex(),Z="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",G=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Ge=k("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",G).replace("tag",Z).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),be=k(U).replace("hr",v).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Z).getRegex(),Ke=k(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",be).getRegex(),K={blockquote:Ke,code:Qe,def:Ue,fences:Fe,heading:He,hr:v,html:Ge,lheading:me,list:Xe,newline:De,paragraph:be,table:L,text:Ne},fe=k("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",v).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Z).getRegex(),Ve={...K,table:fe,paragraph:k(U).replace("hr",v).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",fe).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Z).getRegex()},Ye={...K,html:k(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",G).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:L,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:k(U).replace("hr",v).replace("heading",` *#{1,6} *[^
]`).replace("lheading",me).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},we=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Je=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,ye=/^( {2,}|\\)\n(?!\s*$)/,et=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,C="\\p{P}\\p{S}",tt=k(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,C).getRegex(),nt=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,st=k(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,C).getRegex(),rt=k("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,C).getRegex(),it=k("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,C).getRegex(),lt=k(/\\([punct])/,"gu").replace(/punct/g,C).getRegex(),ot=k(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),at=k(G).replace("(?:-->|$)","-->").getRegex(),ct=k("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",at).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),j=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,pt=k(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",j).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Te=k(/^!?\[(label)\]\[(ref)\]/).replace("label",j).replace("ref",X).getRegex(),$e=k(/^!?\[(ref)\](?:\[\])?/).replace("ref",X).getRegex(),ht=k("reflink|nolink(?!\\()","g").replace("reflink",Te).replace("nolink",$e).getRegex(),V={_backpedal:L,anyPunctuation:lt,autolink:ot,blockSkip:nt,br:ye,code:Je,del:L,emStrongLDelim:st,emStrongRDelimAst:rt,emStrongRDelimUnd:it,escape:we,link:pt,nolink:$e,punctuation:tt,reflink:Te,reflinkSearch:ht,tag:ct,text:et,url:L},ut={...V,link:k(/^!?\[(label)\]\((.*?)\)/).replace("label",j).getRegex(),reflink:k(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",j).getRegex()},F={...V,escape:k(we).replace("])","~|])").getRegex(),url:k(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ft={...F,br:k(ye).replace("{2,}","*").getRegex(),text:k(F.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},q={normal:K,gfm:Ve,pedantic:Ye},A={normal:V,gfm:F,breaks:ft,pedantic:ut},y=class a{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||R,this.options.tokenizer=this.options.tokenizer||new S,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={block:q.normal,inline:A.normal};this.options.pedantic?(t.block=q.pedantic,t.inline=A.pedantic):this.options.gfm&&(t.block=q.gfm,this.options.breaks?t.inline=A.breaks:t.inline=A.gfm),this.tokenizer.rules=t}static get rules(){return{block:q,inline:A}}static lex(e,t){return new a(t).lex(e)}static lexInline(e,t){return new a(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let s=this.inlineQueue[t];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],s=!1){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(l,o,p)=>o+"    ".repeat(p.length));let n,r,i;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>(n=l.call({lexer:this},e,t))?(e=e.substring(n.raw.length),t.push(n),!0):!1))){if(n=this.tokenizer.space(e)){e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);continue}if(n=this.tokenizer.code(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);continue}if(n=this.tokenizer.fences(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.heading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.hr(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.blockquote(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.list(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.html(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.def(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text):this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title});continue}if(n=this.tokenizer.table(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.lheading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(i=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0,o=e.slice(1),p;this.options.extensions.startBlock.forEach(c=>{p=c.call({lexer:this},o),typeof p=="number"&&p>=0&&(l=Math.min(l,p))}),l<1/0&&l>=0&&(i=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i))){r=t[t.length-1],s&&r?.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),s=i.length!==e.length,e=e.substring(n.raw.length);continue}if(n=this.tokenizer.text(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);continue}if(e){let l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let s,n,r,i=e,l,o,p;if(this.tokens.links){let c=Object.keys(this.tokens.links);if(c.length>0)for(;(l=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)c.includes(l[0].slice(l[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(l=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(l=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,l.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(o||(p=""),o=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(s=c.call({lexer:this},e,t))?(e=e.substring(s.raw.length),t.push(s),!0):!1))){if(s=this.tokenizer.escape(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.tag(e)){e=e.substring(s.raw.length),n=t[t.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):t.push(s);continue}if(s=this.tokenizer.link(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(s.raw.length),n=t[t.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):t.push(s);continue}if(s=this.tokenizer.emStrong(e,i,p)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.codespan(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.br(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.del(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.autolink(e)){e=e.substring(s.raw.length),t.push(s);continue}if(!this.state.inLink&&(s=this.tokenizer.url(e))){e=e.substring(s.raw.length),t.push(s);continue}if(r=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0,h=e.slice(1),u;this.options.extensions.startInline.forEach(f=>{u=f.call({lexer:this},h),typeof u=="number"&&u>=0&&(c=Math.min(c,u))}),c<1/0&&c>=0&&(r=e.substring(0,c+1))}if(s=this.tokenizer.inlineText(r)){e=e.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(p=s.raw.slice(-1)),o=!0,n=t[t.length-1],n&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):t.push(s);continue}if(e){let c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return t}},_=class{options;parser;constructor(e){this.options=e||R}space(e){return""}code({text:e,lang:t,escaped:s}){let n=(t||"").match(/^\S*/)?.[0],r=e.replace(/\n$/,"")+`
`;return n?'<pre><code class="language-'+m(n)+'">'+(s?r:m(r,!0))+`</code></pre>
`:"<pre><code>"+(s?r:m(r,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){let t=e.ordered,s=e.start,n="";for(let l=0;l<e.items.length;l++){let o=e.items[l];n+=this.listitem(o)}let r=t?"ol":"ul",i=t&&s!==1?' start="'+s+'"':"";return"<"+r+i+`>
`+n+"</"+r+`>
`}listitem(e){let t="";if(e.task){let s=this.checkbox({checked:!!e.checked});e.loose?e.tokens.length>0&&e.tokens[0].type==="paragraph"?(e.tokens[0].text=s+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=s+" "+e.tokens[0].tokens[0].text)):e.tokens.unshift({type:"text",raw:s+" ",text:s+" "}):t+=s+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",s="";for(let r=0;r<e.header.length;r++)s+=this.tablecell(e.header[r]);t+=this.tablerow({text:s});let n="";for(let r=0;r<e.rows.length;r++){let i=e.rows[r];s="";for(let l=0;l<i.length;l++)s+=this.tablecell(i[l]);n+=this.tablerow({text:s})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){let t=this.parser.parseInline(e.tokens),s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${e}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:s}){let n=this.parser.parseInline(s),r=pe(e);if(r===null)return n;e=r;let i='<a href="'+e+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image({href:e,title:t,text:s}){let n=pe(e);if(n===null)return s;e=n;let r=`<img src="${e}" alt="${s}"`;return t&&(r+=` title="${t}"`),r+=">",r}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):e.text}},E=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},T=class a{options;renderer;textRenderer;constructor(e){this.options=e||R,this.options.renderer=this.options.renderer||new _,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new E}static parse(e,t){return new a(t).parse(e)}static parseInline(e,t){return new a(t).parseInline(e)}parse(e,t=!0){let s="";for(let n=0;n<e.length;n++){let r=e[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){let l=r,o=this.options.extensions.renderers[l.type].call({parser:this},l);if(o!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){s+=o||"";continue}}let i=r;switch(i.type){case"space":{s+=this.renderer.space(i);continue}case"hr":{s+=this.renderer.hr(i);continue}case"heading":{s+=this.renderer.heading(i);continue}case"code":{s+=this.renderer.code(i);continue}case"table":{s+=this.renderer.table(i);continue}case"blockquote":{s+=this.renderer.blockquote(i);continue}case"list":{s+=this.renderer.list(i);continue}case"html":{s+=this.renderer.html(i);continue}case"paragraph":{s+=this.renderer.paragraph(i);continue}case"text":{let l=i,o=this.renderer.text(l);for(;n+1<e.length&&e[n+1].type==="text";)l=e[++n],o+=`
`+this.renderer.text(l);t?s+=this.renderer.paragraph({type:"paragraph",raw:o,text:o,tokens:[{type:"text",raw:o,text:o}]}):s+=o;continue}default:{let l='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return s}parseInline(e,t){t=t||this.renderer;let s="";for(let n=0;n<e.length;n++){let r=e[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){let l=this.options.extensions.renderers[r.type].call({parser:this},r);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){s+=l||"";continue}}let i=r;switch(i.type){case"escape":{s+=t.text(i);break}case"html":{s+=t.html(i);break}case"link":{s+=t.link(i);break}case"image":{s+=t.image(i);break}case"strong":{s+=t.strong(i);break}case"em":{s+=t.em(i);break}case"codespan":{s+=t.codespan(i);break}case"br":{s+=t.br(i);break}case"del":{s+=t.del(i);break}case"text":{s+=t.text(i);break}default:{let l='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return s}},z=class{options;constructor(e){this.options=e||R}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}},H=class{defaults=N();options=this.setOptions;parse=this.#e(y.lex,T.parse);parseInline=this.#e(y.lexInline,T.parseInline);Parser=T;Renderer=_;TextRenderer=E;Lexer=y;Tokenizer=S;Hooks=z;constructor(...e){this.use(...e)}walkTokens(e,t){let s=[];for(let n of e)switch(s=s.concat(t.call(this,n)),n.type){case"table":{let r=n;for(let i of r.header)s=s.concat(this.walkTokens(i.tokens,t));for(let i of r.rows)for(let l of i)s=s.concat(this.walkTokens(l.tokens,t));break}case"list":{let r=n;s=s.concat(this.walkTokens(r.items,t));break}default:{let r=n;this.defaults.extensions?.childTokens?.[r.type]?this.defaults.extensions.childTokens[r.type].forEach(i=>{let l=r[i].flat(1/0);s=s.concat(this.walkTokens(l,t))}):r.tokens&&(s=s.concat(this.walkTokens(r.tokens,t)))}}return s}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(s=>{let n={...s};if(n.async=this.defaults.async||n.async||!1,s.extensions&&(s.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){let i=t.renderers[r.name];i?t.renderers[r.name]=function(...l){let o=r.renderer.apply(this,l);return o===!1&&(o=i.apply(this,l)),o}:t.renderers[r.name]=r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let i=t[r.level];i?i.unshift(r.tokenizer):t[r.level]=[r.tokenizer],r.start&&(r.level==="block"?t.startBlock?t.startBlock.push(r.start):t.startBlock=[r.start]:r.level==="inline"&&(t.startInline?t.startInline.push(r.start):t.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(t.childTokens[r.name]=r.childTokens)}),n.extensions=t),s.renderer){let r=this.defaults.renderer||new _(this.defaults);for(let i in s.renderer){if(!(i in r))throw new Error(`renderer '${i}' does not exist`);if(["options","parser"].includes(i))continue;let l=i,o=s.renderer[l];s.useNewRenderer||(o=this.#t(o,l,r));let p=r[l];r[l]=(...c)=>{let h=o.apply(r,c);return h===!1&&(h=p.apply(r,c)),h||""}}n.renderer=r}if(s.tokenizer){let r=this.defaults.tokenizer||new S(this.defaults);for(let i in s.tokenizer){if(!(i in r))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;let l=i,o=s.tokenizer[l],p=r[l];r[l]=(...c)=>{let h=o.apply(r,c);return h===!1&&(h=p.apply(r,c)),h}}n.tokenizer=r}if(s.hooks){let r=this.defaults.hooks||new z;for(let i in s.hooks){if(!(i in r))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;let l=i,o=s.hooks[l],p=r[l];z.passThroughHooks.has(i)?r[l]=c=>{if(this.defaults.async)return Promise.resolve(o.call(r,c)).then(u=>p.call(r,u));let h=o.call(r,c);return p.call(r,h)}:r[l]=(...c)=>{let h=o.apply(r,c);return h===!1&&(h=p.apply(r,c)),h}}n.hooks=r}if(s.walkTokens){let r=this.defaults.walkTokens,i=s.walkTokens;n.walkTokens=function(l){let o=[];return o.push(i.call(this,l)),r&&(o=o.concat(r.call(this,l))),o}}this.defaults={...this.defaults,...n}}),this}#t(e,t,s){switch(t){case"heading":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,s.parser.parseInline(n.tokens),n.depth,We(s.parser.parseInline(n.tokens,s.parser.textRenderer)))};case"code":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.text,n.lang,!!n.escaped)};case"table":return function(n){if(!n.type||n.type!==t)return e.apply(this,arguments);let r="",i="";for(let o=0;o<n.header.length;o++)i+=this.tablecell({text:n.header[o].text,tokens:n.header[o].tokens,header:!0,align:n.align[o]});r+=this.tablerow({text:i});let l="";for(let o=0;o<n.rows.length;o++){let p=n.rows[o];i="";for(let c=0;c<p.length;c++)i+=this.tablecell({text:p[c].text,tokens:p[c].tokens,header:!1,align:n.align[c]});l+=this.tablerow({text:i})}return e.call(this,r,l)};case"blockquote":return function(n){if(!n.type||n.type!==t)return e.apply(this,arguments);let r=this.parser.parse(n.tokens);return e.call(this,r)};case"list":return function(n){if(!n.type||n.type!==t)return e.apply(this,arguments);let r=n.ordered,i=n.start,l=n.loose,o="";for(let p=0;p<n.items.length;p++){let c=n.items[p],h=c.checked,u=c.task,f="";if(c.task){let g=this.checkbox({checked:!!h});l?c.tokens.length>0&&c.tokens[0].type==="paragraph"?(c.tokens[0].text=g+" "+c.tokens[0].text,c.tokens[0].tokens&&c.tokens[0].tokens.length>0&&c.tokens[0].tokens[0].type==="text"&&(c.tokens[0].tokens[0].text=g+" "+c.tokens[0].tokens[0].text)):c.tokens.unshift({type:"text",text:g+" "}):f+=g+" "}f+=this.parser.parse(c.tokens,l),o+=this.listitem({type:"list_item",raw:f,text:f,task:u,checked:!!h,loose:l,tokens:c.tokens})}return e.call(this,o,r,i)};case"html":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.text,n.block)};case"paragraph":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,this.parser.parseInline(n.tokens))};case"escape":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.text)};case"link":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.href,n.title,this.parser.parseInline(n.tokens))};case"image":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.href,n.title,n.text)};case"strong":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,this.parser.parseInline(n.tokens))};case"em":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,this.parser.parseInline(n.tokens))};case"codespan":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.text)};case"del":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,this.parser.parseInline(n.tokens))};case"text":return function(n){return!n.type||n.type!==t?e.apply(this,arguments):e.call(this,n.text)}}return e}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return y.lex(e,t??this.defaults)}parser(e,t){return T.parse(e,t??this.defaults)}#e(e,t){return(s,n)=>{let r={...n},i={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);let l=this.#n(!!i.silent,!!i.async);if(typeof s>"u"||s===null)return l(new Error("marked(): input parameter is undefined or null"));if(typeof s!="string")return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(s)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(s):s).then(o=>e(o,i)).then(o=>i.hooks?i.hooks.processAllTokens(o):o).then(o=>i.walkTokens?Promise.all(this.walkTokens(o,i.walkTokens)).then(()=>o):o).then(o=>t(o,i)).then(o=>i.hooks?i.hooks.postprocess(o):o).catch(l);try{i.hooks&&(s=i.hooks.preprocess(s));let o=e(s,i);i.hooks&&(o=i.hooks.processAllTokens(o)),i.walkTokens&&this.walkTokens(o,i.walkTokens);let p=t(o,i);return i.hooks&&(p=i.hooks.postprocess(p)),p}catch(o){return l(o)}}}#n(e,t){return s=>{if(s.message+=`
Please report this to https://github.com/markedjs/marked.`,e){let n="<p>An error occurred:</p><pre>"+m(s.message+"",!0)+"</pre>";return t?Promise.resolve(n):n}if(t)return Promise.reject(s);throw s}}},$=new H;function d(a,e){return $.parse(a,e)}d.options=d.setOptions=function(a){return $.setOptions(a),d.defaults=$.defaults,ge(d.defaults),d};d.getDefaults=N;d.defaults=R;d.use=function(...a){return $.use(...a),d.defaults=$.defaults,ge(d.defaults),d};d.walkTokens=function(a,e){return $.walkTokens(a,e)};d.parseInline=$.parseInline;d.Parser=T;d.parser=T.parse;d.Renderer=_;d.TextRenderer=E;d.Lexer=y;d.lexer=y.lex;d.Tokenizer=S;d.Hooks=z;d.parse=d;var kt=d.options,xt=d.setOptions,mt=d.use,bt=d.walkTokens,wt=d.parseInline;var yt=T.parse,Tt=y.lex;function Re(a,{markdownAutoWrap:e}){let s=a.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),n=ae(s);return e===!1?n.replace(/ /g,"&nbsp;"):n}x(Re,"preprocessMarkdown");function ze(a,e={}){let t=Re(a,e),s=d.lexer(t),n=[[]],r=0;function i(l,o="normal"){l.type==="text"?l.text.split(`
`).forEach((c,h)=>{h!==0&&(r++,n.push([])),c.split(" ").forEach(u=>{u=u.replace(/&#39;/g,"'"),u&&n[r].push({content:u,type:o})})}):l.type==="strong"||l.type==="em"?l.tokens.forEach(p=>{i(p,l.type)}):l.type==="html"&&n[r].push({content:l.text,type:"normal"})}return x(i,"processNode"),s.forEach(l=>{l.type==="paragraph"?l.tokens?.forEach(o=>{i(o)}):l.type==="html"&&n[r].push({content:l.text,type:"normal"})}),n}x(ze,"markdownToLines");function Se(a,{markdownAutoWrap:e}={}){let t=d.lexer(a);function s(n){return n.type==="text"?e===!1?n.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):n.text.replace(/\n */g,"<br/>"):n.type==="strong"?`<strong>${n.tokens?.map(s).join("")}</strong>`:n.type==="em"?`<em>${n.tokens?.map(s).join("")}</em>`:n.type==="paragraph"?`<p>${n.tokens?.map(s).join("")}</p>`:n.type==="space"?"":n.type==="html"?`${n.text}`:n.type==="escape"?n.text:`Unsupported markdown: ${n.type}`}return x(s,"output"),t.map(s).join("")}x(Se,"markdownToHTML");function _e(a){return Intl.Segmenter?[...new Intl.Segmenter().segment(a)].map(e=>e.segment):[...a]}x(_e,"splitTextToChars");function Ie(a,e){let t=_e(e.content);return J(a,[],t,e.type)}x(Ie,"splitWordToFitWidth");function J(a,e,t,s){if(t.length===0)return[{content:e.join(""),type:s},{content:"",type:s}];let[n,...r]=t,i=[...e,n];return a([{content:i.join(""),type:s}])?J(a,i,r,s):(e.length===0&&n&&(e.push(n),t.shift()),[{content:e.join(""),type:s},{content:t.join(""),type:s}])}x(J,"splitWordToFitWidthRecursion");function Ae(a,e){if(a.some(({content:t})=>t.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return W(a,e)}x(Ae,"splitLineToFitWidth");function W(a,e,t=[],s=[]){if(a.length===0)return s.length>0&&t.push(s),t.length>0?t:[];let n="";a[0].content===" "&&(n=" ",a.shift());let r=a.shift()??{content:" ",type:"normal"},i=[...s];if(n!==""&&i.push({content:n,type:"normal"}),i.push(r),e(i))return W(a,e,t,i);if(s.length>0)t.push(s),a.unshift(r);else if(r.content){let[l,o]=Ie(e,r);t.push([l]),o.content&&a.unshift(o)}return W(a,e,t)}x(W,"splitLineToFitWidthRecursion");function Y(a,e){e&&a.attr("style",e)}x(Y,"applyStyle");async function Le(a,e,t,s,n=!1){let r=a.append("foreignObject");r.attr("width",`${10*t}px`),r.attr("height",`${10*t}px`);let i=r.append("xhtml:div"),l=e.label;e.label&&Q(e.label)&&(l=await re(e.label.replace(ie.lineBreakRegex,`
`),le()));let o=e.isNode?"nodeLabel":"edgeLabel",p=i.append("span");p.html(l),Y(p,e.labelStyle),p.attr("class",`${o} ${s}`),Y(i,e.labelStyle),i.style("display","table-cell"),i.style("white-space","nowrap"),i.style("line-height","1.5"),i.style("max-width",t+"px"),i.style("text-align","center"),i.attr("xmlns","http://www.w3.org/1999/xhtml"),n&&i.attr("class","labelBkg");let c=i.node().getBoundingClientRect();return c.width===t&&(i.style("display","table"),i.style("white-space","break-spaces"),i.style("width",t+"px"),c=i.node().getBoundingClientRect()),r.node()}x(Le,"addHtmlSpan");function P(a,e,t){return a.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",e*t-.1+"em").attr("dy",t+"em")}x(P,"createTspan");function Ee(a,e,t){let s=a.append("text"),n=P(s,1,e);M(n,t);let r=n.node().getComputedTextLength();return s.remove(),r}x(Ee,"computeWidthOfText");function gt(a,e,t){let s=a.append("text"),n=P(s,1,e);M(n,[{content:t,type:"normal"}]);let r=n.node()?.getBoundingClientRect();return r&&s.remove(),r}x(gt,"computeDimensionOfText");function ve(a,e,t,s=!1){let r=e.append("g"),i=r.insert("rect").attr("class","background").attr("style","stroke: none"),l=r.append("text").attr("y","-10.1"),o=0;for(let p of t){let c=x(u=>Ee(r,1.1,u)<=a,"checkWidth"),h=c(p)?[p]:Ae(p,c);for(let u of h){let f=P(l,o,1.1);M(f,u),o++}}if(s){let p=l.node().getBBox(),c=2;return i.attr("x",p.x-c).attr("y",p.y-c).attr("width",p.width+2*c).attr("height",p.height+2*c),r.node()}else return l.node()}x(ve,"createFormattedText");function M(a,e){a.text(""),e.forEach((t,s)=>{let n=a.append("tspan").attr("font-style",t.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",t.type==="strong"?"bold":"normal");s===0?n.text(t.content):n.text(" "+t.content)})}x(M,"updateTextContentAndStyles");function Ce(a){return a.replace(/fa[bklrs]?:fa-[\w-]+/g,e=>`<i class='${e.replace(":"," ")}'></i>`)}x(Ce,"replaceIconSubstring");var At=x(async(a,e="",{style:t="",isTitle:s=!1,classes:n="",useHtmlLabels:r=!0,isNode:i=!0,width:l=200,addSvgBackground:o=!1}={},p)=>{if(se.debug("XYZ createText",e,t,s,n,r,i,"addSvgBackground: ",o),r){let c=Se(e,p),h=Ce(oe(c)),u=e.replace(/\\\\/g,"\\"),f={isNode:i,label:Q(e)?u:h,labelStyle:t.replace("fill:","color:")};return await Le(a,f,l,n,o)}else{let c=e.replace(/<br\s*\/?>/g,"<br/>"),h=ze(c.replace("<br>","<br/>"),p),u=ve(l,a,h,e?o:!1);if(i){/stroke:/.exec(t)&&(t=t.replace("stroke:","lineColor:"));let f=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");B(u).attr("style",f)}else{let f=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");B(u).select("rect").attr("style",f.replace(/background:/g,"fill:"));let g=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");B(u).select("text").attr("style",g)}return u}},"createText");export{ae as a,gt as b,Ce as c,At as d};
//# sourceMappingURL=chunk-AUO2PXKS.min.js.map
