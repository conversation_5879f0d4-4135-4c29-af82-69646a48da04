﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
        
        <ApplicationId>com.drawnui.tutorials</ApplicationId>
        <ApplicationIdGuid>430e66b0-efc2-4fde-9ac8-074d5c3299b4</ApplicationIdGuid>
        <ApplicationTitle>DrawnUI Tutorials</ApplicationTitle>
        <OutputType>Exe</OutputType>
        <RootNamespace>DrawnUI.Tutorials</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>

        <!--<UseSource>true</UseSource>-->

    </PropertyGroup>

	<PropertyGroup>
        <WindowsPackageType>None</WindowsPackageType>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
        <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
        <AssemblyName>$(MSBuildProjectName)</AssemblyName>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net*-ios|AnyCPU'">
	  <CreatePackage>false</CreatePackage>
        <CodesignKey>iPhone Developer</CodesignKey>
	</PropertyGroup>

  <ItemGroup>
	  <MauiImage Include="Resources\Raw\Svg\dotnet_bot.svg">
	    <BaseSize>168,208</BaseSize>
	  </MauiImage>
	</ItemGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <Optimize>True</Optimize>
    <AndroidEnableSGenConcurrent>True</AndroidEnableSGenConcurrent>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net*-android|AnyCPU'">
      <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net*-android|AnyCPU'">
    <AndroidEnableMultiDex>True</AndroidEnableMultiDex>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(TargetFramework.Contains('ios')) == true">
    <ProvisioningType>manual</ProvisioningType>
  </PropertyGroup>

  <ItemGroup>
    <!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

    <!-- Splash Screen -->
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

    <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

    <ItemGroup Condition="'$(UseSource)' != 'true'">
        <PackageReference Include="DrawnUi.Maui" Version="1.6.2.6" />
    </ItemGroup>

    <ItemGroup>
        <!--https://github.com/BretJohnson/hot-preview-->
        <PackageReference Include="HotPreview.App.Maui" Version="0.12.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
        <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    </ItemGroup>

    <ItemGroup Condition="'$(UseSource)' == 'true'">
      <ProjectReference Include="..\..\DrawnUi\DrawnUi.Maui.csproj" />
    </ItemGroup>

 
</Project>